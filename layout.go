package main

import (
	"github.com/muesli/gamut"

	"github.com/charmbracelet/lipgloss"
)

// @see: https://www.ditig.com/256-colors-cheat-sheet
var (
	normal         = lipgloss.Color("#EDEDED")
	subtle         = lipgloss.AdaptiveColor{Light: "#D9DCCF", Dark: "#383838"}
	highlight      = lipgloss.AdaptiveColor{Light: "#874BFD", Dark: "#7D56F4"}
	special        = lipgloss.AdaptiveColor{Light: "#43BF6D", Dark: "#73F59F"}
	c1             = lipgloss.Color("#F25D94")
	c2             = lipgloss.Color("#EDFF82")
	blends         = gamut.Blends(c1, c2, 8)
	grey27         = lipgloss.Color("#444444")
	grey23         = lipgloss.Color("#3a3a3a")
	grey15         = lipgloss.Color("#262626")
	cornflowerBlue = lipgloss.Color("#5f87ff")

	base = lipgloss.NewStyle().Foreground(normal)

	divider = lipgloss.NewStyle().
		SetString("•").
		Padding(0, 1).
		Foreground(subtle).
		String()

	url = lipgloss.NewStyle().Foreground(special).Render
)

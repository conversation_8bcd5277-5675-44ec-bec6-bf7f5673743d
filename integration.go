package main

import (
	"fmt"
	"log"
	"os"

	"eionz.com/demo/pkg/app"
	"eionz.com/demo/pkg/loader"
	tea "github.com/charmbracelet/bubbletea"
)

// IntegratedModel wraps our app model with the existing UI
type IntegratedModel struct {
	// Original panel
	panel *Panel
	
	// App integration
	coordinator         *app.AppCoordinator
	terminalIntegration *app.TerminalIntegration
	
	// State
	width  int
	height int
}

// NewIntegratedModel creates a model that integrates the app layer with existing UI
func NewIntegratedModel(loader *LoaderAdapter) (*IntegratedModel, error) {
	// Create coordinator
	coordinator, err := app.NewAppCoordinator("./config")
	if err != nil {
		return nil, fmt.Errorf("failed to create coordinator: %w", err)
	}
	
	// Create terminal integration
	terminalIntegration := app.NewTerminalIntegration(coordinator)
	
	// Create original panel
	panel := NewPanel(loader)
	
	return &IntegratedModel{
		panel:               panel,
		coordinator:         coordinator,
		terminalIntegration: terminalIntegration,
	}, nil
}

// Init initializes the integrated model
func (m *IntegratedModel) Init() tea.Cmd {
	// Start the coordinator
	if err := m.coordinator.Start(); err != nil {
		log.Printf("Failed to start coordinator: %v", err)
	}
	
	// Initialize original panel
	return m.panel.Init()
}

// Update handles updates for the integrated model
func (m *IntegratedModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd
	
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		
		// Update panel
		cmd := m.panel.WindowSize(msg)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
		
	case app.TerminalOutput:
		// Handle terminal output by updating panel content
		m.terminalIntegration.AddOutput(msg.Text, msg.Source)
		
		// Update panel with terminal output
		output := m.terminalIntegration.GetOutput()
		if len(output) > 0 {
			// Show recent output in panel
			recentOutput := ""
			start := len(output) - 10 // Show last 10 lines
			if start < 0 {
				start = 0
			}
			for i := start; i < len(output); i++ {
				recentOutput += output[i] + "\n"
			}
			
			// Get existing content and append terminal output
			existingContent := ""
			if m.panel.loader != nil {
				existingContent = m.panel.loader.Content()
			}
			
			newContent := existingContent + "\n\n=== Terminal Output ===\n" + recentOutput
			m.panel.SetContent(newContent)
		}
		
	case tea.KeyMsg:
		// Handle special keys for terminal integration
		switch msg.String() {
		case ":":
			// Enter command mode
			cmd := m.terminalIntegration.Update(msg)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			return m, tea.Batch(cmds...)
			
		case "escape":
			// Exit command mode or pass to panel
			if m.terminalIntegration.IsCommandMode() {
				cmd := m.terminalIntegration.Update(msg)
				if cmd != nil {
					cmds = append(cmds, cmd)
				}
				return m, tea.Batch(cmds...)
			}
			
		case "f1":
			// Toggle help
			cmd := m.terminalIntegration.Update(msg)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			
			// Show help in main panel
			if m.terminalIntegration.ShouldShowHelp() {
				helpText := m.terminalIntegration.GetHelpText()
				m.panel.SetContent(helpText)
			}
			return m, tea.Batch(cmds...)
			
		case "f2":
			// Toggle status
			cmd := m.terminalIntegration.Update(msg)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			
			// Show status in main panel
			if m.terminalIntegration.ShouldShowStatus() {
				statusText := m.terminalIntegration.GetStatusText()
				m.panel.SetContent(statusText)
			}
			return m, tea.Batch(cmds...)
		}
		
		// If in command mode, let terminal integration handle it
		if m.terminalIntegration.IsCommandMode() {
			cmd := m.terminalIntegration.Update(msg)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			return m, tea.Batch(cmds...)
		}
		
		// Otherwise, pass to panel
		_, cmd := m.panel.Update(msg)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
		
	default:
		// Handle terminal integration updates
		cmd := m.terminalIntegration.Update(msg)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
		
		// Update panel
		_, cmd = m.panel.Update(msg)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
	}
	
	return m, tea.Batch(cmds...)
}

// View renders the integrated model
func (m *IntegratedModel) View() string {
	baseView := m.panel.View()
	
	// Add command line if in command mode
	if m.terminalIntegration.IsCommandMode() {
		return m.renderWithCommandLine(baseView)
	}
	
	return baseView
}

// renderWithCommandLine adds a command line at the bottom
func (m *IntegratedModel) renderWithCommandLine(baseView string) string {
	commandLine := m.terminalIntegration.GetCommandLine()
	
	// Add command line to the bottom of the view
	return baseView + "\n" + commandLine
}

// UpdatedMain shows how to use the integrated model
func UpdatedMain() {
	// Initialize configuration
	if err := InitConfig(); err != nil {
		log.Fatalf("Failed to initialize configuration: %v", err)
	}

	// Initialize state management
	if err := InitStateManager(); err != nil {
		log.Fatalf("Failed to initialize state manager: %v", err)
	}

	// Initialize key bindings after configuration is loaded
	InitKeyMap()

	// Create a content loader
	var contentLoader loader.ContentLoader
	var loaderID string

	f, err := initLogger("debug.log", os.Getenv("DEBUG"))
	if err != nil {
		log.Fatal(err)
	}
	defer f()

	// Check if a file path was provided as an argument
	if len(os.Args) > 1 {
		// Use file loader if a file path was provided
		filePath := os.Args[1]
		contentLoader = loader.NewFileContentLoader(filePath)
		loaderID = "file-" + filePath
	} else {
		// Try to load test-content.txt first
		if _, err := os.Stat("test-content.txt"); err == nil {
			contentLoader = loader.NewFileContentLoader("test-content.txt")
			loaderID = "file-test-content.txt"
		} else if _, err := os.Stat("sample.txt"); err == nil {
			// Fall back to sample.txt if it exists
			contentLoader = loader.NewFileContentLoader("sample.txt")
			loaderID = "file-sample.txt"
		} else {
			// Fallback to string loader with app integration info
			contentLoader = loader.NewStringContentLoader(`TermiLLM - Terminal Application with Plugin Architecture

This terminal application now includes:
• Plugin system with hot-reloading
• Configuration management with validation
• Command execution system
• Event sourcing architecture

Commands:
Press ':' to enter command mode, then try:
• plugin.list - List loaded plugins
• system.status - Show system status
• system.help - Show all commands

Press F1 for help, F2 for status
Press Escape to exit command mode`)
			loaderID = "string-default"
		}
	}

	// Create an adapter for the loader
	loaderAdapter := NewLoaderAdapter(contentLoader, loaderID)

	// Create integrated model
	model, err := NewIntegratedModel(loaderAdapter)
	if err != nil {
		log.Fatalf("Failed to create integrated model: %v", err)
	}

	p := tea.NewProgram(
		model,
		tea.WithAltScreen(),      // use the full size of the terminal in its "alternate screen buffer"
		tea.WithMouseAllMotion(), // turn on mouse support so we can track the mouse wheel
	)

	if _, err := p.Run(); err != nil {
		fmt.Println("could not run program:", err)
		os.Exit(1)
	}

	// Save state before exiting
	if GlobalStateManager != nil {
		GlobalStateManager.Save()
	}
	
	// Stop coordinator
	if err := model.coordinator.Stop(); err != nil {
		log.Printf("Failed to stop coordinator: %v", err)
	}
}
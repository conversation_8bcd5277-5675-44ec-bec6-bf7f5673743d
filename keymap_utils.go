package main

import (
	"fmt"
	"reflect"

	"eionz.com/demo/internal/styledkey"
)

// GetAllBindings returns all key bindings in the KeyMap as a map of field name to binding
func GetAllBindings(keyMap KeyMap) map[string]styledkey.Binding {
	bindings := make(map[string]styledkey.Binding)

	// Use reflection to get all fields in the KeyMap struct
	val := reflect.ValueOf(keyMap)
	typ := val.Type()

	// Iterate through all fields
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldName := typ.Field(i).Name

		// Check if the field is a styledkey.Binding
		if binding, ok := field.Interface().(styledkey.Binding); ok {
			bindings[fieldName] = binding
		}
	}

	return bindings
}

// PrintAllBindings prints information about all key bindings in the KeyMap
func PrintAllBindings(keyMap KeyMap) {
	bindings := GetAllBindings(keyMap)

	fmt.Println("All Key Bindings:")
	for name, binding := range bindings {
		keys := binding.Binding.Keys()
		help := binding.Binding.Help()

		fmt.Printf("- %s:\n", name)
		fmt.Printf("  Keys: %v\n", keys)
		fmt.Printf("  Help: %s (%s)\n", help.Key, help.Desc)
		fmt.Printf("  Styled Help: %s\n", binding.StyledHelp())
		fmt.Println()
	}
}

// GetBindingByName returns a specific binding by its field name
func GetBindingByName(keyMap KeyMap, name string) (styledkey.Binding, bool) {
	val := reflect.ValueOf(keyMap)
	field := val.FieldByName(name)

	if !field.IsValid() {
		return styledkey.Binding{}, false
	}

	if binding, ok := field.Interface().(styledkey.Binding); ok {
		return binding, true
	}

	return styledkey.Binding{}, false
}

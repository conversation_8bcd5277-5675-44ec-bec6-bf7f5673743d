package main

import (
	"strings"
	
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

type model struct {
	panel       *Panel
	commandMode *CommandMode
}

// Initialization is handled by the Panel

func NewModel(loader *LoaderAdapter) model {
	return model{
		panel:       NewPanel(loader),
		commandMode: NewCommandMode(),
	}
}

func (m model) Init() tea.Cmd {
	return tea.Batch(
		tea.SetWindowTitle("TermiLLM"),
		m.panel.Init(),
	)
}

func (m model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var (
		cmds []tea.Cmd
	)

	switch msg := msg.(type) {
	case tea.KeyMsg:
		// Handle command mode first
		if m.commandMode.IsActive() {
			_, cmd := m.commandMode.Update(msg)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
			return m, tea.Batch(cmds...)
		}
		
		// Global key bindings when not in command mode
		if DefaultKeyMap.MatchQuit(msg) {
			return m, tea.Quit
		}
		if DefaultKeyMap.MatchCommandMode(msg) {
			m.commandMode.Activate()
			return m, nil
		}
		if DefaultKeyMap.MatchToggleChatMode(msg) {
			m.panel.ToggleChatMode()
			return m, nil
		}
		if DefaultKeyMap.MatchToggleSidebar(msg) {
			m.panel.ToggleSidebar()
		}
		if DefaultKeyMap.MatchToggleSidebarWidth(msg) {
			m.panel.ToggleSidebarWidth()
		}
		if DefaultKeyMap.MatchFocusSidebar(msg) {
			m.panel.FocusSidebar()
		}
		if DefaultKeyMap.MatchFocusMain(msg) {
			m.panel.FocusMain()
		}

	case tea.WindowSizeMsg:
		// Update the panel size and get any initialization commands
		cmd := m.panel.WindowSize(msg)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

	case panelInitializedMsg:
		// Forward initialization messages to the panel
		_, cmd := m.panel.Update(msg)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}

	default:
		// Handle all other messages through the panel
		var panelCmd tea.Cmd
		_, panelCmd = m.panel.Update(msg)
		if panelCmd != nil {
			cmds = append(cmds, panelCmd)
		}
	}

	return m, tea.Batch(cmds...)
}

func (m model) View() string {
	// Always get the base panel view first
	panelView := m.panel.View()
	
	if m.commandMode.IsActive() {
		// Calculate chat window width (main content area width)
		sidebarWidth := 0
		if m.panel.sidebar != nil && m.panel.sidebar.visible {
			sidebarWidth = m.panel.sidebar.width + 1 // +1 for resize indicator
		}
		chatWidth := m.panel.width - sidebarWidth
		
		// Adjust command input width based on chat window space
		m.commandMode.AdjustWidth(chatWidth)
		
		// Create a chat-width floating overlay that doesn't affect content layout
		commandView := m.commandMode.View()
		
		// Create command overlay with chat window width
		commandOverlay := lipgloss.NewStyle().
			Foreground(lipgloss.Color("15")).
			Width(chatWidth).
			Render(commandView)
		
		// Convert panel view to lines for overlay positioning
		panelLines := strings.Split(panelView, "\n")
		overlayLines := strings.Split(commandOverlay, "\n")
		
		// Calculate overlay position (above footer bar)
		overlayHeight := len(overlayLines)
		overlayStartY := len(panelLines) - 2 - overlayHeight // Above footer bar
		
		// Ensure we don't go too high
		if overlayStartY < 2 {
			overlayStartY = 2
		}
		
		// Create result with overlay - replace chat area with overlay content
		result := make([]string, len(panelLines))
		copy(result, panelLines)
		
		// Replace lines with overlay content positioned over chat area
		for i, overlayLine := range overlayLines {
			lineIndex := overlayStartY + i
			if lineIndex < len(result) && lineIndex >= 0 {
				// Get the original line
				originalLine := result[lineIndex]
				
				// If original line is shorter than sidebar width, pad it
				if len(originalLine) < sidebarWidth {
					originalLine += strings.Repeat(" ", sidebarWidth-len(originalLine))
				}
				
				// Keep sidebar area (first sidebarWidth characters) and replace chat area
				sidebarPart := ""
				if len(originalLine) >= sidebarWidth {
					sidebarPart = originalLine[:sidebarWidth]
				}
				
				// Combine sidebar part with overlay
				result[lineIndex] = sidebarPart + overlayLine
			}
		}
		
		return strings.Join(result, "\n")
	}
	
	return panelView
}

package main

// This file contains the adapter that bridges between the independent loader package
// and the Bubbletea UI framework. It converts the channel-based API of the loader
// to the command-based API of Bubbletea.

import (
	"context"

	"eionz.com/demo/pkg/loader"
	tea "github.com/charmbracelet/bubbletea"
)

// ContentLoadedMsg is sent when content has been loaded
type ContentLoadedMsg struct {
	LoaderID string
}

// LoaderAdapter adapts a loader.ContentLoader to work with Bubbletea
type LoaderAdapter struct {
	Loader loader.ContentLoader
	ID     string
}

// NewLoaderAdapter creates a new adapter for a loader.ContentLoader
func NewLoaderAdapter(l loader.ContentLoader, id string) *LoaderAdapter {
	return &LoaderAdapter{
		Loader: l,
		ID:     id,
	}
}

// Load initiates the content loading process and returns a tea.Cmd
func (a *LoaderAdapter) Load() tea.Cmd {
	return func() tea.Msg {
		// Create a context that could be cancelled if needed
		ctx := context.Background()

		// Start the loading process
		resultCh := a.Loader.Load(ctx)

		// Wait for the result to be processed
		<-resultCh

		// Return a ContentLoadedMsg
		return ContentLoadedMsg{
			LoaderID: a.ID,
		}
	}
}

// IsLoading returns whether content is currently being loaded
func (a *LoaderAdapter) IsLoading() bool {
	return a.Loader.IsLoading()
}

// Error returns any error that occurred during loading
func (a *LoaderAdapter) Error() error {
	return a.Loader.Error()
}

// Content returns the loaded content
func (a *LoaderAdapter) Content() string {
	return a.Loader.Content()
}

// Reset clears any errors and resets the loading state
func (a *LoaderAdapter) Reset() {
	a.Loader.Reset()
}

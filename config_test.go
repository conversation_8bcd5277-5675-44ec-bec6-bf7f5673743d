package main

import (
	"os"
	"testing"
)

func TestConfigLoading(t *testing.T) {
	// Test that configuration loads without error
	config, err := LoadConfig()
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Test that required fields are set
	if config.UI.Title == "" {
		t.Error("Title should not be empty")
	}

	if config.Sidebar.MinWidth <= 0 {
		t.<PERSON>rror("Sidebar min width should be positive")
	}

	if len(config.Sidebar.Items) == 0 {
		t.<PERSON>rror("Sidebar should have items")
	}
}

func TestConfigValidation(t *testing.T) {
	// Test invalid configuration
	config := DefaultConfig()
	config.Sidebar.MinWidth = 5 // Too small

	err := validateConfig(config)
	if err == nil {
		t.Error("Expected validation error for min_width < 10")
	}

	// Test invalid max width
	config = DefaultConfig()
	config.Sidebar.MaxWidth = 20
	config.Sidebar.MinWidth = 30

	err = validateConfig(config)
	if err == nil {
		t.Error("Expected validation error for max_width < min_width")
	}
}

func TestEnvironmentOverrides(t *testing.T) {
	// Set environment variables
	os.Setenv("TERMILLM_TITLE", "Test Title")
	os.Setenv("TERMILLM_DEBUG", "true")
	os.Setenv("TERMILLM_PRIMARY_COLOR", "#ff0000")
	defer os.Unsetenv("TERMILLM_TITLE")
	defer os.Unsetenv("TERMILLM_DEBUG")
	defer os.Unsetenv("TERMILLM_PRIMARY_COLOR")

	config := DefaultConfig()
	loadConfigFromEnv(config)

	if config.UI.Title != "Test Title" {
		t.Errorf("Expected title 'Test Title', got '%s'", config.UI.Title)
	}

	if !config.UI.ShowDebug {
		t.Error("Expected debug to be true")
	}

	if config.Colors.Primary != "#ff0000" {
		t.Errorf("Expected primary color '#ff0000', got '%s'", config.Colors.Primary)
	}
}

func TestGetColor(t *testing.T) {
	// Initialize a test config
	AppConfig = DefaultConfig()

	color := GetColor("primary")
	if string(color) != AppConfig.Colors.Primary {
		t.Errorf("Expected color %s, got %s", AppConfig.Colors.Primary, color)
	}

	// Test fallback for unknown color
	unknownColor := GetColor("unknown")
	if string(unknownColor) != "15" {
		t.Errorf("Expected fallback color '15', got '%s'", unknownColor)
	}
}

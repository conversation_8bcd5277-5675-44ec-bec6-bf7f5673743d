package main

import (
	"fmt"
	"log"
	"net/http"
	"time"
)

// User represents a user in our system
type User struct {
	ID       int64     `json:"id"`
	Name     string    `json:"name"`
	Email    string    `json:"email"`
	Created  time.Time `json:"created"`
	Active   bool      `json:"active"`
}

// UserService handles user operations
type UserService struct {
	users map[int64]*User
}

// NewUserService creates a new user service
func NewUserService() *UserService {
	return &UserService{
		users: make(map[int64]*User),
	}
}

// C<PERSON><PERSON><PERSON> creates a new user
func (us *UserService) CreateUser(name, email string) (*User, error) {
	if name == "" {
		return nil, fmt.Errorf("name cannot be empty")
	}
	
	if email == "" {
		return nil, fmt.Errorf("email cannot be empty")
	}
	
	user := &User{
		ID:      int64(len(us.users) + 1),
		Name:    name,
		Email:   email,
		Created: time.Now(),
		Active:  true,
	}
	
	us.users[user.ID] = user
	return user, nil
}

// GetUser retrieves a user by ID
func (us *UserService) GetUser(id int64) (*User, bool) {
	user, exists := us.users[id]
	return user, exists
}

// ListUsers returns all users
func (us *UserService) ListUsers() []*User {
	users := make([]*User, 0, len(us.users))
	for _, user := range us.users {
		users = append(users, user)
	}
	return users
}

// HTTP handler for user operations
func (us *UserService) HandleUsers(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case "GET":
		// List all users or get specific user
		us.handleGetUsers(w, r)
	case "POST":
		// Create new user
		us.handleCreateUser(w, r)
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

func (us *UserService) handleGetUsers(w http.ResponseWriter, r *http.Request) {
	// Implementation here...
	fmt.Fprintf(w, "Getting users")
}

func (us *UserService) handleCreateUser(w http.ResponseWriter, r *http.Request) {
	// Implementation here...
	fmt.Fprintf(w, "Creating user")
}

func main() {
	userService := NewUserService()
	
	// Create some test users
	user1, err := userService.CreateUser("Alice Johnson", "<EMAIL>")
	if err \!= nil {
		log.Fatalf("Failed to create user: %v", err)
	}
	
	user2, err := userService.CreateUser("Bob Smith", "<EMAIL>")
	if err \!= nil {
		log.Fatalf("Failed to create user: %v", err)
	}
	
	// Print users
	fmt.Printf("Created user: %+v\n", user1)
	fmt.Printf("Created user: %+v\n", user2)
	
	// List all users
	allUsers := userService.ListUsers()
	fmt.Printf("Total users: %d\n", len(allUsers))
	
	// Start HTTP server
	http.HandleFunc("/users", userService.HandleUsers)
	
	fmt.Println("Server starting on :8080...")
	if err := http.ListenAndServe(":8080", nil); err \!= nil {
		log.Fatalf("Server failed: %v", err)
	}
}
EOF < /dev/null
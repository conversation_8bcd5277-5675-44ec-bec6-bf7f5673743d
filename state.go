package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// StateManager defines the interface for state management
type StateManager interface {
	GetState() interface{}
	SetState(state interface{}) error
	Subscribe(observer StateObserver) func()
	Validate() error
	Save() error
	Load() error
}

// StateObserver receives notifications when state changes
type StateObserver interface {
	OnStateChanged(oldState, newState interface{})
}

// StateChangeEvent represents a state change
type StateChangeEvent struct {
	Timestamp time.Time
	OldState  interface{}
	NewState  interface{}
	Source    string
}

// ComponentState represents the state of a UI component
type ComponentState interface {
	GetID() string
	GetType() string
	Clone() ComponentState
	Merge(other ComponentState) error
}

// AppState represents the global application state
type AppState struct {
	Version      string                 `json:"version"`
	LastSaved    time.Time              `json:"last_saved"`
	UI           UIState                `json:"ui"`
	Panel        *PanelState            `json:"panel"`
	Sidebar      *SidebarState          `json:"sidebar"`
	Content      *ContentState          `json:"content"`
	CustomData   map[string]interface{} `json:"custom_data"`
	mu           sync.RWMutex           `json:"-"`
	observers    []StateObserver        `json:"-"`
	stateFile    string                 `json:"-"`
	autoSave     bool                   `json:"-"`
	autoSaveFunc func()                 `json:"-"`
}

// UIState represents UI-specific state
type UIState struct {
	WindowWidth     int         `json:"window_width"`
	WindowHeight    int         `json:"window_height"`
	FocusState      FocusState  `json:"focus_state"`
	Theme           string      `json:"theme"`
	LastInteraction time.Time   `json:"last_interaction"`
}

// PanelState represents Panel component state
type PanelState struct {
	ID           string    `json:"id"`
	Ready        bool      `json:"ready"`
	Width        int       `json:"width"`
	Height       int       `json:"height"`
	LoaderID     string    `json:"loader_id"`
	LastUpdated  time.Time `json:"last_updated"`
}

// SidebarState represents Sidebar component state
type SidebarState struct {
	ID            string    `json:"id"`
	Visible       bool      `json:"visible"`
	Focused       bool      `json:"focused"`
	Width         int       `json:"width"`
	Height        int       `json:"height"`
	SelectedIndex int       `json:"selected_index"`
	Items         []SidebarItem `json:"items"`
	LastUpdated   time.Time `json:"last_updated"`
}

// ContentState represents content loading state
type ContentState struct {
	ID          string    `json:"id"`
	LoaderType  string    `json:"loader_type"`
	LoaderID    string    `json:"loader_id"`
	Content     string    `json:"content"`
	IsLoading   bool      `json:"is_loading"`
	Error       string    `json:"error,omitempty"`
	LastLoaded  time.Time `json:"last_loaded"`
}

// Global state manager instance
var GlobalStateManager *AppStateManager

// AppStateManager manages the global application state
type AppStateManager struct {
	state     *AppState
	mu        sync.RWMutex
	observers []StateObserver
	stateFile string
	autoSave  bool
}

// NewAppStateManager creates a new application state manager
func NewAppStateManager() *AppStateManager {
	stateFile := getStateFilePath()
	
	manager := &AppStateManager{
		state: &AppState{
			Version:    "1.0.0",
			LastSaved:  time.Now(),
			UI:         UIState{},
			Panel:      &PanelState{ID: "main-panel"},
			Sidebar:    &SidebarState{ID: "main-sidebar"},
			Content:    &ContentState{ID: "main-content"},
			CustomData: make(map[string]interface{}),
			observers:  make([]StateObserver, 0),
			stateFile:  stateFile,
			autoSave:   false, // Disabled by default to avoid deadlocks during init
		},
		stateFile: stateFile,
		autoSave:  false, // Disabled by default to avoid deadlocks during init
		observers: make([]StateObserver, 0),
	}

	return manager
}

// GetState returns a copy of the current application state
func (asm *AppStateManager) GetState() *AppState {
	asm.mu.RLock()
	defer asm.mu.RUnlock()
	
	// Return a deep copy to prevent external mutations
	return asm.cloneState(asm.state)
}

// UpdateUIState updates the UI state
func (asm *AppStateManager) UpdateUIState(updateFunc func(*UIState)) {
	asm.mu.Lock()
	defer asm.mu.Unlock()
	
	oldState := asm.cloneState(asm.state)
	updateFunc(&asm.state.UI)
	asm.state.UI.LastInteraction = time.Now()
	
	asm.notifyObservers(oldState, asm.state)
	
	if asm.autoSave {
		asm.Save() // Synchronous save to avoid race conditions
	}
}

// UpdatePanelState updates the panel state
func (asm *AppStateManager) UpdatePanelState(updateFunc func(*PanelState)) {
	asm.mu.Lock()
	defer asm.mu.Unlock()
	
	oldState := asm.cloneState(asm.state)
	updateFunc(asm.state.Panel)
	asm.state.Panel.LastUpdated = time.Now()
	
	asm.notifyObservers(oldState, asm.state)
	
	if asm.autoSave {
		asm.Save() // Synchronous save to avoid race conditions
	}
}

// UpdateSidebarState updates the sidebar state
func (asm *AppStateManager) UpdateSidebarState(updateFunc func(*SidebarState)) {
	asm.mu.Lock()
	defer asm.mu.Unlock()
	
	oldState := asm.cloneState(asm.state)
	updateFunc(asm.state.Sidebar)
	asm.state.Sidebar.LastUpdated = time.Now()
	
	asm.notifyObservers(oldState, asm.state)
	
	if asm.autoSave {
		asm.Save() // Synchronous save to avoid race conditions
	}
}

// UpdateContentState updates the content state
func (asm *AppStateManager) UpdateContentState(updateFunc func(*ContentState)) {
	asm.mu.Lock()
	defer asm.mu.Unlock()
	
	oldState := asm.cloneState(asm.state)
	updateFunc(asm.state.Content)
	
	asm.notifyObservers(oldState, asm.state)
	
	if asm.autoSave {
		asm.Save() // Synchronous save to avoid race conditions
	}
}

// SetCustomData sets custom application data
func (asm *AppStateManager) SetCustomData(key string, value interface{}) {
	asm.mu.Lock()
	defer asm.mu.Unlock()
	
	oldState := asm.cloneState(asm.state)
	asm.state.CustomData[key] = value
	
	asm.notifyObservers(oldState, asm.state)
	
	if asm.autoSave {
		asm.Save() // Synchronous save to avoid race conditions
	}
}

// GetCustomData gets custom application data
func (asm *AppStateManager) GetCustomData(key string) (interface{}, bool) {
	asm.mu.RLock()
	defer asm.mu.RUnlock()
	
	value, exists := asm.state.CustomData[key]
	return value, exists
}

// Subscribe adds a state observer
func (asm *AppStateManager) Subscribe(observer StateObserver) func() {
	asm.mu.Lock()
	defer asm.mu.Unlock()
	
	asm.observers = append(asm.observers, observer)
	
	// Return unsubscribe function
	return func() {
		asm.mu.Lock()
		defer asm.mu.Unlock()
		
		for i, obs := range asm.observers {
			if obs == observer {
				asm.observers = append(asm.observers[:i], asm.observers[i+1:]...)
				break
			}
		}
	}
}

// Validate validates the current state
func (asm *AppStateManager) Validate() error {
	asm.mu.RLock()
	defer asm.mu.RUnlock()
	
	if asm.state.Panel == nil {
		return fmt.Errorf("panel state is nil")
	}
	
	if asm.state.Sidebar == nil {
		return fmt.Errorf("sidebar state is nil")
	}
	
	if asm.state.Content == nil {
		return fmt.Errorf("content state is nil")
	}
	
	if asm.state.Sidebar.SelectedIndex < 0 || 
	   (len(asm.state.Sidebar.Items) > 0 && asm.state.Sidebar.SelectedIndex >= len(asm.state.Sidebar.Items)) {
		return fmt.Errorf("sidebar selected index %d is out of bounds (items: %d)", 
			asm.state.Sidebar.SelectedIndex, len(asm.state.Sidebar.Items))
	}
	
	return nil
}

// Save persists the state to disk
func (asm *AppStateManager) Save() error {
	// Create a copy of the state to avoid holding the lock during file operations
	var stateCopy *AppState
	func() {
		asm.mu.RLock()
		defer asm.mu.RUnlock()
		stateCopy = asm.cloneState(asm.state)
		stateCopy.LastSaved = time.Now()
	}()
	
	data, err := json.MarshalIndent(stateCopy, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal state: %w", err)
	}
	
	// Ensure directory exists
	dir := filepath.Dir(asm.stateFile)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create state directory: %w", err)
	}
	
	if err := os.WriteFile(asm.stateFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write state file: %w", err)
	}
	
	// Update the LastSaved timestamp in the actual state
	asm.mu.Lock()
	asm.state.LastSaved = stateCopy.LastSaved
	asm.mu.Unlock()
	
	return nil
}

// Load loads the state from disk
func (asm *AppStateManager) Load() error {
	if _, err := os.Stat(asm.stateFile); os.IsNotExist(err) {
		// No state file exists, use defaults
		return nil
	}
	
	data, err := os.ReadFile(asm.stateFile)
	if err != nil {
		return fmt.Errorf("failed to read state file: %w", err)
	}
	
	asm.mu.Lock()
	defer asm.mu.Unlock()
	
	oldState := asm.cloneState(asm.state)
	
	if err := json.Unmarshal(data, asm.state); err != nil {
		return fmt.Errorf("failed to unmarshal state: %w", err)
	}
	
	// Reinitialize transient fields
	if asm.state.CustomData == nil {
		asm.state.CustomData = make(map[string]interface{})
	}
	asm.state.observers = make([]StateObserver, 0)
	asm.state.stateFile = asm.stateFile
	asm.state.autoSave = asm.autoSave
	
	asm.notifyObservers(oldState, asm.state)
	
	// Validate without holding the lock
	asm.mu.Unlock()
	err = asm.Validate()
	asm.mu.Lock()
	
	return err
}

// Reset resets the state to defaults
func (asm *AppStateManager) Reset() {
	asm.mu.Lock()
	defer asm.mu.Unlock()
	
	oldState := asm.cloneState(asm.state)
	
	asm.state = &AppState{
		Version:    "1.0.0",
		LastSaved:  time.Now(),
		UI:         UIState{},
		Panel:      &PanelState{ID: "main-panel"},
		Sidebar:    &SidebarState{ID: "main-sidebar"},
		Content:    &ContentState{ID: "main-content"},
		CustomData: make(map[string]interface{}),
		observers:  make([]StateObserver, 0),
		stateFile:  asm.stateFile,
		autoSave:   true,
	}
	
	asm.notifyObservers(oldState, asm.state)
}

// EnableAutoSave enables or disables automatic state persistence
func (asm *AppStateManager) EnableAutoSave(enabled bool) {
	asm.mu.Lock()
	defer asm.mu.Unlock()
	
	asm.autoSave = enabled
	asm.state.autoSave = enabled
}

// notifyObservers notifies all observers of state changes
func (asm *AppStateManager) notifyObservers(oldState, newState *AppState) {
	for _, observer := range asm.observers {
		go observer.OnStateChanged(oldState, newState)
	}
}

// cloneState creates a deep copy of the application state
func (asm *AppStateManager) cloneState(state *AppState) *AppState {
	data, _ := json.Marshal(state)
	var cloned AppState
	json.Unmarshal(data, &cloned)
	
	// Reinitialize transient fields
	cloned.observers = make([]StateObserver, 0)
	cloned.stateFile = state.stateFile
	cloned.autoSave = state.autoSave
	if cloned.CustomData == nil {
		cloned.CustomData = make(map[string]interface{})
	}
	
	return &cloned
}

// getStateFilePath returns the path for the state file
func getStateFilePath() string {
	// Check environment variable first
	if path := os.Getenv("TERMILLM_STATE_FILE"); path != "" {
		return path
	}
	
	// Check if we have a config directory
	if home, err := os.UserHomeDir(); err == nil {
		stateDir := filepath.Join(home, ".config", "termillm")
		return filepath.Join(stateDir, "state.json")
	}
	
	// Fallback to current directory
	return "termillm-state.json"
}

// InitStateManager initializes the global state manager
func InitStateManager() error {
	GlobalStateManager = NewAppStateManager()
	return GlobalStateManager.Load()
}

// GetGlobalState returns the global application state
func GetGlobalState() *AppState {
	if GlobalStateManager == nil {
		return nil
	}
	return GlobalStateManager.GetState()
}

// Component state implementations

// GetID returns the component ID
func (ps *PanelState) GetID() string { return ps.ID }
func (ss *SidebarState) GetID() string { return ss.ID }
func (cs *ContentState) GetID() string { return cs.ID }

// GetType returns the component type
func (ps *PanelState) GetType() string { return "panel" }
func (ss *SidebarState) GetType() string { return "sidebar" }
func (cs *ContentState) GetType() string { return "content" }

// Clone creates a copy of the component state
func (ps *PanelState) Clone() ComponentState {
	clone := *ps
	return &clone
}

func (ss *SidebarState) Clone() ComponentState {
	clone := *ss
	clone.Items = make([]SidebarItem, len(ss.Items))
	copy(clone.Items, ss.Items)
	return &clone
}

func (cs *ContentState) Clone() ComponentState {
	clone := *cs
	return &clone
}

// Merge merges another component state into this one
func (ps *PanelState) Merge(other ComponentState) error {
	if otherPanel, ok := other.(*PanelState); ok {
		if otherPanel.Width > 0 {
			ps.Width = otherPanel.Width
		}
		if otherPanel.Height > 0 {
			ps.Height = otherPanel.Height
		}
		if otherPanel.LoaderID != "" {
			ps.LoaderID = otherPanel.LoaderID
		}
		ps.Ready = otherPanel.Ready
		ps.LastUpdated = time.Now()
		return nil
	}
	return fmt.Errorf("cannot merge %T into PanelState", other)
}

func (ss *SidebarState) Merge(other ComponentState) error {
	if otherSidebar, ok := other.(*SidebarState); ok {
		ss.Visible = otherSidebar.Visible
		ss.Focused = otherSidebar.Focused
		if otherSidebar.Width > 0 {
			ss.Width = otherSidebar.Width
		}
		if otherSidebar.Height > 0 {
			ss.Height = otherSidebar.Height
		}
		if otherSidebar.SelectedIndex >= 0 {
			ss.SelectedIndex = otherSidebar.SelectedIndex
		}
		if len(otherSidebar.Items) > 0 {
			ss.Items = make([]SidebarItem, len(otherSidebar.Items))
			copy(ss.Items, otherSidebar.Items)
		}
		ss.LastUpdated = time.Now()
		return nil
	}
	return fmt.Errorf("cannot merge %T into SidebarState", other)
}

func (cs *ContentState) Merge(other ComponentState) error {
	if otherContent, ok := other.(*ContentState); ok {
		if otherContent.LoaderType != "" {
			cs.LoaderType = otherContent.LoaderType
		}
		if otherContent.LoaderID != "" {
			cs.LoaderID = otherContent.LoaderID
		}
		if otherContent.Content != "" {
			cs.Content = otherContent.Content
		}
		cs.IsLoading = otherContent.IsLoading
		if otherContent.Error != "" {
			cs.Error = otherContent.Error
		}
		if !otherContent.LastLoaded.IsZero() {
			cs.LastLoaded = otherContent.LastLoaded
		}
		return nil
	}
	return fmt.Errorf("cannot merge %T into ContentState", other)
}
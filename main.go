package main

import (
	"fmt"
	"log"
	"os"

	"eionz.com/demo/pkg/loader"
	"eionz.com/demo/pkg/syntax"
	"eionz.com/demo/pkg/themes"
	tea "github.com/charmbracelet/bubbletea"
)

func main() {
	// Initialize configuration
	if err := InitConfig(); err != nil {
		log.Fatalf("Failed to initialize configuration: %v", err)
	}

	// Initialize themes
	themes.InitializeGlobalThemeManager(themes.GetDefaultThemesPath())
	themeManager := themes.GetGlobalThemeManager()
	themeManager.SetTheme("dark") // Set default theme
	themeManager.ApplyTheme()
	
	// Initialize syntax highlighting with theme
	theme := themeManager.CreateSyntaxTheme(themeManager.GetCurrentTheme().Syntax)
	syntax.InitializeGlobalSyntaxService(theme)

	// Initialize state management
	if err := InitStateManager(); err != nil {
		log.Fatalf("Failed to initialize state manager: %v", err)
	}

	// Initialize key bindings after configuration is loaded
	InitKeyMap()

	// Create a content loader
	var contentLoader loader.ContentLoader
	var loaderID string

	f, err := initLogger("debug.log", os.Getenv("DEBUG"))
	if err != nil {
		log.Fatal(err)
	}
	defer f()

	// Check if a file path was provided as an argument
	if len(os.Args) > 1 {
		// Use file loader if a file path was provided
		filePath := os.Args[1]
		baseLoader := loader.NewFileContentLoader(filePath)
		syntaxLoader := loader.NewSyntaxHighlightingLoader(baseLoader)
		syntaxLoader.SetFilePath(filePath)
		contentLoader = syntaxLoader
		loaderID = "file-" + filePath
	} else {
		// Try to load test-content.txt first
		if _, err := os.Stat("test-content.txt"); err == nil {
			baseLoader := loader.NewFileContentLoader("test-content.txt")
			syntaxLoader := loader.NewSyntaxHighlightingLoader(baseLoader)
			syntaxLoader.SetFilePath("test-content.txt")
			contentLoader = syntaxLoader
			loaderID = "file-test-content.txt"
		} else if _, err := os.Stat("sample.txt"); err == nil {
			// Fall back to sample.txt if it exists
			baseLoader := loader.NewFileContentLoader("sample.txt")
			syntaxLoader := loader.NewSyntaxHighlightingLoader(baseLoader)
			syntaxLoader.SetFilePath("sample.txt")
			contentLoader = syntaxLoader
			loaderID = "file-sample.txt"
		} else {
			// Fallback to string loader if no file is available
			baseLoader := loader.NewStringContentLoader("This is a test content loaded through the content loader.\n\nIt demonstrates how the spinner is now part of the panel structure.")
			syntaxLoader := loader.NewSyntaxHighlightingLoader(baseLoader)
			syntaxLoader.SetLanguage("text") // Force plain text for demo content
			contentLoader = syntaxLoader
			loaderID = "string-default"
		}
	}

	// Create an adapter for the loader
	loaderAdapter := NewLoaderAdapter(contentLoader, loaderID)

	p := tea.NewProgram(
		NewModel(loaderAdapter),
		tea.WithAltScreen(),      // use the full size of the terminal in its "alternate screen buffer"
		tea.WithMouseAllMotion(), // turn on mouse support so we can track the mouse wheel
	)

	if _, err := p.Run(); err != nil {
		fmt.Println("could not run program:", err)
		os.Exit(1)
	}

	// Save state before exiting
	if GlobalStateManager != nil {
		GlobalStateManager.Save()
	}
}

func initLogger(f string, debug string) (func(), error) {
	// Create a log file
	if len(debug) > 0 {
		f, err := tea.LogToFile(f, "[DEBUG]")
		if err != nil {
			return func() {}, err
		}
		log.SetFlags(log.Lmsgprefix | log.LstdFlags)
		return func() {
			f.Close()
		}, nil
	}

	return func() {}, nil
}

package main

import (
	"context"
	"errors"
	"testing"

	"eionz.com/demo/pkg/loader"
)

// MockLoader implements the loader.ContentLoader interface for testing
type MockLoader struct {
	loading bool
	err     error
	content string
}

func (m *MockLoader) Load(ctx context.Context) <-chan loader.LoadResult {
	resultCh := make(chan loader.LoadResult, 1)
	m.loading = true

	go func() {
		defer close(resultCh)
		// Simulate loading
		m.loading = false
		resultCh <- loader.LoadResult{
			Content: m.content,
			Err:     m.err,
		}
	}()

	return resultCh
}

func (m *MockLoader) IsLoading() bool {
	return m.loading
}

func (m *MockLoader) Error() error {
	return m.err
}

func (m *MockLoader) Content() string {
	return m.content
}

func (m *MockLoader) Reset() {
	m.loading = false
	m.err = nil
}

// TestLoaderAdapter_Load_Success tests successful loading through the adapter
func TestLoaderAdapter_Load_Success(t *testing.T) {
	// Create a mock loader with test content
	mockLoader := &MockLoader{
		content: "Test content",
	}

	// Create an adapter with the mock loader
	adapter := NewLoaderAdapter(mockLoader, "test-id")

	// Test initial state
	if adapter.IsLoading() {
		t.Error("Adapter should not be in loading state initially")
	}
	if adapter.Error() != nil {
		t.Errorf("Adapter should not have error initially, got: %v", adapter.Error())
	}
	if adapter.Content() != "Test content" {
		t.Errorf("Adapter content should match mock loader, expected: 'Test content', got: %s", adapter.Content())
	}

	// Get the load command
	cmd := adapter.Load()

	// Execute the command
	msg := cmd()

	// Verify the message
	contentLoadedMsg, ok := msg.(ContentLoadedMsg)
	if !ok {
		t.Fatalf("Expected ContentLoadedMsg, got: %T", msg)
	}
	if contentLoadedMsg.LoaderID != "test-id" {
		t.Errorf("Expected LoaderID: 'test-id', got: %s", contentLoadedMsg.LoaderID)
	}

	// Verify the adapter state
	if adapter.IsLoading() {
		t.Error("Adapter should not be in loading state after completion")
	}
	if adapter.Error() != nil {
		t.Errorf("Adapter should not have error after successful load, got: %v", adapter.Error())
	}
	if adapter.Content() != "Test content" {
		t.Errorf("Adapter content should match, expected: 'Test content', got: %s", adapter.Content())
	}
}

// TestLoaderAdapter_Load_Error tests loading with error through the adapter
func TestLoaderAdapter_Load_Error(t *testing.T) {
	// Create a mock loader with an error
	testError := errors.New("test error")
	mockLoader := &MockLoader{
		err: testError,
	}

	// Create an adapter with the mock loader
	adapter := NewLoaderAdapter(mockLoader, "test-id")

	// Get the load command
	cmd := adapter.Load()

	// Execute the command
	msg := cmd()

	// Verify the message
	contentLoadedMsg, ok := msg.(ContentLoadedMsg)
	if !ok {
		t.Fatalf("Expected ContentLoadedMsg, got: %T", msg)
	}
	if contentLoadedMsg.LoaderID != "test-id" {
		t.Errorf("Expected LoaderID: 'test-id', got: %s", contentLoadedMsg.LoaderID)
	}

	// Verify the adapter state
	if adapter.IsLoading() {
		t.Error("Adapter should not be in loading state after error")
	}
	if adapter.Error() != testError {
		t.Errorf("Adapter error should match, expected: %v, got: %v", testError, adapter.Error())
	}
}

// TestLoaderAdapter_Reset tests the Reset method
func TestLoaderAdapter_Reset(t *testing.T) {
	// Create a mock loader with an error
	mockLoader := &MockLoader{
		loading: true,
		err:     errors.New("test error"),
		content: "test content",
	}

	// Create an adapter with the mock loader
	adapter := NewLoaderAdapter(mockLoader, "test-id")

	// Verify the initial state
	if !adapter.IsLoading() {
		t.Error("Adapter should be in loading state")
	}
	if adapter.Error() == nil {
		t.Error("Adapter should have an error")
	}
	if adapter.Content() != "test content" {
		t.Errorf("Adapter content should be 'test content', got: %s", adapter.Content())
	}

	// Reset the adapter
	adapter.Reset()

	// Verify the state after reset
	if adapter.IsLoading() {
		t.Error("Adapter should not be in loading state after reset")
	}
	if adapter.Error() != nil {
		t.Errorf("Adapter should not have error after reset, got: %v", adapter.Error())
	}
}

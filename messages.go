package main

import (
	"time"

	tea "github.com/charmbracelet/bubbletea"
)

// Define a message to signal panel initialization is complete
type panelInitializedMsg struct{}

// panelInitializedMsg is defined in messages.go

// waitForPanelInitCmd creates a command that waits and then sends a panelInitializedMsg
func waitForPanelInitCmd() tea.Cmd {
	return func() tea.Msg {
		// Use a shorter delay for testing
		time.Sleep(500 * time.Millisecond)
		// fmt.Println("Panel initialization complete") // Debug output
		return panelInitializedMsg{}
	}
}

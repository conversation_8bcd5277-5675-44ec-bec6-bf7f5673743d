package main

import (
	"fmt"
	"syscall"
	"unsafe"
)

type winsize struct {
	ws_row    uint16
	ws_col    uint16
	ws_xpixel uint16
	ws_ypixel uint16
}

const TIOCGWINSZ = 0x40087468

func main() {
	var ws winsize
	fd := 0 // stdin

	ret, _, err := syscall.Syscall(syscall.SYS_IOCTL, uintptr(fd), TIOCGWINSZ, uintptr(unsafe.Pointer(&ws)))
	if ret == 0 {
		fmt.Printf("Terminal dimensions: %d columns x %d rows\n", ws.ws_col, ws.ws_row)
	} else {
		fmt.Println("Error getting terminal size:", err)
	}
}

package main

import (
	"eionz.com/demo/internal/styledkey"
	"github.com/charmbracelet/bubbles/key"
	tea "github.com/charmbracelet/bubbletea"
)

// KeyMap holds all the key bindings for the application
type KeyMap struct {
	Quit               styledkey.Binding
	ToggleSidebar      styledkey.Binding
	ToggleSidebarWidth styledkey.Binding
	FocusSidebar       styledkey.Binding
	FocusMain          styledkey.Binding
	CommandMode        styledkey.Binding
	ToggleChatMode     styledkey.Binding
	// Add more bindings here as needed, for example:
	// Help styledkey.Binding
	// Search styledkey.Binding
}

var unicodePlaceholder = "🩷"

// DefaultKeyMap contains the default key bindings for the application
var DefaultKeyMap KeyMap

// InitKeyMap initializes the key bindings from configuration
func InitKeyMap() {
	config := GetKeyBindConfig()

	DefaultKeyMap = KeyMap{
		Quit: styledkey.New(
			config.Quit,
			config.Quit[0],
			unicodePlaceholder+" press "+config.Quit[0]+" to quit",
			styledkey.NewPrimaryStyle(),
		),
		ToggleSidebar: styledkey.New(
			config.ToggleSidebar,
			config.ToggleSidebar[0],
			"press "+config.ToggleSidebar[0]+" to toggle sidebar",
			styledkey.NewPrimaryStyle(),
		),
		ToggleSidebarWidth: styledkey.New(
			config.ToggleSidebarWidth,
			config.ToggleSidebarWidth[0],
			"press "+config.ToggleSidebarWidth[0]+" to resize sidebar",
			styledkey.NewPrimaryStyle(),
		),
		FocusSidebar: styledkey.New(
			config.FocusSidebar,
			config.FocusSidebar[0],
			"press "+config.FocusSidebar[0]+" to focus sidebar",
			styledkey.NewPrimaryStyle(),
		),
		FocusMain: styledkey.New(
			config.FocusMain,
			config.FocusMain[0],
			"press "+config.FocusMain[0]+" to focus main",
			styledkey.NewPrimaryStyle(),
		),
		CommandMode: styledkey.New(
			config.CommandMode,
			config.CommandMode[0],
			"press "+config.CommandMode[0]+" for command mode",
			styledkey.NewPrimaryStyle(),
		),
		ToggleChatMode: styledkey.New(
			[]string{"c"},
			"c",
			"press c to toggle chat mode",
			styledkey.NewPrimaryStyle(),
		),
	}
}

// MatchQuit checks if the key message matches the Quit binding
func (k KeyMap) MatchQuit(msg tea.KeyMsg) bool {
	return key.Matches(msg, k.Quit.Binding)
}

// MatchToggleSidebar checks if the key message matches the ToggleSidebar binding
func (k KeyMap) MatchToggleSidebar(msg tea.KeyMsg) bool {
	return key.Matches(msg, k.ToggleSidebar.Binding)
}

// MatchToggleSidebarWidth checks if the key message matches the ToggleSidebarWidth binding
func (k KeyMap) MatchToggleSidebarWidth(msg tea.KeyMsg) bool {
	return key.Matches(msg, k.ToggleSidebarWidth.Binding)
}

// MatchFocusSidebar checks if the key message matches the FocusSidebar binding
func (k KeyMap) MatchFocusSidebar(msg tea.KeyMsg) bool {
	return key.Matches(msg, k.FocusSidebar.Binding)
}

// MatchFocusMain checks if the key message matches the FocusMain binding
func (k KeyMap) MatchFocusMain(msg tea.KeyMsg) bool {
	return key.Matches(msg, k.FocusMain.Binding)
}

// MatchCommandMode checks if the key message matches the CommandMode binding
func (k KeyMap) MatchCommandMode(msg tea.KeyMsg) bool {
	return key.Matches(msg, k.CommandMode.Binding)
}

// MatchToggleChatMode checks if the key message matches the ToggleChatMode binding
func (k KeyMap) MatchToggleChatMode(msg tea.KeyMsg) bool {
	return key.Matches(msg, k.ToggleChatMode.Binding)
}

// GetAllBindings returns all key bindings as a slice of named bindings
func (k KeyMap) GetAllBindings() []NamedBinding {
	return []NamedBinding{
		{Name: "Quit", Binding: k.Quit},
		{Name: "ToggleSidebar", Binding: k.ToggleSidebar},
		{Name: "ToggleSidebarWidth", Binding: k.ToggleSidebarWidth},
		{Name: "FocusSidebar", Binding: k.FocusSidebar},
		{Name: "FocusMain", Binding: k.FocusMain},
		{Name: "CommandMode", Binding: k.CommandMode},
		{Name: "ToggleChatMode", Binding: k.ToggleChatMode},
		// Add more bindings here when added to the struct
		// {Name: "Help", Binding: k.Help},
		// {Name: "Search", Binding: k.Search},
	}
}

// NamedBinding associates a name with a key binding
type NamedBinding struct {
	Name    string
	Binding styledkey.Binding
}

package eventbus

import (
	"context"
	"fmt"
	"sync"
	"time"

	"eionz.com/demo/pkg/domain"
)

// EventHandler processes domain events
type EventHandler interface {
	Handle(ctx context.Context, event domain.DomainEvent) error
	CanHandle(eventType string) bool
	HandlerName() string
}

// EventHandlerFunc is a function adapter for EventHandler
type EventHandlerFunc struct {
	name       string
	handleFunc func(ctx context.Context, event domain.DomainEvent) error
	eventTypes []string
}

func NewEventHandlerFunc(name string, eventTypes []string, handleFunc func(ctx context.Context, event domain.DomainEvent) error) *EventHandlerFunc {
	return &EventHandlerFunc{
		name:       name,
		handleFunc: handleFunc,
		eventTypes: eventTypes,
	}
}

func (h *EventHandlerFunc) Handle(ctx context.Context, event domain.DomainEvent) error {
	return h.handleFunc(ctx, event)
}

func (h *EventHandlerFunc) CanHandle(eventType string) bool {
	for _, et := range h.eventTypes {
		if et == eventType {
			return true
		}
	}
	return false
}

func (h *EventHandlerFunc) HandlerName() string {
	return h.name
}

// EventBusConfig holds configuration for the event bus
type EventBusConfig struct {
	MaxRetries      int
	RetryDelay      time.Duration
	BufferSize      int
	MaxConcurrency  int
	EnableMetrics   bool
	TimeoutDuration time.Duration
}

func DefaultEventBusConfig() EventBusConfig {
	return EventBusConfig{
		MaxRetries:      3,
		RetryDelay:      100 * time.Millisecond,
		BufferSize:      1000,
		MaxConcurrency:  10,
		EnableMetrics:   true,
		TimeoutDuration: 5 * time.Second,
	}
}

// EventBusMetrics holds metrics about event processing
type EventBusMetrics struct {
	TotalEvents       int64
	SuccessfulEvents  int64
	FailedEvents      int64
	HandlerExecutions int64
	AverageLatency    time.Duration
	mutex             sync.RWMutex
}

func (m *EventBusMetrics) IncrementTotal() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.TotalEvents++
}

func (m *EventBusMetrics) IncrementSuccessful() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.SuccessfulEvents++
}

func (m *EventBusMetrics) IncrementFailed() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.FailedEvents++
}

func (m *EventBusMetrics) IncrementHandlerExecutions() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.HandlerExecutions++
}

func (m *EventBusMetrics) UpdateLatency(latency time.Duration) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	// Simple moving average
	if m.AverageLatency == 0 {
		m.AverageLatency = latency
	} else {
		m.AverageLatency = (m.AverageLatency + latency) / 2
	}
}

func (m *EventBusMetrics) GetStats() (int64, int64, int64, int64, time.Duration) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.TotalEvents, m.SuccessfulEvents, m.FailedEvents, m.HandlerExecutions, m.AverageLatency
}

// EventBus manages domain event publishing and subscription
type EventBus struct {
	handlers        map[string][]EventHandler
	handlersMutex   sync.RWMutex
	eventChannel    chan domain.DomainEvent
	workerPool      chan struct{}
	config          EventBusConfig
	metrics         *EventBusMetrics
	ctx             context.Context
	cancel          context.CancelFunc
	wg              sync.WaitGroup
	started         bool
	startedMutex    sync.RWMutex
}

// NewEventBus creates a new event bus with the given configuration
func NewEventBus(config EventBusConfig) *EventBus {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &EventBus{
		handlers:     make(map[string][]EventHandler),
		eventChannel: make(chan domain.DomainEvent, config.BufferSize),
		workerPool:   make(chan struct{}, config.MaxConcurrency),
		config:       config,
		metrics:      &EventBusMetrics{},
		ctx:          ctx,
		cancel:       cancel,
		started:      false,
	}
}

// Start begins processing events
func (eb *EventBus) Start() error {
	eb.startedMutex.Lock()
	defer eb.startedMutex.Unlock()
	
	if eb.started {
		return fmt.Errorf("event bus is already started")
	}
	
	eb.started = true
	
	// Start worker goroutine
	eb.wg.Add(1)
	go eb.processEvents()
	
	return nil
}

// Stop gracefully shuts down the event bus
func (eb *EventBus) Stop() error {
	eb.startedMutex.Lock()
	defer eb.startedMutex.Unlock()
	
	if !eb.started {
		return nil
	}
	
	eb.started = false
	eb.cancel()
	close(eb.eventChannel)
	
	// Wait for all workers to finish
	eb.wg.Wait()
	
	return nil
}

// IsStarted returns whether the event bus is currently running
func (eb *EventBus) IsStarted() bool {
	eb.startedMutex.RLock()
	defer eb.startedMutex.RUnlock()
	return eb.started
}

// Subscribe registers an event handler for specific event types
func (eb *EventBus) Subscribe(handler EventHandler) error {
	if handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}
	
	eb.handlersMutex.Lock()
	defer eb.handlersMutex.Unlock()
	
	// Register handler for all event types it can handle
	// We need to determine what event types this handler supports
	eventTypes := eb.getSupportedEventTypes(handler)
	
	for _, eventType := range eventTypes {
		eb.handlers[eventType] = append(eb.handlers[eventType], handler)
	}
	
	return nil
}

// SubscribeToEventType registers a handler for a specific event type
func (eb *EventBus) SubscribeToEventType(eventType string, handler EventHandler) error {
	if handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}
	
	if eventType == "" {
		return fmt.Errorf("event type cannot be empty")
	}
	
	eb.handlersMutex.Lock()
	defer eb.handlersMutex.Unlock()
	
	eb.handlers[eventType] = append(eb.handlers[eventType], handler)
	
	return nil
}

// Unsubscribe removes an event handler
func (eb *EventBus) Unsubscribe(handler EventHandler) error {
	if handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}
	
	eb.handlersMutex.Lock()
	defer eb.handlersMutex.Unlock()
	
	for eventType, handlers := range eb.handlers {
		for i, h := range handlers {
			if h == handler {
				eb.handlers[eventType] = append(handlers[:i], handlers[i+1:]...)
				break
			}
		}
	}
	
	return nil
}

// Publish sends an event to all registered handlers
func (eb *EventBus) Publish(event domain.DomainEvent) error {
	if event == nil {
		return fmt.Errorf("event cannot be nil")
	}
	
	eb.startedMutex.RLock()
	if !eb.started {
		eb.startedMutex.RUnlock()
		return fmt.Errorf("event bus is not started")
	}
	eb.startedMutex.RUnlock()
	
	if eb.config.EnableMetrics {
		eb.metrics.IncrementTotal()
	}
	
	select {
	case eb.eventChannel <- event:
		return nil
	case <-eb.ctx.Done():
		return fmt.Errorf("event bus is shutting down")
	default:
		if eb.config.EnableMetrics {
			eb.metrics.IncrementFailed()
		}
		return fmt.Errorf("event channel is full")
	}
}

// PublishAsync sends an event asynchronously without blocking
func (eb *EventBus) PublishAsync(event domain.DomainEvent) {
	go func() {
		_ = eb.Publish(event) // Ignore error for async publish
	}()
}

// GetMetrics returns current event bus metrics
func (eb *EventBus) GetMetrics() *EventBusMetrics {
	return eb.metrics
}

// GetHandlerCount returns the number of handlers for a given event type
func (eb *EventBus) GetHandlerCount(eventType string) int {
	eb.handlersMutex.RLock()
	defer eb.handlersMutex.RUnlock()
	
	handlers, exists := eb.handlers[eventType]
	if !exists {
		return 0
	}
	
	return len(handlers)
}

// ListEventTypes returns all registered event types
func (eb *EventBus) ListEventTypes() []string {
	eb.handlersMutex.RLock()
	defer eb.handlersMutex.RUnlock()
	
	eventTypes := make([]string, 0, len(eb.handlers))
	for eventType := range eb.handlers {
		eventTypes = append(eventTypes, eventType)
	}
	
	return eventTypes
}

// processEvents is the main event processing loop
func (eb *EventBus) processEvents() {
	defer eb.wg.Done()
	
	for {
		select {
		case event, ok := <-eb.eventChannel:
			if !ok {
				// Channel closed, exit
				return
			}
			
			// Process event in worker pool
			eb.workerPool <- struct{}{} // Acquire worker
			eb.wg.Add(1)
			go eb.handleEvent(event)
			
		case <-eb.ctx.Done():
			return
		}
	}
}

// handleEvent processes a single event
func (eb *EventBus) handleEvent(event domain.DomainEvent) {
	defer eb.wg.Done()
	defer func() { <-eb.workerPool }() // Release worker
	
	startTime := time.Now()
	
	eb.handlersMutex.RLock()
	handlers, exists := eb.handlers[event.EventType()]
	eb.handlersMutex.RUnlock()
	
	if !exists || len(handlers) == 0 {
		return // No handlers for this event type
	}
	
	// Create context with timeout
	ctx, cancel := context.WithTimeout(eb.ctx, eb.config.TimeoutDuration)
	defer cancel()
	
	// Execute all handlers for this event type
	for _, handler := range handlers {
		if handler.CanHandle(event.EventType()) {
			eb.executeHandlerWithRetry(ctx, handler, event)
			
			if eb.config.EnableMetrics {
				eb.metrics.IncrementHandlerExecutions()
			}
		}
	}
	
	if eb.config.EnableMetrics {
		latency := time.Since(startTime)
		eb.metrics.UpdateLatency(latency)
		eb.metrics.IncrementSuccessful()
	}
}

// executeHandlerWithRetry executes a handler with retry logic
func (eb *EventBus) executeHandlerWithRetry(ctx context.Context, handler EventHandler, event domain.DomainEvent) {
	var lastErr error
	
	for attempt := 0; attempt <= eb.config.MaxRetries; attempt++ {
		if attempt > 0 {
			select {
			case <-time.After(eb.config.RetryDelay):
			case <-ctx.Done():
				return
			}
		}
		
		err := handler.Handle(ctx, event)
		if err == nil {
			return // Success
		}
		
		lastErr = err
		
		// Check if context was cancelled
		if ctx.Err() != nil {
			break
		}
	}
	
	// All retries failed
	if eb.config.EnableMetrics {
		eb.metrics.IncrementFailed()
	}
	
	// TODO: Add error logging or error handler callback
	_ = lastErr
}

// getSupportedEventTypes uses reflection to determine what event types a handler supports
func (eb *EventBus) getSupportedEventTypes(handler EventHandler) []string {
	// For now, we'll rely on the handler's CanHandle method
	// In a real implementation, you might want to use reflection or registration
	
	// Common event types to check
	commonEventTypes := []string{
		"LayoutCreated",
		"LayoutResized",
		"FocusChanged",
		"SidebarToggle",
		"SidebarResized",
		"ContentCreated",
		"ContentLoaded",
		"ContentStateChanged",
		"ContentError",
		"ContentTransformed",
		"ContentCached",
		// Test event types
		"TestEvent",
		"EventA",
		"EventB",
	}
	
	var supportedTypes []string
	for _, eventType := range commonEventTypes {
		if handler.CanHandle(eventType) {
			supportedTypes = append(supportedTypes, eventType)
		}
	}
	
	return supportedTypes
}
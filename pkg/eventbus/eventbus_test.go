package eventbus

import (
	"context"
	"fmt"
	"testing"
	"time"

	"eionz.com/demo/pkg/domain"
)

// Mock event for testing
type MockEvent struct {
	id         string
	eventType  string
	occurredOn time.Time
	aggregateID string
	version    int
}

func NewMockEvent(eventType, aggregateID string) *MockEvent {
	return &MockEvent{
		id:          "test-event-1",
		eventType:   eventType,
		occurredOn:  time.Now(),
		aggregateID: aggregateID,
		version:     1,
	}
}

func (e *MockEvent) EventID() string {
	return e.id
}

func (e *MockEvent) EventType() string {
	return e.eventType
}

func (e *MockEvent) OccurredOn() time.Time {
	return e.occurredOn
}

func (e *MockEvent) AggregateID() string {
	return e.aggregateID
}

func (e *MockEvent) EventVersion() int {
	return e.version
}

// Mock handler for testing
type MockHandler struct {
	name           string
	supportedTypes []string
	handledEvents  []domain.DomainEvent
	shouldFail     bool
	handledCount   int
}

func NewMockHandler(name string, supportedTypes []string) *MockHandler {
	return &MockHandler{
		name:           name,
		supportedTypes: supportedTypes,
		handledEvents:  make([]domain.DomainEvent, 0),
		shouldFail:     false,
		handledCount:   0,
	}
}

func (h *MockHandler) Handle(ctx context.Context, event domain.DomainEvent) error {
	h.handledEvents = append(h.handledEvents, event)
	h.handledCount++
	
	if h.shouldFail {
		return fmt.Errorf("mock handler failed")
	}
	
	return nil
}

func (h *MockHandler) CanHandle(eventType string) bool {
	for _, supportedType := range h.supportedTypes {
		if supportedType == eventType {
			return true
		}
	}
	return false
}

func (h *MockHandler) HandlerName() string {
	return h.name
}

func (h *MockHandler) SetShouldFail(shouldFail bool) {
	h.shouldFail = shouldFail
}

func (h *MockHandler) GetHandledCount() int {
	return h.handledCount
}

func (h *MockHandler) GetHandledEvents() []domain.DomainEvent {
	return h.handledEvents
}

func TestEventBus_StartStop(t *testing.T) {
	config := DefaultEventBusConfig()
	config.BufferSize = 10
	
	eventBus := NewEventBus(config)
	
	if eventBus.IsStarted() {
		t.Error("Event bus should not be started initially")
	}
	
	err := eventBus.Start()
	if err != nil {
		t.Fatalf("Failed to start event bus: %v", err)
	}
	
	if !eventBus.IsStarted() {
		t.Error("Event bus should be started")
	}
	
	// Starting again should fail
	err = eventBus.Start()
	if err == nil {
		t.Error("Starting already started event bus should fail")
	}
	
	err = eventBus.Stop()
	if err != nil {
		t.Fatalf("Failed to stop event bus: %v", err)
	}
	
	if eventBus.IsStarted() {
		t.Error("Event bus should not be started after stop")
	}
}

func TestEventBus_Subscribe(t *testing.T) {
	config := DefaultEventBusConfig()
	eventBus := NewEventBus(config)
	
	handler := NewMockHandler("test-handler", []string{"TestEvent"})
	
	err := eventBus.Subscribe(handler)
	if err != nil {
		t.Fatalf("Failed to subscribe handler: %v", err)
	}
	
	// Check if handler is registered
	count := eventBus.GetHandlerCount("TestEvent")
	if count != 1 {
		t.Errorf("Expected 1 handler for TestEvent, got %d", count)
	}
}

func TestEventBus_SubscribeToEventType(t *testing.T) {
	config := DefaultEventBusConfig()
	eventBus := NewEventBus(config)
	
	handler := NewMockHandler("test-handler", []string{"TestEvent"})
	
	err := eventBus.SubscribeToEventType("TestEvent", handler)
	if err != nil {
		t.Fatalf("Failed to subscribe handler to event type: %v", err)
	}
	
	count := eventBus.GetHandlerCount("TestEvent")
	if count != 1 {
		t.Errorf("Expected 1 handler for TestEvent, got %d", count)
	}
}

func TestEventBus_Unsubscribe(t *testing.T) {
	config := DefaultEventBusConfig()
	eventBus := NewEventBus(config)
	
	handler := NewMockHandler("test-handler", []string{"TestEvent"})
	
	err := eventBus.SubscribeToEventType("TestEvent", handler)
	if err != nil {
		t.Fatalf("Failed to subscribe handler: %v", err)
	}
	
	err = eventBus.Unsubscribe(handler)
	if err != nil {
		t.Fatalf("Failed to unsubscribe handler: %v", err)
	}
	
	count := eventBus.GetHandlerCount("TestEvent")
	if count != 0 {
		t.Errorf("Expected 0 handlers for TestEvent after unsubscribe, got %d", count)
	}
}

func TestEventBus_PublishAndHandle(t *testing.T) {
	config := DefaultEventBusConfig()
	config.BufferSize = 10
	
	eventBus := NewEventBus(config)
	handler := NewMockHandler("test-handler", []string{"TestEvent"})
	
	err := eventBus.SubscribeToEventType("TestEvent", handler)
	if err != nil {
		t.Fatalf("Failed to subscribe handler: %v", err)
	}
	
	err = eventBus.Start()
	if err != nil {
		t.Fatalf("Failed to start event bus: %v", err)
	}
	defer eventBus.Stop()
	
	// Publish an event
	event := NewMockEvent("TestEvent", "test-aggregate")
	err = eventBus.Publish(event)
	if err != nil {
		t.Fatalf("Failed to publish event: %v", err)
	}
	
	// Wait for event to be processed
	time.Sleep(50 * time.Millisecond)
	
	// Check if handler received the event
	if handler.GetHandledCount() != 1 {
		t.Errorf("Expected handler to be called 1 time, got %d", handler.GetHandledCount())
	}
	
	handledEvents := handler.GetHandledEvents()
	if len(handledEvents) != 1 {
		t.Errorf("Expected 1 handled event, got %d", len(handledEvents))
	}
	
	if handledEvents[0].EventType() != "TestEvent" {
		t.Errorf("Expected TestEvent, got %s", handledEvents[0].EventType())
	}
}

func TestEventBus_MultipleHandlers(t *testing.T) {
	config := DefaultEventBusConfig()
	config.BufferSize = 10
	
	eventBus := NewEventBus(config)
	
	handler1 := NewMockHandler("handler-1", []string{"TestEvent"})
	handler2 := NewMockHandler("handler-2", []string{"TestEvent"})
	
	err := eventBus.SubscribeToEventType("TestEvent", handler1)
	if err != nil {
		t.Fatalf("Failed to subscribe handler1: %v", err)
	}
	
	err = eventBus.SubscribeToEventType("TestEvent", handler2)
	if err != nil {
		t.Fatalf("Failed to subscribe handler2: %v", err)
	}
	
	err = eventBus.Start()
	if err != nil {
		t.Fatalf("Failed to start event bus: %v", err)
	}
	defer eventBus.Stop()
	
	// Publish an event
	event := NewMockEvent("TestEvent", "test-aggregate")
	err = eventBus.Publish(event)
	if err != nil {
		t.Fatalf("Failed to publish event: %v", err)
	}
	
	// Wait for event to be processed
	time.Sleep(50 * time.Millisecond)
	
	// Check if both handlers received the event
	if handler1.GetHandledCount() != 1 {
		t.Errorf("Expected handler1 to be called 1 time, got %d", handler1.GetHandledCount())
	}
	
	if handler2.GetHandledCount() != 1 {
		t.Errorf("Expected handler2 to be called 1 time, got %d", handler2.GetHandledCount())
	}
}

func TestEventBus_Metrics(t *testing.T) {
	config := DefaultEventBusConfig()
	config.EnableMetrics = true
	config.BufferSize = 10
	
	eventBus := NewEventBus(config)
	handler := NewMockHandler("test-handler", []string{"TestEvent"})
	
	err := eventBus.SubscribeToEventType("TestEvent", handler)
	if err != nil {
		t.Fatalf("Failed to subscribe handler: %v", err)
	}
	
	err = eventBus.Start()
	if err != nil {
		t.Fatalf("Failed to start event bus: %v", err)
	}
	defer eventBus.Stop()
	
	// Publish multiple events
	for i := 0; i < 3; i++ {
		event := NewMockEvent("TestEvent", "test-aggregate")
		err = eventBus.Publish(event)
		if err != nil {
			t.Fatalf("Failed to publish event %d: %v", i, err)
		}
	}
	
	// Wait for events to be processed
	time.Sleep(100 * time.Millisecond)
	
	metrics := eventBus.GetMetrics()
	total, successful, failed, handlerExecs, _ := metrics.GetStats()
	
	if total != 3 {
		t.Errorf("Expected 3 total events, got %d", total)
	}
	
	if successful != 3 {
		t.Errorf("Expected 3 successful events, got %d", successful)
	}
	
	if failed != 0 {
		t.Errorf("Expected 0 failed events, got %d", failed)
	}
	
	if handlerExecs != 3 {
		t.Errorf("Expected 3 handler executions, got %d", handlerExecs)
	}
}

func TestEventBus_ListEventTypes(t *testing.T) {
	config := DefaultEventBusConfig()
	eventBus := NewEventBus(config)
	
	handler1 := NewMockHandler("handler-1", []string{"EventA"})
	handler2 := NewMockHandler("handler-2", []string{"EventB"})
	
	err := eventBus.SubscribeToEventType("EventA", handler1)
	if err != nil {
		t.Fatalf("Failed to subscribe handler1: %v", err)
	}
	
	err = eventBus.SubscribeToEventType("EventB", handler2)
	if err != nil {
		t.Fatalf("Failed to subscribe handler2: %v", err)
	}
	
	eventTypes := eventBus.ListEventTypes()
	if len(eventTypes) != 2 {
		t.Errorf("Expected 2 event types, got %d", len(eventTypes))
	}
	
	// Check that both event types are present
	eventTypeMap := make(map[string]bool)
	for _, eventType := range eventTypes {
		eventTypeMap[eventType] = true
	}
	
	if !eventTypeMap["EventA"] || !eventTypeMap["EventB"] {
		t.Error("Expected both EventA and EventB to be in event types list")
	}
}

func TestEventBus_PublishWhenNotStarted(t *testing.T) {
	config := DefaultEventBusConfig()
	eventBus := NewEventBus(config)
	
	event := NewMockEvent("TestEvent", "test-aggregate")
	err := eventBus.Publish(event)
	if err == nil {
		t.Error("Publishing event when not started should fail")
	}
}
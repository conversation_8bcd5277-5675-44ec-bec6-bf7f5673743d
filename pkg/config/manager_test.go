package config

import (
	"os"
	"path/filepath"
	"testing"
	"time"
)

func TestConfigManager_LoadAndSaveProfile(t *testing.T) {
	// Create temporary directory for test configs
	tempDir, err := os.MkdirTemp("", "config-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)
	
	cm := NewConfigManager(tempDir)
	
	// Test loading non-existent profile creates default
	err = cm.LoadProfile(Development)
	if err != nil {
		t.Fatalf("Failed to load development profile: %v", err)
	}
	
	// Verify default profile was created
	profile, err := cm.GetProfile(Development)
	if err != nil {
		t.Fatalf("Failed to get development profile: %v", err)
	}
	
	if profile.Environment != Development {
		t.Errorf("Expected environment %s, got %s", Development, profile.Environment)
	}
	
	if profile.Name == "" {
		t.Error("Profile name should not be empty")
	}
	
	// Verify default config structure
	serverConfig, err := cm.GetConfigForEnv(Development, "server.host")
	if err != nil {
		t.Fatalf("Failed to get server.host config: %v", err)
	}
	
	if serverConfig != "localhost" {
		t.Errorf("Expected server.host to be localhost, got %v", serverConfig)
	}
	
	// Test saving profile
	err = cm.SaveProfile(Development)
	if err != nil {
		t.Fatalf("Failed to save development profile: %v", err)
	}
	
	// Verify file was created
	filename := filepath.Join(tempDir, "development.json")
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		t.Error("Config file was not created")
	}
}

func TestConfigManager_GetSetConfig(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "config-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)
	
	cm := NewConfigManager(tempDir)
	cm.LoadProfile(Development)
	cm.SetActiveEnvironment(Development)
	
	// Test getting existing config
	host, err := cm.GetConfig("server.host")
	if err != nil {
		t.Fatalf("Failed to get server.host: %v", err)
	}
	
	if host != "localhost" {
		t.Errorf("Expected localhost, got %v", host)
	}
	
	// Test setting config
	err = cm.SetConfig("server.port", 9000.0)
	if err != nil {
		t.Fatalf("Failed to set server.port: %v", err)
	}
	
	// Verify config was set
	port, err := cm.GetConfig("server.port")
	if err != nil {
		t.Fatalf("Failed to get server.port: %v", err)
	}
	
	if port != 9000.0 {
		t.Errorf("Expected 9000.0, got %v", port)
	}
	
	// Test setting nested config
	err = cm.SetConfig("new.nested.value", "test")
	if err != nil {
		t.Fatalf("Failed to set nested config: %v", err)
	}
	
	value, err := cm.GetConfig("new.nested.value")
	if err != nil {
		t.Fatalf("Failed to get nested config: %v", err)
	}
	
	if value != "test" {
		t.Errorf("Expected 'test', got %v", value)
	}
}

func TestConfigManager_Validation(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "config-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)
	
	cm := NewConfigManager(tempDir)
	cm.LoadProfile(Development)
	cm.SetActiveEnvironment(Development)
	
	// Add validators
	cm.AddValidator("server.port", ValidatePort)
	cm.AddValidator("server.host", ValidateHost)
	cm.AddValidator("logging.level", ValidateLogLevel)
	
	// Test valid values
	err = cm.SetConfig("server.port", 8080.0)
	if err != nil {
		t.Errorf("Valid port should not fail validation: %v", err)
	}
	
	err = cm.SetConfig("server.host", "example.com")
	if err != nil {
		t.Errorf("Valid host should not fail validation: %v", err)
	}
	
	err = cm.SetConfig("logging.level", "debug")
	if err != nil {
		t.Errorf("Valid log level should not fail validation: %v", err)
	}
	
	// Test invalid values
	err = cm.SetConfig("server.port", 99999.0)
	if err == nil {
		t.Error("Invalid port should fail validation")
	}
	
	err = cm.SetConfig("server.host", "")
	if err == nil {
		t.Error("Empty host should fail validation")
	}
	
	err = cm.SetConfig("logging.level", "invalid")
	if err == nil {
		t.Error("Invalid log level should fail validation")
	}
	
	// Test validate all
	err = cm.ValidateAll()
	if err != nil {
		t.Errorf("ValidateAll should pass: %v", err)
	}
}

func TestConfigManager_MultipleEnvironments(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "config-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)
	
	cm := NewConfigManager(tempDir)
	
	// Load multiple environments
	environments := []Environment{Development, Testing, Staging, Production}
	for _, env := range environments {
		err = cm.LoadProfile(env)
		if err != nil {
			t.Fatalf("Failed to load %s profile: %v", env, err)
		}
	}
	
	// Verify different default configs
	devDebug, _ := cm.GetConfigForEnv(Development, "features.debug_mode")
	prodDebug, _ := cm.GetConfigForEnv(Production, "features.debug_mode")
	
	if devDebug != true {
		t.Error("Development should have debug mode enabled")
	}
	
	if prodDebug != false {
		t.Error("Production should have debug mode disabled")
	}
	
	// Test environment switching
	cm.SetActiveEnvironment(Development)
	if cm.GetActiveEnvironment() != Development {
		t.Error("Active environment should be Development")
	}
	
	cm.SetActiveEnvironment(Production)
	if cm.GetActiveEnvironment() != Production {
		t.Error("Active environment should be Production")
	}
	
	// Test independent configs
	cm.SetConfigForEnv(Development, "test.value", "dev")
	cm.SetConfigForEnv(Production, "test.value", "prod")
	
	devValue, _ := cm.GetConfigForEnv(Development, "test.value")
	prodValue, _ := cm.GetConfigForEnv(Production, "test.value")
	
	if devValue != "dev" {
		t.Errorf("Expected 'dev', got %v", devValue)
	}
	
	if prodValue != "prod" {
		t.Errorf("Expected 'prod', got %v", prodValue)
	}
}

func TestConfigManager_CloneProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "config-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)
	
	cm := NewConfigManager(tempDir)
	cm.LoadProfile(Development)
	
	// Modify development config
	cm.SetConfigForEnv(Development, "custom.setting", "dev-value")
	
	// Clone development to staging
	err = cm.CloneProfile(Development, Staging, "Staging Clone")
	if err != nil {
		t.Fatalf("Failed to clone profile: %v", err)
	}
	
	// Verify cloned config
	stagingValue, err := cm.GetConfigForEnv(Staging, "custom.setting")
	if err != nil {
		t.Fatalf("Failed to get staging config: %v", err)
	}
	
	if stagingValue != "dev-value" {
		t.Errorf("Expected 'dev-value', got %v", stagingValue)
	}
	
	// Verify independence
	cm.SetConfigForEnv(Staging, "custom.setting", "staging-value")
	
	devValue, _ := cm.GetConfigForEnv(Development, "custom.setting")
	stagingValue, _ = cm.GetConfigForEnv(Staging, "custom.setting")
	
	if devValue != "dev-value" {
		t.Errorf("Development value should remain 'dev-value', got %v", devValue)
	}
	
	if stagingValue != "staging-value" {
		t.Errorf("Staging value should be 'staging-value', got %v", stagingValue)
	}
}

func TestConfigManager_Watchers(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "config-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)
	
	cm := NewConfigManager(tempDir)
	cm.LoadProfile(Development)
	cm.SetActiveEnvironment(Development)
	
	// Create test watcher
	watcher := &TestConfigWatcher{
		name:    "test-watcher",
		changes: make([]ConfigChange, 0),
	}
	
	cm.AddWatcher(watcher)
	
	// Trigger config change
	cm.SetConfig("test.key", "new-value")
	
	// Allow some time for async notification
	time.Sleep(10 * time.Millisecond)
	
	// Verify watcher was notified (would be more robust with proper synchronization)
	if len(watcher.changes) == 0 {
		t.Error("Watcher should have been notified of config change")
	}
	
	// Test removing watcher
	cm.RemoveWatcher("test-watcher")
	
	// Trigger another change
	cm.SetConfig("test.key2", "another-value")
	time.Sleep(10 * time.Millisecond)
	
	// Changes count should remain the same
	if len(watcher.changes) > 1 {
		t.Error("Watcher should have been removed")
	}
}

func TestConfigManager_Encryption(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "config-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)
	
	cm := NewConfigManager(tempDir)
	cm.SetEncryptionKey([]byte("test-key-123"))
	
	// Load profile and mark a field as encrypted
	cm.LoadProfile(Development)
	profile, _ := cm.GetProfile(Development)
	profile.Encrypted = []string{"database.password"}
	
	// Set sensitive value
	cm.SetConfigForEnv(Development, "database.password", "secret-password")
	
	// Save profile (should encrypt)
	err = cm.SaveProfile(Development)
	if err != nil {
		t.Fatalf("Failed to save profile: %v", err)
	}
	
	// Load again (should decrypt)
	cm2 := NewConfigManager(tempDir)
	cm2.SetEncryptionKey([]byte("test-key-123"))
	err = cm2.LoadProfile(Development)
	if err != nil {
		t.Fatalf("Failed to load profile: %v", err)
	}
	
	// Verify value is available
	password, err := cm2.GetConfigForEnv(Development, "database.password")
	if err != nil {
		t.Fatalf("Failed to get encrypted config: %v", err)
	}
	
	if password != "secret-password" {
		t.Errorf("Expected 'secret-password', got %v", password)
	}
}

func TestBuiltInValidators(t *testing.T) {
	// Test ValidatePort
	if err := ValidatePort(8080.0); err != nil {
		t.Errorf("Valid port should pass: %v", err)
	}
	
	if err := ValidatePort(99999.0); err == nil {
		t.Error("Invalid port should fail")
	}
	
	if err := ValidatePort("invalid"); err == nil {
		t.Error("Non-numeric port should fail")
	}
	
	// Test ValidateHost
	if err := ValidateHost("localhost"); err != nil {
		t.Errorf("Valid host should pass: %v", err)
	}
	
	if err := ValidateHost(""); err == nil {
		t.Error("Empty host should fail")
	}
	
	if err := ValidateHost(123); err == nil {
		t.Error("Non-string host should fail")
	}
	
	// Test ValidateLogLevel
	if err := ValidateLogLevel("debug"); err != nil {
		t.Errorf("Valid log level should pass: %v", err)
	}
	
	if err := ValidateLogLevel("invalid"); err == nil {
		t.Error("Invalid log level should fail")
	}
	
	// Test ValidateRequired
	if err := ValidateRequired("value"); err != nil {
		t.Errorf("Non-empty value should pass: %v", err)
	}
	
	if err := ValidateRequired(""); err == nil {
		t.Error("Empty string should fail")
	}
	
	if err := ValidateRequired(nil); err == nil {
		t.Error("Nil value should fail")
	}
	
	// Test ValidateRange
	rangeValidator := ValidateRange(1, 10)
	
	if err := rangeValidator(5.0); err != nil {
		t.Errorf("Value in range should pass: %v", err)
	}
	
	if err := rangeValidator(15.0); err == nil {
		t.Error("Value out of range should fail")
	}
	
	// Test ValidateEnum
	enumValidator := ValidateEnum("dev", "test", "prod")
	
	if err := enumValidator("dev"); err != nil {
		t.Errorf("Valid enum value should pass: %v", err)
	}
	
	if err := enumValidator("invalid"); err == nil {
		t.Error("Invalid enum value should fail")
	}
}

// Test helper types

type ConfigChange struct {
	Environment Environment
	OldConfig   map[string]interface{}
	NewConfig   map[string]interface{}
}

type TestConfigWatcher struct {
	name    string
	changes []ConfigChange
}

func (w *TestConfigWatcher) OnConfigChange(env Environment, oldConfig, newConfig map[string]interface{}) error {
	w.changes = append(w.changes, ConfigChange{
		Environment: env,
		OldConfig:   oldConfig,
		NewConfig:   newConfig,
	})
	return nil
}

func (w *TestConfigWatcher) WatcherName() string {
	return w.name
}
// Package config provides environment-specific configuration management
package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"sync"
	"time"
)

// Environment represents different deployment environments
type Environment string

const (
	Development Environment = "development"
	Testing     Environment = "testing"
	Staging     Environment = "staging"
	Production  Environment = "production"
)

// ConfigProfile represents a configuration profile for a specific environment
type ConfigProfile struct {
	Name        string                 `json:"name"`
	Environment Environment            `json:"environment"`
	Version     string                 `json:"version"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Config      map[string]interface{} `json:"config"`
	Metadata    map[string]string      `json:"metadata"`
	Encrypted   []string               `json:"encrypted,omitempty"` // List of encrypted field paths
}

// ConfigManager manages environment-specific configuration profiles
type ConfigManager struct {
	profiles     map[Environment]*ConfigProfile
	activeEnv    Environment
	configDir    string
	mutex        sync.RWMutex
	validators   map[string]ValidatorFunc
	watchers     []ConfigWatcher
	encryptionKey []byte
}

// ValidatorFunc represents a configuration validation function
type ValidatorFunc func(value interface{}) error

// ConfigWatcher watches for configuration changes
type ConfigWatcher interface {
	OnConfigChange(env Environment, oldConfig, newConfig map[string]interface{}) error
	WatcherName() string
}

// ConfigChangeEvent represents a configuration change event
type ConfigChangeEvent struct {
	Environment Environment            `json:"environment"`
	Profile     string                 `json:"profile"`
	Field       string                 `json:"field"`
	OldValue    interface{}            `json:"old_value,omitempty"`
	NewValue    interface{}            `json:"new_value"`
	Timestamp   time.Time              `json:"timestamp"`
	Source      string                 `json:"source"`
}

// NewConfigManager creates a new configuration manager
func NewConfigManager(configDir string) *ConfigManager {
	return &ConfigManager{
		profiles:   make(map[Environment]*ConfigProfile),
		activeEnv:  Development,
		configDir:  configDir,
		validators: make(map[string]ValidatorFunc),
		watchers:   make([]ConfigWatcher, 0),
	}
}

// SetEncryptionKey sets the encryption key for sensitive configuration values
func (cm *ConfigManager) SetEncryptionKey(key []byte) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	cm.encryptionKey = key
}

// LoadProfile loads a configuration profile from file
func (cm *ConfigManager) LoadProfile(env Environment) error {
	filename := filepath.Join(cm.configDir, fmt.Sprintf("%s.json", env))
	data, err := os.ReadFile(filename)
	if err != nil {
		if os.IsNotExist(err) {
			// Create default profile if it doesn't exist
			return cm.createDefaultProfile(env)
		}
		return fmt.Errorf("failed to read config file %s: %w", filename, err)
	}
	
	var profile ConfigProfile
	if err := json.Unmarshal(data, &profile); err != nil {
		return fmt.Errorf("failed to unmarshal config profile: %w", err)
	}
	
	// Decrypt sensitive values
	if err := cm.decryptProfile(&profile); err != nil {
		return fmt.Errorf("failed to decrypt profile: %w", err)
	}
	
	// Now acquire the lock to update the in-memory profile
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	cm.profiles[env] = &profile
	return nil
}

// SaveProfile saves a configuration profile to file
func (cm *ConfigManager) SaveProfile(env Environment) error {
	cm.mutex.RLock()
	profile, exists := cm.profiles[env]
	if !exists {
		cm.mutex.RUnlock()
		return fmt.Errorf("profile for environment %s not found", env)
	}
	
	// Create a copy for encryption
	profileCopy := *profile
	profileCopy.Config = make(map[string]interface{})
	for k, v := range profile.Config {
		profileCopy.Config[k] = v
	}
	cm.mutex.RUnlock()
	
	// Encrypt sensitive values
	if err := cm.encryptProfile(&profileCopy); err != nil {
		return fmt.Errorf("failed to encrypt profile: %w", err)
	}
	
	profileCopy.UpdatedAt = time.Now()
	
	data, err := json.MarshalIndent(profileCopy, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config profile: %w", err)
	}
	
	filename := filepath.Join(cm.configDir, fmt.Sprintf("%s.json", env))
	if err := os.MkdirAll(cm.configDir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}
	
	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}
	
	return nil
}

// SetActiveEnvironment sets the active environment
func (cm *ConfigManager) SetActiveEnvironment(env Environment) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	if _, exists := cm.profiles[env]; !exists {
		return fmt.Errorf("profile for environment %s not loaded", env)
	}
	
	oldEnv := cm.activeEnv
	cm.activeEnv = env
	
	// Notify watchers of environment change
	go cm.notifyWatchers(oldEnv, env)
	
	return nil
}

// GetActiveEnvironment returns the current active environment
func (cm *ConfigManager) GetActiveEnvironment() Environment {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.activeEnv
}

// GetConfig gets a configuration value from the active environment
func (cm *ConfigManager) GetConfig(key string) (interface{}, error) {
	return cm.GetConfigForEnv(cm.GetActiveEnvironment(), key)
}

// GetConfigForEnv gets a configuration value for a specific environment
func (cm *ConfigManager) GetConfigForEnv(env Environment, key string) (interface{}, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	profile, exists := cm.profiles[env]
	if !exists {
		return nil, fmt.Errorf("profile for environment %s not found", env)
	}
	
	return cm.getNestedValue(profile.Config, key)
}

// SetConfig sets a configuration value in the active environment
func (cm *ConfigManager) SetConfig(key string, value interface{}) error {
	return cm.SetConfigForEnv(cm.GetActiveEnvironment(), key, value)
}

// SetConfigForEnv sets a configuration value for a specific environment
func (cm *ConfigManager) SetConfigForEnv(env Environment, key string, value interface{}) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	profile, exists := cm.profiles[env]
	if !exists {
		return fmt.Errorf("profile for environment %s not found", env)
	}
	
	// Validate the value
	if validator, exists := cm.validators[key]; exists {
		if err := validator(value); err != nil {
			return fmt.Errorf("validation failed for key %s: %w", key, err)
		}
	}
	
	oldValue, _ := cm.getNestedValue(profile.Config, key)
	
	// Set the new value
	if err := cm.setNestedValue(profile.Config, key, value); err != nil {
		return fmt.Errorf("failed to set config value: %w", err)
	}
	
	profile.UpdatedAt = time.Now()
	
	// Notify watchers of the change
	go cm.notifyConfigChange(env, key, oldValue, value)
	
	return nil
}

// GetAllConfigs returns all configuration for the active environment
func (cm *ConfigManager) GetAllConfigs() map[string]interface{} {
	return cm.GetAllConfigsForEnv(cm.GetActiveEnvironment())
}

// GetAllConfigsForEnv returns all configuration for a specific environment
func (cm *ConfigManager) GetAllConfigsForEnv(env Environment) map[string]interface{} {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	profile, exists := cm.profiles[env]
	if !exists {
		return make(map[string]interface{})
	}
	
	// Return a copy to prevent external modification
	result := make(map[string]interface{})
	cm.deepCopy(profile.Config, result)
	return result
}

// AddValidator adds a validator for a configuration key
func (cm *ConfigManager) AddValidator(key string, validator ValidatorFunc) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	cm.validators[key] = validator
}

// RemoveValidator removes a validator for a configuration key
func (cm *ConfigManager) RemoveValidator(key string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	delete(cm.validators, key)
}

// AddWatcher adds a configuration change watcher
func (cm *ConfigManager) AddWatcher(watcher ConfigWatcher) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	cm.watchers = append(cm.watchers, watcher)
}

// RemoveWatcher removes a configuration change watcher
func (cm *ConfigManager) RemoveWatcher(watcherName string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	for i, watcher := range cm.watchers {
		if watcher.WatcherName() == watcherName {
			cm.watchers = append(cm.watchers[:i], cm.watchers[i+1:]...)
			break
		}
	}
}

// ValidateAll validates all configuration values in the active environment
func (cm *ConfigManager) ValidateAll() error {
	return cm.ValidateAllForEnv(cm.GetActiveEnvironment())
}

// ValidateAllForEnv validates all configuration values for a specific environment
func (cm *ConfigManager) ValidateAllForEnv(env Environment) error {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	profile, exists := cm.profiles[env]
	if !exists {
		return fmt.Errorf("profile for environment %s not found", env)
	}
	
	return cm.validateConfig(profile.Config, "")
}

// GetProfile returns the profile for a specific environment
func (cm *ConfigManager) GetProfile(env Environment) (*ConfigProfile, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	profile, exists := cm.profiles[env]
	if !exists {
		return nil, fmt.Errorf("profile for environment %s not found", env)
	}
	
	// Return a copy
	profileCopy := *profile
	profileCopy.Config = make(map[string]interface{})
	cm.deepCopy(profile.Config, profileCopy.Config)
	
	return &profileCopy, nil
}

// ListEnvironments returns all loaded environments
func (cm *ConfigManager) ListEnvironments() []Environment {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	envs := make([]Environment, 0, len(cm.profiles))
	for env := range cm.profiles {
		envs = append(envs, env)
	}
	
	return envs
}

// CloneProfile creates a new profile based on an existing one
func (cm *ConfigManager) CloneProfile(sourceEnv, targetEnv Environment, newName string) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	sourceProfile, exists := cm.profiles[sourceEnv]
	if !exists {
		return fmt.Errorf("source profile for environment %s not found", sourceEnv)
	}
	
	targetProfile := ConfigProfile{
		Name:        newName,
		Environment: targetEnv,
		Version:     "1.0.0",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Config:      make(map[string]interface{}),
		Metadata:    make(map[string]string),
		Encrypted:   make([]string, len(sourceProfile.Encrypted)),
	}
	
	// Deep copy config and metadata
	cm.deepCopy(sourceProfile.Config, targetProfile.Config)
	for k, v := range sourceProfile.Metadata {
		targetProfile.Metadata[k] = v
	}
	copy(targetProfile.Encrypted, sourceProfile.Encrypted)
	
	cm.profiles[targetEnv] = &targetProfile
	return nil
}

// Helper methods

func (cm *ConfigManager) createDefaultProfile(env Environment) error {
	profile := ConfigProfile{
		Name:        fmt.Sprintf("Default %s Profile", env),
		Environment: env,
		Version:     "1.0.0",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Config:      cm.getDefaultConfig(env),
		Metadata:    make(map[string]string),
		Encrypted:   []string{},
	}
	
	// Encrypt sensitive values before saving
	if err := cm.encryptProfile(&profile); err != nil {
		return fmt.Errorf("failed to encrypt profile: %w", err)
	}
	
	// Save to file first
	data, err := json.MarshalIndent(profile, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config profile: %w", err)
	}
	
	filename := filepath.Join(cm.configDir, fmt.Sprintf("%s.json", env))
	if err := os.MkdirAll(cm.configDir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}
	
	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}
	
	// Now decrypt for in-memory storage and set it with proper locking
	if err := cm.decryptProfile(&profile); err != nil {
		return fmt.Errorf("failed to decrypt profile: %w", err)
	}
	
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	cm.profiles[env] = &profile
	
	return nil
}

func (cm *ConfigManager) getDefaultConfig(env Environment) map[string]interface{} {
	config := map[string]interface{}{
		"server": map[string]interface{}{
			"host": "localhost",
			"port": 8080,
		},
		"database": map[string]interface{}{
			"host":     "localhost",
			"port":     5432,
			"name":     "termillm",
			"ssl_mode": "disable",
		},
		"logging": map[string]interface{}{
			"level":  "info",
			"format": "json",
		},
		"features": map[string]interface{}{
			"debug_mode":      env == Development,
			"metrics_enabled": true,
		},
	}
	
	// Environment-specific overrides
	switch env {
	case Development:
		config["logging"].(map[string]interface{})["level"] = "debug"
		config["features"].(map[string]interface{})["debug_mode"] = true
	case Testing:
		config["database"].(map[string]interface{})["name"] = "termillm_test"
		config["logging"].(map[string]interface{})["level"] = "warn"
	case Production:
		config["server"].(map[string]interface{})["host"] = "0.0.0.0"
		config["database"].(map[string]interface{})["ssl_mode"] = "require"
		config["logging"].(map[string]interface{})["level"] = "info"
		config["features"].(map[string]interface{})["debug_mode"] = false
	}
	
	return config
}

func (cm *ConfigManager) getNestedValue(config map[string]interface{}, key string) (interface{}, error) {
	parts := strings.Split(key, ".")
	current := config
	
	for i, part := range parts {
		if i == len(parts)-1 {
			if value, exists := current[part]; exists {
				return value, nil
			}
			return nil, fmt.Errorf("key %s not found", key)
		}
		
		if next, exists := current[part]; exists {
			if nextMap, ok := next.(map[string]interface{}); ok {
				current = nextMap
			} else {
				return nil, fmt.Errorf("intermediate key %s is not a map", part)
			}
		} else {
			return nil, fmt.Errorf("intermediate key %s not found", part)
		}
	}
	
	return nil, fmt.Errorf("key %s not found", key)
}

func (cm *ConfigManager) setNestedValue(config map[string]interface{}, key string, value interface{}) error {
	parts := strings.Split(key, ".")
	current := config
	
	for i, part := range parts {
		if i == len(parts)-1 {
			current[part] = value
			return nil
		}
		
		if next, exists := current[part]; exists {
			if nextMap, ok := next.(map[string]interface{}); ok {
				current = nextMap
			} else {
				return fmt.Errorf("intermediate key %s is not a map", part)
			}
		} else {
			// Create intermediate map
			newMap := make(map[string]interface{})
			current[part] = newMap
			current = newMap
		}
	}
	
	return nil
}

func (cm *ConfigManager) deepCopy(src, dst map[string]interface{}) {
	for k, v := range src {
		if srcMap, ok := v.(map[string]interface{}); ok {
			dstMap := make(map[string]interface{})
			cm.deepCopy(srcMap, dstMap)
			dst[k] = dstMap
		} else if srcSlice, ok := v.([]interface{}); ok {
			dstSlice := make([]interface{}, len(srcSlice))
			copy(dstSlice, srcSlice)
			dst[k] = dstSlice
		} else {
			dst[k] = v
		}
	}
}

func (cm *ConfigManager) validateConfig(config map[string]interface{}, prefix string) error {
	for key, value := range config {
		fullKey := key
		if prefix != "" {
			fullKey = prefix + "." + key
		}
		
		if validator, exists := cm.validators[fullKey]; exists {
			if err := validator(value); err != nil {
				return fmt.Errorf("validation failed for %s: %w", fullKey, err)
			}
		}
		
		if nestedMap, ok := value.(map[string]interface{}); ok {
			if err := cm.validateConfig(nestedMap, fullKey); err != nil {
				return err
			}
		}
	}
	
	return nil
}

func (cm *ConfigManager) notifyWatchers(oldEnv, newEnv Environment) {
	for _, watcher := range cm.watchers {
		oldConfig := cm.GetAllConfigsForEnv(oldEnv)
		newConfig := cm.GetAllConfigsForEnv(newEnv)
		
		if err := watcher.OnConfigChange(newEnv, oldConfig, newConfig); err != nil {
			// Log error but continue with other watchers
			fmt.Printf("Config watcher %s error: %v\n", watcher.WatcherName(), err)
		}
	}
}

func (cm *ConfigManager) notifyConfigChange(env Environment, key string, oldValue, newValue interface{}) {
	// This would normally publish to an event bus or notification system
	event := ConfigChangeEvent{
		Environment: env,
		Profile:     cm.profiles[env].Name,
		Field:       key,
		OldValue:    oldValue,
		NewValue:    newValue,
		Timestamp:   time.Now(),
		Source:      "config_manager",
	}
	
	// For now, just log the change
	fmt.Printf("Config change: %+v\n", event)
}

func (cm *ConfigManager) encryptProfile(profile *ConfigProfile) error {
	if len(cm.encryptionKey) == 0 {
		return nil // No encryption key set
	}
	
	for _, fieldPath := range profile.Encrypted {
		value, err := cm.getNestedValue(profile.Config, fieldPath)
		if err != nil {
			continue // Field might not exist
		}
		
		if strValue, ok := value.(string); ok {
			encrypted := cm.simpleEncrypt(strValue)
			cm.setNestedValue(profile.Config, fieldPath, encrypted)
		}
	}
	
	return nil
}

func (cm *ConfigManager) decryptProfile(profile *ConfigProfile) error {
	if len(cm.encryptionKey) == 0 {
		return nil // No encryption key set
	}
	
	for _, fieldPath := range profile.Encrypted {
		value, err := cm.getNestedValue(profile.Config, fieldPath)
		if err != nil {
			continue // Field might not exist
		}
		
		if strValue, ok := value.(string); ok {
			decrypted := cm.simpleDecrypt(strValue)
			cm.setNestedValue(profile.Config, fieldPath, decrypted)
		}
	}
	
	return nil
}

// Simple encryption/decryption (in production, use proper encryption)
func (cm *ConfigManager) simpleEncrypt(value string) string {
	// This is a placeholder - use proper encryption in production
	return "encrypted:" + value
}

func (cm *ConfigManager) simpleDecrypt(value string) string {
	// This is a placeholder - use proper decryption in production
	if strings.HasPrefix(value, "encrypted:") {
		return strings.TrimPrefix(value, "encrypted:")
	}
	return value
}

// Built-in validators

// ValidatePort validates that a value is a valid port number
func ValidatePort(value interface{}) error {
	port, ok := value.(float64)
	if !ok {
		return fmt.Errorf("port must be a number")
	}
	
	if port < 1 || port > 65535 {
		return fmt.Errorf("port must be between 1 and 65535")
	}
	
	return nil
}

// ValidateHost validates that a value is a valid hostname or IP
func ValidateHost(value interface{}) error {
	host, ok := value.(string)
	if !ok {
		return fmt.Errorf("host must be a string")
	}
	
	if host == "" {
		return fmt.Errorf("host cannot be empty")
	}
	
	return nil
}

// ValidateLogLevel validates that a value is a valid log level
func ValidateLogLevel(value interface{}) error {
	level, ok := value.(string)
	if !ok {
		return fmt.Errorf("log level must be a string")
	}
	
	validLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
		"fatal": true,
	}
	
	if !validLevels[strings.ToLower(level)] {
		return fmt.Errorf("invalid log level: %s", level)
	}
	
	return nil
}

// ValidateRequired validates that a value is not nil or empty
func ValidateRequired(value interface{}) error {
	if value == nil {
		return fmt.Errorf("value is required")
	}
	
	if str, ok := value.(string); ok && str == "" {
		return fmt.Errorf("value cannot be empty")
	}
	
	return nil
}

// ValidateRange creates a validator for numeric ranges
func ValidateRange(min, max float64) ValidatorFunc {
	return func(value interface{}) error {
		num, ok := value.(float64)
		if !ok {
			return fmt.Errorf("value must be a number")
		}
		
		if num < min || num > max {
			return fmt.Errorf("value must be between %f and %f", min, max)
		}
		
		return nil
	}
}

// ValidateEnum creates a validator for enumerated values
func ValidateEnum(validValues ...interface{}) ValidatorFunc {
	return func(value interface{}) error {
		for _, valid := range validValues {
			if reflect.DeepEqual(value, valid) {
				return nil
			}
		}
		
		return fmt.Errorf("value must be one of: %v", validValues)
	}
}
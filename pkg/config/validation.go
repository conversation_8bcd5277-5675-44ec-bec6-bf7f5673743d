// Package config provides configuration validation and error handling
package config

import (
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// ValidationError represents a configuration validation error
type ValidationError struct {
	Field   string `json:"field"`
	Value   interface{} `json:"value"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation error for field '%s': %s (value: %v)", e.Field, e.Message, e.Value)
}

// ValidationResult contains the results of configuration validation
type ValidationResult struct {
	Valid  bool              `json:"valid"`
	Errors []ValidationError `json:"errors"`
}

// ConfigValidator provides comprehensive configuration validation
type ConfigValidator struct {
	rules       map[string][]ValidationRule
	constraints map[string][]ConstraintRule
	schema      *ConfigSchema
}

// ValidationRule represents a single validation rule
type ValidationRule struct {
	Name        string
	Description string
	Validator   func(value interface{}) error
	Required    bool
	StopOnError bool
}

// ConstraintRule represents a constraint between multiple configuration values
type ConstraintRule struct {
	Name        string
	Description string
	Fields      []string
	Validator   func(values map[string]interface{}) error
}

// ConfigSchema defines the expected structure and types of configuration
type ConfigSchema struct {
	Properties map[string]PropertyDefinition `json:"properties"`
	Required   []string                      `json:"required"`
	Version    string                        `json:"version"`
}

// PropertyDefinition defines a configuration property
type PropertyDefinition struct {
	Type        string                        `json:"type"`
	Description string                        `json:"description"`
	Default     interface{}                   `json:"default,omitempty"`
	Enum        []interface{}                 `json:"enum,omitempty"`
	Minimum     *float64                      `json:"minimum,omitempty"`
	Maximum     *float64                      `json:"maximum,omitempty"`
	Pattern     string                        `json:"pattern,omitempty"`
	Format      string                        `json:"format,omitempty"`
	Items       *PropertyDefinition           `json:"items,omitempty"`
	Properties  map[string]PropertyDefinition `json:"properties,omitempty"`
	Required    []string                      `json:"required,omitempty"`
}

// NewConfigValidator creates a new configuration validator
func NewConfigValidator() *ConfigValidator {
	return &ConfigValidator{
		rules:       make(map[string][]ValidationRule),
		constraints: make(map[string][]ConstraintRule),
	}
}

// AddRule adds a validation rule for a specific field
func (cv *ConfigValidator) AddRule(field string, rule ValidationRule) {
	cv.rules[field] = append(cv.rules[field], rule)
}

// AddConstraint adds a constraint rule across multiple fields
func (cv *ConfigValidator) AddConstraint(name string, constraint ConstraintRule) {
	cv.constraints[name] = append(cv.constraints[name], constraint)
}

// SetSchema sets the configuration schema
func (cv *ConfigValidator) SetSchema(schema *ConfigSchema) {
	cv.schema = schema
}

// ValidateConfig validates a complete configuration
func (cv *ConfigValidator) ValidateConfig(config map[string]interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:  true,
		Errors: make([]ValidationError, 0),
	}
	
	// Schema validation
	if cv.schema != nil {
		cv.validateSchema(config, cv.schema, "", result)
	}
	
	// Field-specific rule validation
	cv.validateRules(config, "", result)
	
	// Constraint validation
	cv.validateConstraints(config, result)
	
	result.Valid = len(result.Errors) == 0
	return result
}

// ValidateField validates a specific field
func (cv *ConfigValidator) ValidateField(field string, value interface{}) *ValidationResult {
	result := &ValidationResult{
		Valid:  true,
		Errors: make([]ValidationError, 0),
	}
	
	// Schema validation for field
	if cv.schema != nil {
		cv.validateFieldSchema(field, value, cv.schema, result)
	}
	
	// Rule validation for field
	if rules, exists := cv.rules[field]; exists {
		for _, rule := range rules {
			if err := rule.Validator(value); err != nil {
				result.Errors = append(result.Errors, ValidationError{
					Field:   field,
					Value:   value,
					Message: err.Error(),
					Code:    rule.Name,
				})
				
				if rule.StopOnError {
					break
				}
			}
		}
	}
	
	result.Valid = len(result.Errors) == 0
	return result
}

// Schema validation methods

func (cv *ConfigValidator) validateSchema(config map[string]interface{}, schema *ConfigSchema, prefix string, result *ValidationResult) {
	// Check required fields
	for _, required := range schema.Required {
		fullField := required
		if prefix != "" {
			fullField = prefix + "." + required
		}
		
		if _, exists := cv.getNestedValue(config, fullField); !exists {
			result.Errors = append(result.Errors, ValidationError{
				Field:   fullField,
				Value:   nil,
				Message: "required field is missing",
				Code:    "required",
			})
		}
	}
	
	// Validate each property
	for propName, propDef := range schema.Properties {
		fullField := propName
		if prefix != "" {
			fullField = prefix + "." + propName
		}
		
		value, exists := cv.getNestedValue(config, fullField)
		if !exists {
			continue
		}
		
		cv.validateProperty(fullField, value, propDef, result)
	}
}

func (cv *ConfigValidator) validateFieldSchema(field string, value interface{}, schema *ConfigSchema, result *ValidationResult) {
	parts := strings.Split(field, ".")
	current := schema.Properties
	
	for i, part := range parts {
		if propDef, exists := current[part]; exists {
			if i == len(parts)-1 {
				// This is the final field, validate it
				cv.validateProperty(field, value, propDef, result)
				return
			} else {
				// Navigate deeper
				if propDef.Properties != nil {
					current = propDef.Properties
				} else {
					return // Can't go deeper
				}
			}
		} else {
			return // Field not in schema
		}
	}
}

func (cv *ConfigValidator) validateProperty(field string, value interface{}, propDef PropertyDefinition, result *ValidationResult) {
	// Type validation
	if !cv.validateType(value, propDef.Type) {
		result.Errors = append(result.Errors, ValidationError{
			Field:   field,
			Value:   value,
			Message: fmt.Sprintf("expected type %s", propDef.Type),
			Code:    "type_mismatch",
		})
		return
	}
	
	// Enum validation
	if len(propDef.Enum) > 0 {
		if !cv.validateEnum(value, propDef.Enum) {
			result.Errors = append(result.Errors, ValidationError{
				Field:   field,
				Value:   value,
				Message: fmt.Sprintf("value must be one of: %v", propDef.Enum),
				Code:    "enum_violation",
			})
		}
	}
	
	// Numeric range validation
	if propDef.Type == "number" {
		if num, ok := value.(float64); ok {
			if propDef.Minimum != nil && num < *propDef.Minimum {
				result.Errors = append(result.Errors, ValidationError{
					Field:   field,
					Value:   value,
					Message: fmt.Sprintf("value must be >= %f", *propDef.Minimum),
					Code:    "minimum_violation",
				})
			}
			
			if propDef.Maximum != nil && num > *propDef.Maximum {
				result.Errors = append(result.Errors, ValidationError{
					Field:   field,
					Value:   value,
					Message: fmt.Sprintf("value must be <= %f", *propDef.Maximum),
					Code:    "maximum_violation",
				})
			}
		}
	}
	
	// Pattern validation
	if propDef.Pattern != "" && propDef.Type == "string" {
		if str, ok := value.(string); ok {
			if matched, err := regexp.MatchString(propDef.Pattern, str); err != nil || !matched {
				result.Errors = append(result.Errors, ValidationError{
					Field:   field,
					Value:   value,
					Message: fmt.Sprintf("value must match pattern: %s", propDef.Pattern),
					Code:    "pattern_violation",
				})
			}
		}
	}
	
	// Format validation
	if propDef.Format != "" {
		if err := cv.validateFormat(value, propDef.Format); err != nil {
			result.Errors = append(result.Errors, ValidationError{
				Field:   field,
				Value:   value,
				Message: err.Error(),
				Code:    "format_violation",
			})
		}
	}
	
	// Object validation
	if propDef.Type == "object" && propDef.Properties != nil {
		if objMap, ok := value.(map[string]interface{}); ok {
			subSchema := &ConfigSchema{
				Properties: propDef.Properties,
				Required:   propDef.Required,
			}
			cv.validateSchema(map[string]interface{}{field: objMap}, subSchema, "", result)
		}
	}
	
	// Array validation
	if propDef.Type == "array" && propDef.Items != nil {
		if arr, ok := value.([]interface{}); ok {
			for i, item := range arr {
				itemField := fmt.Sprintf("%s[%d]", field, i)
				cv.validateProperty(itemField, item, *propDef.Items, result)
			}
		}
	}
}

func (cv *ConfigValidator) validateType(value interface{}, expectedType string) bool {
	switch expectedType {
	case "string":
		_, ok := value.(string)
		return ok
	case "number":
		_, ok := value.(float64)
		return ok
	case "integer":
		if num, ok := value.(float64); ok {
			return num == float64(int(num))
		}
		return false
	case "boolean":
		_, ok := value.(bool)
		return ok
	case "array":
		_, ok := value.([]interface{})
		return ok
	case "object":
		_, ok := value.(map[string]interface{})
		return ok
	case "null":
		return value == nil
	default:
		return true // Unknown type, assume valid
	}
}

func (cv *ConfigValidator) validateEnum(value interface{}, enum []interface{}) bool {
	for _, enumValue := range enum {
		if reflect.DeepEqual(value, enumValue) {
			return true
		}
	}
	return false
}

func (cv *ConfigValidator) validateFormat(value interface{}, format string) error {
	str, ok := value.(string)
	if !ok {
		return fmt.Errorf("format validation requires string value")
	}
	
	switch format {
	case "email":
		return cv.validateEmail(str)
	case "uri", "url":
		return cv.validateURL(str)
	case "ipv4":
		return cv.validateIPv4(str)
	case "ipv6":
		return cv.validateIPv6(str)
	case "hostname":
		return cv.validateHostname(str)
	case "date":
		return cv.validateDate(str)
	case "time":
		return cv.validateTime(str)
	case "datetime":
		return cv.validateDateTime(str)
	case "duration":
		return cv.validateDuration(str)
	default:
		return nil // Unknown format, assume valid
	}
}

func (cv *ConfigValidator) validateEmail(email string) error {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(email) {
		return fmt.Errorf("invalid email format")
	}
	return nil
}

func (cv *ConfigValidator) validateURL(url string) error {
	urlRegex := regexp.MustCompile(`^https?://[^\s/$.?#].[^\s]*$`)
	if !urlRegex.MatchString(url) {
		return fmt.Errorf("invalid URL format")
	}
	return nil
}

func (cv *ConfigValidator) validateIPv4(ip string) error {
	ipRegex := regexp.MustCompile(`^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`)
	if !ipRegex.MatchString(ip) {
		return fmt.Errorf("invalid IPv4 address format")
	}
	return nil
}

func (cv *ConfigValidator) validateIPv6(ip string) error {
	ipv6Regex := regexp.MustCompile(`^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$`)
	if !ipv6Regex.MatchString(ip) {
		return fmt.Errorf("invalid IPv6 address format")
	}
	return nil
}

func (cv *ConfigValidator) validateHostname(hostname string) error {
	hostnameRegex := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$`)
	if !hostnameRegex.MatchString(hostname) {
		return fmt.Errorf("invalid hostname format")
	}
	return nil
}

func (cv *ConfigValidator) validateDate(date string) error {
	_, err := time.Parse("2006-01-02", date)
	if err != nil {
		return fmt.Errorf("invalid date format, expected YYYY-MM-DD")
	}
	return nil
}

func (cv *ConfigValidator) validateTime(timeStr string) error {
	_, err := time.Parse("15:04:05", timeStr)
	if err != nil {
		return fmt.Errorf("invalid time format, expected HH:MM:SS")
	}
	return nil
}

func (cv *ConfigValidator) validateDateTime(datetime string) error {
	_, err := time.Parse(time.RFC3339, datetime)
	if err != nil {
		return fmt.Errorf("invalid datetime format, expected RFC3339")
	}
	return nil
}

func (cv *ConfigValidator) validateDuration(duration string) error {
	_, err := time.ParseDuration(duration)
	if err != nil {
		return fmt.Errorf("invalid duration format")
	}
	return nil
}

// Rule validation methods

func (cv *ConfigValidator) validateRules(config map[string]interface{}, prefix string, result *ValidationResult) {
	for field, rules := range cv.rules {
		value, exists := cv.getNestedValue(config, field)
		
		for _, rule := range rules {
			if rule.Required && !exists {
				result.Errors = append(result.Errors, ValidationError{
					Field:   field,
					Value:   nil,
					Message: "required field is missing",
					Code:    rule.Name,
				})
				continue
			}
			
			if !exists {
				continue // Skip validation for optional missing fields
			}
			
			if err := rule.Validator(value); err != nil {
				result.Errors = append(result.Errors, ValidationError{
					Field:   field,
					Value:   value,
					Message: err.Error(),
					Code:    rule.Name,
				})
				
				if rule.StopOnError {
					break
				}
			}
		}
	}
}

// Constraint validation methods

func (cv *ConfigValidator) validateConstraints(config map[string]interface{}, result *ValidationResult) {
	for _, constraints := range cv.constraints {
		for _, constraint := range constraints {
			values := make(map[string]interface{})
			allFieldsExist := true
			
			// Collect values for all fields in the constraint
			for _, field := range constraint.Fields {
				if value, exists := cv.getNestedValue(config, field); exists {
					values[field] = value
				} else {
					allFieldsExist = false
					break
				}
			}
			
			if !allFieldsExist {
				continue // Skip constraint if not all fields exist
			}
			
			if err := constraint.Validator(values); err != nil {
				result.Errors = append(result.Errors, ValidationError{
					Field:   strings.Join(constraint.Fields, ", "),
					Value:   values,
					Message: err.Error(),
					Code:    constraint.Name,
				})
			}
		}
	}
}

// Helper methods

func (cv *ConfigValidator) getNestedValue(config map[string]interface{}, key string) (interface{}, bool) {
	parts := strings.Split(key, ".")
	current := config
	
	for i, part := range parts {
		if i == len(parts)-1 {
			if value, exists := current[part]; exists {
				return value, true
			}
			return nil, false
		}
		
		if next, exists := current[part]; exists {
			if nextMap, ok := next.(map[string]interface{}); ok {
				current = nextMap
			} else {
				return nil, false
			}
		} else {
			return nil, false
		}
	}
	
	return nil, false
}

// Built-in validation rules

// CreateDatabaseConnectionRule validates database connection parameters
func CreateDatabaseConnectionRule() ValidationRule {
	return ValidationRule{
		Name:        "database_connection",
		Description: "Validates database connection parameters",
		Validator: func(value interface{}) error {
			config, ok := value.(map[string]interface{})
			if !ok {
				return fmt.Errorf("database config must be an object")
			}
			
			// Check required fields
			required := []string{"host", "port", "name"}
			for _, field := range required {
				if _, exists := config[field]; !exists {
					return fmt.Errorf("missing required field: %s", field)
				}
			}
			
			// Validate port
			if port, exists := config["port"]; exists {
				if portNum, ok := port.(float64); ok {
					if portNum < 1 || portNum > 65535 {
						return fmt.Errorf("port must be between 1 and 65535")
					}
				} else {
					return fmt.Errorf("port must be a number")
				}
			}
			
			return nil
		},
		Required:    true,
		StopOnError: true,
	}
}

// CreateServerConfigRule validates server configuration
func CreateServerConfigRule() ValidationRule {
	return ValidationRule{
		Name:        "server_config",
		Description: "Validates server configuration parameters",
		Validator: func(value interface{}) error {
			config, ok := value.(map[string]interface{})
			if !ok {
				return fmt.Errorf("server config must be an object")
			}
			
			// Validate host
			if host, exists := config["host"]; exists {
				if hostStr, ok := host.(string); ok {
					if hostStr == "" {
						return fmt.Errorf("host cannot be empty")
					}
				} else {
					return fmt.Errorf("host must be a string")
				}
			}
			
			// Validate port
			if port, exists := config["port"]; exists {
				if err := ValidatePort(port); err != nil {
					return err
				}
			}
			
			return nil
		},
		Required:    true,
		StopOnError: true,
	}
}

// CreateSecurityConstraint validates security-related constraints
func CreateSecurityConstraint() ConstraintRule {
	return ConstraintRule{
		Name:        "security_constraint",
		Description: "Ensures security settings are consistent",
		Fields:      []string{"features.debug_mode", "logging.level", "server.host"},
		Validator: func(values map[string]interface{}) error {
			debugMode, _ := values["features.debug_mode"].(bool)
			logLevel, _ := values["logging.level"].(string)
			host, _ := values["server.host"].(string)
			
			// Production security checks
			if host == "0.0.0.0" { // Production-like host
				if debugMode {
					return fmt.Errorf("debug mode should be disabled in production")
				}
				
				if logLevel == "debug" {
					return fmt.Errorf("debug logging should not be used in production")
				}
			}
			
			return nil
		},
	}
}

// CreatePerformanceConstraint validates performance-related settings
func CreatePerformanceConstraint() ConstraintRule {
	return ConstraintRule{
		Name:        "performance_constraint",
		Description: "Ensures performance settings are reasonable",
		Fields:      []string{"server.max_connections", "server.timeout"},
		Validator: func(values map[string]interface{}) error {
			maxConn, connExists := values["server.max_connections"].(float64)
			timeout, timeoutExists := values["server.timeout"].(float64)
			
			if connExists && timeoutExists {
				// High connection count with low timeout might cause issues
				if maxConn > 1000 && timeout < 30 {
					return fmt.Errorf("high connection count with low timeout may cause performance issues")
				}
			}
			
			return nil
		},
	}
}

// ConfigErrorHandler handles configuration errors with different strategies
type ConfigErrorHandler struct {
	strategies map[string]ErrorHandlingStrategy
}

// ErrorHandlingStrategy defines how to handle different types of errors
type ErrorHandlingStrategy interface {
	Handle(err ValidationError, config map[string]interface{}) error
	CanHandle(errorCode string) bool
	Priority() int
}

// LogAndContinueStrategy logs errors but continues processing
type LogAndContinueStrategy struct{}

func (s *LogAndContinueStrategy) Handle(err ValidationError, config map[string]interface{}) error {
	fmt.Printf("Configuration warning: %s\n", err.Error())
	return nil
}

func (s *LogAndContinueStrategy) CanHandle(errorCode string) bool {
	return true // Can handle any error
}

func (s *LogAndContinueStrategy) Priority() int {
	return 1 // Low priority
}

// FailFastStrategy stops processing on any error
type FailFastStrategy struct{}

func (s *FailFastStrategy) Handle(err ValidationError, config map[string]interface{}) error {
	return fmt.Errorf("critical configuration error: %s", err.Error())
}

func (s *FailFastStrategy) CanHandle(errorCode string) bool {
	critical := map[string]bool{
		"required":      true,
		"type_mismatch": true,
	}
	return critical[errorCode]
}

func (s *FailFastStrategy) Priority() int {
	return 10 // High priority
}

// DefaultValueStrategy applies default values for missing fields
type DefaultValueStrategy struct {
	defaults map[string]interface{}
}

func NewDefaultValueStrategy(defaults map[string]interface{}) *DefaultValueStrategy {
	return &DefaultValueStrategy{defaults: defaults}
}

func (s *DefaultValueStrategy) Handle(err ValidationError, config map[string]interface{}) error {
	if err.Code == "required" {
		if defaultValue, exists := s.defaults[err.Field]; exists {
			// Apply default value
			s.setNestedValue(config, err.Field, defaultValue)
			fmt.Printf("Applied default value for %s: %v\n", err.Field, defaultValue)
			return nil
		}
	}
	return fmt.Errorf("no default available for required field: %s", err.Field)
}

func (s *DefaultValueStrategy) CanHandle(errorCode string) bool {
	return errorCode == "required"
}

func (s *DefaultValueStrategy) Priority() int {
	return 5 // Medium priority
}

func (s *DefaultValueStrategy) setNestedValue(config map[string]interface{}, key string, value interface{}) {
	parts := strings.Split(key, ".")
	current := config
	
	for i, part := range parts {
		if i == len(parts)-1 {
			current[part] = value
			return
		}
		
		if next, exists := current[part]; exists {
			if nextMap, ok := next.(map[string]interface{}); ok {
				current = nextMap
			} else {
				return
			}
		} else {
			newMap := make(map[string]interface{})
			current[part] = newMap
			current = newMap
		}
	}
}

// NewConfigErrorHandler creates a new configuration error handler
func NewConfigErrorHandler() *ConfigErrorHandler {
	return &ConfigErrorHandler{
		strategies: make(map[string]ErrorHandlingStrategy),
	}
}

// AddStrategy adds an error handling strategy
func (h *ConfigErrorHandler) AddStrategy(name string, strategy ErrorHandlingStrategy) {
	h.strategies[name] = strategy
}

// HandleErrors processes validation errors using registered strategies
func (h *ConfigErrorHandler) HandleErrors(errors []ValidationError, config map[string]interface{}) error {
	for _, err := range errors {
		handled := false
		var bestStrategy ErrorHandlingStrategy
		bestPriority := -1
		
		// Find the best strategy for this error
		for _, strategy := range h.strategies {
			if strategy.CanHandle(err.Code) && strategy.Priority() > bestPriority {
				bestStrategy = strategy
				bestPriority = strategy.Priority()
			}
		}
		
		if bestStrategy != nil {
			if handleErr := bestStrategy.Handle(err, config); handleErr != nil {
				return handleErr
			}
			handled = true
		}
		
		if !handled {
			return fmt.Errorf("unhandled validation error: %s", err.Error())
		}
	}
	
	return nil
}
package config

import (
	"testing"
)

func TestConfigValidator_ValidateConfig(t *testing.T) {
	validator := NewConfigValidator()
	
	// Add basic validation rules
	validator.AddRule("server.port", ValidationRule{
		Name:      "port_validation",
		Validator: ValidatePort,
		Required:  true,
	})
	
	validator.AddRule("server.host", ValidationRule{
		Name:      "host_validation",
		Validator: ValidateHost,
		Required:  true,
	})
	
	// Test valid configuration
	validConfig := map[string]interface{}{
		"server": map[string]interface{}{
			"host": "localhost",
			"port": 8080.0,
		},
	}
	
	result := validator.ValidateConfig(validConfig)
	if !result.Valid {
		t.<PERSON>rf("Valid config should pass validation, errors: %v", result.Errors)
	}
	
	// Test invalid configuration
	invalidConfig := map[string]interface{}{
		"server": map[string]interface{}{
			"host": "",
			"port": 99999.0,
		},
	}
	
	result = validator.ValidateConfig(invalidConfig)
	if result.Valid {
		t.<PERSON>r("Invalid config should fail validation")
	}
	
	if len(result.Errors) != 2 {
		t.<PERSON><PERSON>("Expected 2 validation errors, got %d", len(result.Errors))
	}
}

func TestConfigValidator_SchemaValidation(t *testing.T) {
	validator := NewConfigValidator()
	
	// Define schema
	schema := &ConfigSchema{
		Properties: map[string]PropertyDefinition{
			"server": {
				Type: "object",
				Properties: map[string]PropertyDefinition{
					"port": {
						Type:    "number",
						Minimum: func(f float64) *float64 { return &f }(1),
						Maximum: func(f float64) *float64 { return &f }(65535),
					},
					"host": {
						Type:    "string",
						Pattern: `^[a-zA-Z0-9.-]+$`,
					},
					"ssl": {
						Type: "boolean",
					},
				},
				Required: []string{"port", "host"},
			},
			"database": {
				Type: "object",
				Properties: map[string]PropertyDefinition{
					"driver": {
						Type: "string",
						Enum: []interface{}{"postgres", "mysql", "sqlite"},
					},
					"connections": {
						Type: "array",
						Items: &PropertyDefinition{
							Type: "object",
							Properties: map[string]PropertyDefinition{
								"name": {Type: "string"},
								"url":  {Type: "string", Format: "uri"},
							},
						},
					},
				},
			},
		},
		Required: []string{"server"},
	}
	
	validator.SetSchema(schema)
	
	// Test valid config
	validConfig := map[string]interface{}{
		"server": map[string]interface{}{
			"port": 8080.0,
			"host": "localhost",
			"ssl":  true,
		},
		"database": map[string]interface{}{
			"driver": "postgres",
			"connections": []interface{}{
				map[string]interface{}{
					"name": "primary",
					"url":  "http://localhost:5432",
				},
			},
		},
	}
	
	result := validator.ValidateConfig(validConfig)
	if !result.Valid {
		t.Errorf("Valid config should pass schema validation, errors: %v", result.Errors)
	}
	
	// Test missing required field
	missingRequired := map[string]interface{}{
		"database": map[string]interface{}{
			"driver": "postgres",
		},
	}
	
	result = validator.ValidateConfig(missingRequired)
	if result.Valid {
		t.Error("Config missing required field should fail validation")
	}
	
	// Test type mismatch
	typeMismatch := map[string]interface{}{
		"server": map[string]interface{}{
			"port": "not-a-number",
			"host": "localhost",
		},
	}
	
	result = validator.ValidateConfig(typeMismatch)
	if result.Valid {
		t.Error("Config with type mismatch should fail validation")
	}
	
	// Test enum violation
	enumViolation := map[string]interface{}{
		"server": map[string]interface{}{
			"port": 8080.0,
			"host": "localhost",
		},
		"database": map[string]interface{}{
			"driver": "oracle", // Not in enum
		},
	}
	
	result = validator.ValidateConfig(enumViolation)
	if result.Valid {
		t.Error("Config with enum violation should fail validation")
	}
	
	// Test range violation
	rangeViolation := map[string]interface{}{
		"server": map[string]interface{}{
			"port": 99999.0, // Above maximum
			"host": "localhost",
		},
	}
	
	result = validator.ValidateConfig(rangeViolation)
	if result.Valid {
		t.Error("Config with range violation should fail validation")
	}
}

func TestConfigValidator_FormatValidation(t *testing.T) {
	validator := NewConfigValidator()
	
	schema := &ConfigSchema{
		Properties: map[string]PropertyDefinition{
			"email": {Type: "string", Format: "email"},
			"url":   {Type: "string", Format: "url"},
			"ipv4":  {Type: "string", Format: "ipv4"},
			"date":  {Type: "string", Format: "date"},
			"time":  {Type: "string", Format: "time"},
		},
	}
	
	validator.SetSchema(schema)
	
	// Test valid formats
	validFormats := map[string]interface{}{
		"email": "<EMAIL>",
		"url":   "https://example.com",
		"ipv4":  "***********",
		"date":  "2023-12-01",
		"time":  "14:30:00",
	}
	
	result := validator.ValidateConfig(validFormats)
	if !result.Valid {
		t.Errorf("Valid formats should pass validation, errors: %v", result.Errors)
	}
	
	// Test invalid formats
	invalidFormats := map[string]interface{}{
		"email": "invalid-email",
		"url":   "not-a-url",
		"ipv4":  "999.999.999.999",
		"date":  "invalid-date",
		"time":  "25:00:00",
	}
	
	result = validator.ValidateConfig(invalidFormats)
	if result.Valid {
		t.Error("Invalid formats should fail validation")
	}
	
	if len(result.Errors) != 5 {
		t.Errorf("Expected 5 format validation errors, got %d", len(result.Errors))
	}
}

func TestConfigValidator_Constraints(t *testing.T) {
	validator := NewConfigValidator()
	
	// Add security constraint
	validator.AddConstraint("security", CreateSecurityConstraint())
	
	// Test violation of security constraint
	unsafeConfig := map[string]interface{}{
		"features": map[string]interface{}{
			"debug_mode": true,
		},
		"logging": map[string]interface{}{
			"level": "debug",
		},
		"server": map[string]interface{}{
			"host": "0.0.0.0", // Production-like setting
		},
	}
	
	result := validator.ValidateConfig(unsafeConfig)
	if result.Valid {
		t.Error("Config violating security constraints should fail validation")
	}
	
	// Test valid security configuration
	safeConfig := map[string]interface{}{
		"features": map[string]interface{}{
			"debug_mode": false,
		},
		"logging": map[string]interface{}{
			"level": "info",
		},
		"server": map[string]interface{}{
			"host": "0.0.0.0",
		},
	}
	
	result = validator.ValidateConfig(safeConfig)
	if !result.Valid {
		t.Errorf("Safe config should pass validation, errors: %v", result.Errors)
	}
}

func TestConfigValidator_BuiltInRules(t *testing.T) {
	validator := NewConfigValidator()
	
	// Add built-in rules
	validator.AddRule("database", CreateDatabaseConnectionRule())
	validator.AddRule("server", CreateServerConfigRule())
	
	// Test valid configuration
	validConfig := map[string]interface{}{
		"database": map[string]interface{}{
			"host": "localhost",
			"port": 5432.0,
			"name": "testdb",
		},
		"server": map[string]interface{}{
			"host": "localhost",
			"port": 8080.0,
		},
	}
	
	result := validator.ValidateConfig(validConfig)
	if !result.Valid {
		t.Errorf("Valid config should pass built-in rule validation, errors: %v", result.Errors)
	}
	
	// Test invalid database config
	invalidDbConfig := map[string]interface{}{
		"database": map[string]interface{}{
			"host": "localhost",
			// Missing port and name
		},
		"server": map[string]interface{}{
			"host": "localhost",
			"port": 8080.0,
		},
	}
	
	result = validator.ValidateConfig(invalidDbConfig)
	if result.Valid {
		t.Error("Invalid database config should fail validation")
	}
}

func TestConfigValidator_ValidateField(t *testing.T) {
	validator := NewConfigValidator()
	
	validator.AddRule("port", ValidationRule{
		Name:      "port_validation",
		Validator: ValidatePort,
		Required:  true,
	})
	
	// Test valid field
	result := validator.ValidateField("port", 8080.0)
	if !result.Valid {
		t.Errorf("Valid field should pass validation, errors: %v", result.Errors)
	}
	
	// Test invalid field
	result = validator.ValidateField("port", 99999.0)
	if result.Valid {
		t.Error("Invalid field should fail validation")
	}
	
	if len(result.Errors) != 1 {
		t.Errorf("Expected 1 validation error, got %d", len(result.Errors))
	}
	
	if result.Errors[0].Field != "port" {
		t.Errorf("Expected field 'port', got %s", result.Errors[0].Field)
	}
}

func TestConfigErrorHandler_Strategies(t *testing.T) {
	handler := NewConfigErrorHandler()
	
	// Add strategies
	handler.AddStrategy("log", &LogAndContinueStrategy{})
	handler.AddStrategy("fail", &FailFastStrategy{})
	
	defaults := map[string]interface{}{
		"server.port": 8080.0,
		"server.host": "localhost",
	}
	handler.AddStrategy("default", NewDefaultValueStrategy(defaults))
	
	config := make(map[string]interface{})
	
	// Test handling required field error with default strategy
	requiredError := ValidationError{
		Field:   "server.port",
		Value:   nil,
		Message: "required field is missing",
		Code:    "required",
	}
	
	err := handler.HandleErrors([]ValidationError{requiredError}, config)
	if err != nil {
		t.Errorf("Required error should be handled with default value: %v", err)
	}
	
	// Verify default value was applied
	if port, exists := config["server"]; exists {
		if serverConfig, ok := port.(map[string]interface{}); ok {
			if portValue, exists := serverConfig["port"]; !exists || portValue != 8080.0 {
				t.Error("Default port value should have been applied")
			}
		}
	} else {
		t.Error("Server config should have been created")
	}
	
	// Test critical error with fail fast strategy
	criticalError := ValidationError{
		Field:   "server.host",
		Value:   123,
		Message: "expected string type",
		Code:    "type_mismatch",
	}
	
	err = handler.HandleErrors([]ValidationError{criticalError}, config)
	if err == nil {
		t.Error("Critical error should cause failure")
	}
}

func TestValidationError_Error(t *testing.T) {
	err := ValidationError{
		Field:   "server.port",
		Value:   99999,
		Message: "port out of range",
		Code:    "range_violation",
	}
	
	expected := "validation error for field 'server.port': port out of range (value: 99999)"
	if err.Error() != expected {
		t.Errorf("Expected error message: %s, got: %s", expected, err.Error())
	}
}

func TestPropertyDefinition_TypeValidation(t *testing.T) {
	validator := NewConfigValidator()
	
	testCases := []struct {
		value        interface{}
		expectedType string
		shouldPass   bool
	}{
		{"string", "string", true},
		{123.0, "number", true},
		{42.0, "integer", true},
		{42.5, "integer", false},
		{true, "boolean", true},
		{[]interface{}{1, 2, 3}, "array", true},
		{map[string]interface{}{"key": "value"}, "object", true},
		{nil, "null", true},
		{"string", "number", false},
		{123, "string", false},
	}
	
	for _, tc := range testCases {
		result := validator.validateType(tc.value, tc.expectedType)
		if result != tc.shouldPass {
			t.Errorf("Type validation for %v as %s should be %v, got %v",
				tc.value, tc.expectedType, tc.shouldPass, result)
		}
	}
}

func TestFormatValidators(t *testing.T) {
	validator := NewConfigValidator()
	
	testCases := []struct {
		value      string
		format     string
		shouldPass bool
	}{
		{"<EMAIL>", "email", true},
		{"invalid-email", "email", false},
		{"https://example.com", "url", true},
		{"not-a-url", "url", false},
		{"***********", "ipv4", true},
		{"999.999.999.999", "ipv4", false},
		{"2001:0db8:85a3:0000:0000:8a2e:0370:7334", "ipv6", true},
		{"invalid-ipv6", "ipv6", false},
		{"example.com", "hostname", true},
		{"invalid..hostname", "hostname", false},
		{"2023-12-01", "date", true},
		{"invalid-date", "date", false},
		{"14:30:00", "time", true},
		{"25:00:00", "time", false},
		{"2023-12-01T14:30:00Z", "datetime", true},
		{"invalid-datetime", "datetime", false},
		{"1h30m", "duration", true},
		{"invalid-duration", "duration", false},
	}
	
	for _, tc := range testCases {
		err := validator.validateFormat(tc.value, tc.format)
		passed := err == nil
		
		if passed != tc.shouldPass {
			t.Errorf("Format validation for '%s' as %s should be %v, got %v (error: %v)",
				tc.value, tc.format, tc.shouldPass, passed, err)
		}
	}
}
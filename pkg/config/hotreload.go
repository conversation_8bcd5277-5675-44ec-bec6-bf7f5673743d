// Package config provides hot-reload configuration management
package config

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
)

// HotReloadManager manages hot-reloading of configuration files
type HotReloadManager struct {
	configManager *ConfigManager
	watcher       *fsnotify.Watcher
	watchedFiles  map[string]Environment
	listeners     []HotReloadListener
	debounceTime  time.Duration
	mutex         sync.RWMutex
	ctx           context.Context
	cancel        context.CancelFunc
	running       bool
}

// HotReloadListener receives notifications when configuration changes
type HotReloadListener interface {
	OnConfigReload(env Environment, oldConfig, newConfig map[string]interface{}) error
	OnReloadError(env Environment, err error)
	ListenerName() string
}

// HotReloadEvent represents a configuration reload event
type HotReloadEvent struct {
	Environment Environment            `json:"environment"`
	FilePath    string                 `json:"file_path"`
	ChangeType  string                 `json:"change_type"`
	OldConfig   map[string]interface{} `json:"old_config,omitempty"`
	NewConfig   map[string]interface{} `json:"new_config"`
	Timestamp   time.Time              `json:"timestamp"`
	Success     bool                   `json:"success"`
	Error       string                 `json:"error,omitempty"`
}

// NewHotReloadManager creates a new hot-reload manager
func NewHotReloadManager(configManager *ConfigManager) (*HotReloadManager, error) {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return nil, fmt.Errorf("failed to create file watcher: %w", err)
	}
	
	ctx, cancel := context.WithCancel(context.Background())
	
	return &HotReloadManager{
		configManager: configManager,
		watcher:       watcher,
		watchedFiles:  make(map[string]Environment),
		listeners:     make([]HotReloadListener, 0),
		debounceTime:  500 * time.Millisecond, // Default debounce time
		ctx:           ctx,
		cancel:        cancel,
		running:       false,
	}, nil
}

// Start begins watching for configuration file changes
func (hrm *HotReloadManager) Start() error {
	hrm.mutex.Lock()
	defer hrm.mutex.Unlock()
	
	if hrm.running {
		return fmt.Errorf("hot reload manager is already running")
	}
	
	hrm.running = true
	
	// Start the event processing goroutine
	go hrm.processEvents()
	
	return nil
}

// Stop stops watching for configuration file changes
func (hrm *HotReloadManager) Stop() error {
	hrm.mutex.Lock()
	defer hrm.mutex.Unlock()
	
	if !hrm.running {
		return nil
	}
	
	hrm.running = false
	hrm.cancel()
	
	// Close the file watcher
	if err := hrm.watcher.Close(); err != nil {
		return fmt.Errorf("failed to close file watcher: %w", err)
	}
	
	return nil
}

// WatchEnvironment starts watching a specific environment's configuration file
func (hrm *HotReloadManager) WatchEnvironment(env Environment) error {
	hrm.mutex.Lock()
	defer hrm.mutex.Unlock()
	
	configDir := hrm.configManager.configDir
	filePath := filepath.Join(configDir, fmt.Sprintf("%s.json", env))
	
	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("configuration file for environment %s does not exist: %s", env, filePath)
	}
	
	// Add to watcher
	if err := hrm.watcher.Add(filePath); err != nil {
		return fmt.Errorf("failed to watch file %s: %w", filePath, err)
	}
	
	hrm.watchedFiles[filePath] = env
	return nil
}

// UnwatchEnvironment stops watching a specific environment's configuration file
func (hrm *HotReloadManager) UnwatchEnvironment(env Environment) error {
	hrm.mutex.Lock()
	defer hrm.mutex.Unlock()
	
	configDir := hrm.configManager.configDir
	filePath := filepath.Join(configDir, fmt.Sprintf("%s.json", env))
	
	// Remove from watcher
	if err := hrm.watcher.Remove(filePath); err != nil {
		return fmt.Errorf("failed to unwatch file %s: %w", filePath, err)
	}
	
	delete(hrm.watchedFiles, filePath)
	return nil
}

// AddListener adds a hot-reload listener
func (hrm *HotReloadManager) AddListener(listener HotReloadListener) {
	hrm.mutex.Lock()
	defer hrm.mutex.Unlock()
	
	hrm.listeners = append(hrm.listeners, listener)
}

// RemoveListener removes a hot-reload listener
func (hrm *HotReloadManager) RemoveListener(name string) {
	hrm.mutex.Lock()
	defer hrm.mutex.Unlock()
	
	for i, listener := range hrm.listeners {
		if listener.ListenerName() == name {
			hrm.listeners = append(hrm.listeners[:i], hrm.listeners[i+1:]...)
			break
		}
	}
}

// SetDebounceTime sets the debounce time for file change events
func (hrm *HotReloadManager) SetDebounceTime(duration time.Duration) {
	hrm.mutex.Lock()
	defer hrm.mutex.Unlock()
	
	hrm.debounceTime = duration
}

// processEvents processes file system events
func (hrm *HotReloadManager) processEvents() {
	debounceTimer := make(map[string]*time.Timer)
	
	for {
		select {
		case event, ok := <-hrm.watcher.Events:
			if !ok {
				return
			}
			
			hrm.handleFileEvent(event, debounceTimer)
			
		case err, ok := <-hrm.watcher.Errors:
			if !ok {
				return
			}
			
			fmt.Printf("File watcher error: %v\n", err)
			
		case <-hrm.ctx.Done():
			// Cancel any pending timers
			for _, timer := range debounceTimer {
				timer.Stop()
			}
			return
		}
	}
}

// handleFileEvent handles a single file system event
func (hrm *HotReloadManager) handleFileEvent(event fsnotify.Event, debounceTimer map[string]*time.Timer) {
	hrm.mutex.RLock()
	env, isWatched := hrm.watchedFiles[event.Name]
	hrm.mutex.RUnlock()
	
	if !isWatched {
		return
	}
	
	// Cancel existing timer for this file
	if timer, exists := debounceTimer[event.Name]; exists {
		timer.Stop()
	}
	
	// Create new debounced timer
	debounceTimer[event.Name] = time.AfterFunc(hrm.debounceTime, func() {
		hrm.reloadConfiguration(env, event.Name, event.Op.String())
		delete(debounceTimer, event.Name)
	})
}

// reloadConfiguration reloads the configuration for a specific environment
func (hrm *HotReloadManager) reloadConfiguration(env Environment, filePath, changeType string) {
	// Get old configuration
	oldConfig := hrm.configManager.GetAllConfigsForEnv(env)
	
	// Reload the profile
	err := hrm.configManager.LoadProfile(env)
	
	event := HotReloadEvent{
		Environment: env,
		FilePath:    filePath,
		ChangeType:  changeType,
		OldConfig:   oldConfig,
		Timestamp:   time.Now(),
		Success:     err == nil,
	}
	
	if err != nil {
		event.Error = err.Error()
		hrm.notifyReloadError(env, err)
		return
	}
	
	// Get new configuration
	newConfig := hrm.configManager.GetAllConfigsForEnv(env)
	event.NewConfig = newConfig
	
	// Validate new configuration
	if validator := hrm.getValidator(); validator != nil {
		result := validator.ValidateConfig(newConfig)
		if !result.Valid {
			validationErr := fmt.Errorf("configuration validation failed: %v", result.Errors)
			event.Success = false
			event.Error = validationErr.Error()
			hrm.notifyReloadError(env, validationErr)
			return
		}
	}
	
	// Notify listeners
	hrm.notifyReload(env, oldConfig, newConfig)
	
	// Log the reload event
	hrm.logReloadEvent(event)
}

// getValidator returns the validator if available
func (hrm *HotReloadManager) getValidator() *ConfigValidator {
	// This would typically be injected or configured
	// For now, return nil to skip validation
	return nil
}

// notifyReload notifies all listeners of a successful configuration reload
func (hrm *HotReloadManager) notifyReload(env Environment, oldConfig, newConfig map[string]interface{}) {
	hrm.mutex.RLock()
	listeners := make([]HotReloadListener, len(hrm.listeners))
	copy(listeners, hrm.listeners)
	hrm.mutex.RUnlock()
	
	for _, listener := range listeners {
		go func(l HotReloadListener) {
			if err := l.OnConfigReload(env, oldConfig, newConfig); err != nil {
				fmt.Printf("Hot reload listener %s error: %v\n", l.ListenerName(), err)
			}
		}(listener)
	}
}

// notifyReloadError notifies all listeners of a reload error
func (hrm *HotReloadManager) notifyReloadError(env Environment, err error) {
	hrm.mutex.RLock()
	listeners := make([]HotReloadListener, len(hrm.listeners))
	copy(listeners, hrm.listeners)
	hrm.mutex.RUnlock()
	
	for _, listener := range listeners {
		go func(l HotReloadListener) {
			l.OnReloadError(env, err)
		}(listener)
	}
}

// logReloadEvent logs a reload event
func (hrm *HotReloadManager) logReloadEvent(event HotReloadEvent) {
	status := "SUCCESS"
	if !event.Success {
		status = "FAILED"
	}
	
	fmt.Printf("[%s] Config reload for %s: %s (change: %s, file: %s)\n",
		status, event.Environment, event.Timestamp.Format(time.RFC3339),
		event.ChangeType, event.FilePath)
	
	if event.Error != "" {
		fmt.Printf("Error: %s\n", event.Error)
	}
}

// GetWatchedEnvironments returns all currently watched environments
func (hrm *HotReloadManager) GetWatchedEnvironments() []Environment {
	hrm.mutex.RLock()
	defer hrm.mutex.RUnlock()
	
	envs := make([]Environment, 0, len(hrm.watchedFiles))
	seen := make(map[Environment]bool)
	
	for _, env := range hrm.watchedFiles {
		if !seen[env] {
			envs = append(envs, env)
			seen[env] = true
		}
	}
	
	return envs
}

// IsRunning returns whether the hot-reload manager is running
func (hrm *HotReloadManager) IsRunning() bool {
	hrm.mutex.RLock()
	defer hrm.mutex.RUnlock()
	return hrm.running
}

// ConfigService provides high-level configuration operations with hot-reload support
type ConfigService struct {
	configManager   *ConfigManager
	hotReloadManager *HotReloadManager
	validator       *ConfigValidator
	errorHandler    *ConfigErrorHandler
	mutex           sync.RWMutex
}

// NewConfigService creates a new configuration service
func NewConfigService(configDir string) (*ConfigService, error) {
	configManager := NewConfigManager(configDir)
	
	hotReloadManager, err := NewHotReloadManager(configManager)
	if err != nil {
		return nil, fmt.Errorf("failed to create hot reload manager: %w", err)
	}
	
	validator := NewConfigValidator()
	errorHandler := NewConfigErrorHandler()
	
	service := &ConfigService{
		configManager:    configManager,
		hotReloadManager: hotReloadManager,
		validator:        validator,
		errorHandler:     errorHandler,
	}
	
	// Add default error handling strategies
	service.setupDefaultErrorHandling()
	
	// Add default validation rules
	service.setupDefaultValidation()
	
	return service, nil
}

// Start starts the configuration service
func (cs *ConfigService) Start() error {
	// Load all environment profiles
	environments := []Environment{Development, Testing, Staging, Production}
	for _, env := range environments {
		if err := cs.configManager.LoadProfile(env); err != nil {
			return fmt.Errorf("failed to load profile for %s: %w", env, err)
		}
	}
	
	// Start hot-reload manager
	if err := cs.hotReloadManager.Start(); err != nil {
		return fmt.Errorf("failed to start hot reload manager: %w", err)
	}
	
	// Watch all loaded environments
	for _, env := range environments {
		if err := cs.hotReloadManager.WatchEnvironment(env); err != nil {
			// Log warning but continue
			fmt.Printf("Warning: failed to watch environment %s: %v\n", env, err)
		}
	}
	
	return nil
}

// Stop stops the configuration service
func (cs *ConfigService) Stop() error {
	return cs.hotReloadManager.Stop()
}

// SetActiveEnvironment sets the active environment with validation
func (cs *ConfigService) SetActiveEnvironment(env Environment) error {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()
	
	// Validate configuration before switching
	config := cs.configManager.GetAllConfigsForEnv(env)
	result := cs.validator.ValidateConfig(config)
	
	if !result.Valid {
		// Try to handle errors
		if err := cs.errorHandler.HandleErrors(result.Errors, config); err != nil {
			return fmt.Errorf("failed to switch to environment %s: %w", env, err)
		}
		
		// Re-validate after error handling
		result = cs.validator.ValidateConfig(config)
		if !result.Valid {
			return fmt.Errorf("configuration still invalid after error handling for environment %s", env)
		}
	}
	
	return cs.configManager.SetActiveEnvironment(env)
}

// GetConfig gets a configuration value with validation
func (cs *ConfigService) GetConfig(key string) (interface{}, error) {
	cs.mutex.RLock()
	defer cs.mutex.RUnlock()
	
	return cs.configManager.GetConfig(key)
}

// SetConfig sets a configuration value with validation
func (cs *ConfigService) SetConfig(key string, value interface{}) error {
	cs.mutex.Lock()
	defer cs.mutex.Unlock()
	
	// Validate the field
	result := cs.validator.ValidateField(key, value)
	if !result.Valid {
		if err := cs.errorHandler.HandleErrors(result.Errors, nil); err != nil {
			return fmt.Errorf("failed to set config %s: %w", key, err)
		}
	}
	
	return cs.configManager.SetConfig(key, value)
}

// AddReloadListener adds a hot-reload listener
func (cs *ConfigService) AddReloadListener(listener HotReloadListener) {
	cs.hotReloadManager.AddListener(listener)
}

// RemoveReloadListener removes a hot-reload listener
func (cs *ConfigService) RemoveReloadListener(name string) {
	cs.hotReloadManager.RemoveListener(name)
}

// setupDefaultErrorHandling sets up default error handling strategies
func (cs *ConfigService) setupDefaultErrorHandling() {
	// Add default value strategy with common defaults
	defaults := map[string]interface{}{
		"server.host":           "localhost",
		"server.port":           8080.0,
		"database.host":         "localhost",
		"database.port":         5432.0,
		"database.ssl_mode":     "disable",
		"logging.level":         "info",
		"features.debug_mode":   false,
		"features.metrics_enabled": true,
	}
	
	cs.errorHandler.AddStrategy("default", NewDefaultValueStrategy(defaults))
	cs.errorHandler.AddStrategy("log", &LogAndContinueStrategy{})
	cs.errorHandler.AddStrategy("fail", &FailFastStrategy{})
}

// setupDefaultValidation sets up default validation rules
func (cs *ConfigService) setupDefaultValidation() {
	// Add basic field validators
	cs.validator.AddRule("server.port", ValidationRule{
		Name:      "port_validation",
		Validator: ValidatePort,
		Required:  true,
	})
	
	cs.validator.AddRule("server.host", ValidationRule{
		Name:      "host_validation",
		Validator: ValidateHost,
		Required:  true,
	})
	
	cs.validator.AddRule("logging.level", ValidationRule{
		Name:      "log_level_validation",
		Validator: ValidateLogLevel,
		Required:  true,
	})
	
	// Add built-in complex validators
	cs.validator.AddRule("database", CreateDatabaseConnectionRule())
	cs.validator.AddRule("server", CreateServerConfigRule())
	
	// Add constraints
	cs.validator.AddConstraint("security", CreateSecurityConstraint())
	cs.validator.AddConstraint("performance", CreatePerformanceConstraint())
}

// Built-in hot-reload listeners

// LoggingReloadListener logs configuration changes
type LoggingReloadListener struct {
	name string
}

func NewLoggingReloadListener() *LoggingReloadListener {
	return &LoggingReloadListener{name: "logging_reload_listener"}
}

func (l *LoggingReloadListener) OnConfigReload(env Environment, oldConfig, newConfig map[string]interface{}) error {
	fmt.Printf("Configuration reloaded for environment %s\n", env)
	
	// Compare configs and log changes
	changes := l.findChanges(oldConfig, newConfig, "")
	for _, change := range changes {
		fmt.Printf("  %s\n", change)
	}
	
	return nil
}

func (l *LoggingReloadListener) OnReloadError(env Environment, err error) {
	fmt.Printf("Configuration reload failed for environment %s: %v\n", env, err)
}

func (l *LoggingReloadListener) ListenerName() string {
	return l.name
}

func (l *LoggingReloadListener) findChanges(old, new map[string]interface{}, prefix string) []string {
	changes := make([]string, 0)
	
	// Check for modified and added values
	for key, newValue := range new {
		fullKey := key
		if prefix != "" {
			fullKey = prefix + "." + key
		}
		
		if oldValue, exists := old[key]; exists {
			if !l.isEqual(oldValue, newValue) {
				if oldMap, ok := oldValue.(map[string]interface{}); ok {
					if newMap, ok := newValue.(map[string]interface{}); ok {
						// Recurse into nested objects
						nestedChanges := l.findChanges(oldMap, newMap, fullKey)
						changes = append(changes, nestedChanges...)
					} else {
						changes = append(changes, fmt.Sprintf("Changed %s: %v -> %v", fullKey, oldValue, newValue))
					}
				} else {
					changes = append(changes, fmt.Sprintf("Changed %s: %v -> %v", fullKey, oldValue, newValue))
				}
			}
		} else {
			changes = append(changes, fmt.Sprintf("Added %s: %v", fullKey, newValue))
		}
	}
	
	// Check for removed values
	for key := range old {
		if _, exists := new[key]; !exists {
			fullKey := key
			if prefix != "" {
				fullKey = prefix + "." + key
			}
			changes = append(changes, fmt.Sprintf("Removed %s", fullKey))
		}
	}
	
	return changes
}

func (l *LoggingReloadListener) isEqual(a, b interface{}) bool {
	// Simple equality check - could be enhanced with deep comparison
	return fmt.Sprintf("%v", a) == fmt.Sprintf("%v", b)
}

// ServiceReloadListener notifies services of configuration changes
type ServiceReloadListener struct {
	name     string
	services map[string]ReloadableService
	mutex    sync.RWMutex
}

// ReloadableService can be reloaded when configuration changes
type ReloadableService interface {
	Reload(config map[string]interface{}) error
	ServiceName() string
}

func NewServiceReloadListener() *ServiceReloadListener {
	return &ServiceReloadListener{
		name:     "service_reload_listener",
		services: make(map[string]ReloadableService),
	}
}

func (l *ServiceReloadListener) AddService(service ReloadableService) {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	l.services[service.ServiceName()] = service
}

func (l *ServiceReloadListener) RemoveService(serviceName string) {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	delete(l.services, serviceName)
}

func (l *ServiceReloadListener) OnConfigReload(env Environment, oldConfig, newConfig map[string]interface{}) error {
	l.mutex.RLock()
	services := make([]ReloadableService, 0, len(l.services))
	for _, service := range l.services {
		services = append(services, service)
	}
	l.mutex.RUnlock()
	
	for _, service := range services {
		if err := service.Reload(newConfig); err != nil {
			fmt.Printf("Failed to reload service %s: %v\n", service.ServiceName(), err)
		}
	}
	
	return nil
}

func (l *ServiceReloadListener) OnReloadError(env Environment, err error) {
	fmt.Printf("Configuration reload error for environment %s: %v\n", env, err)
}

func (l *ServiceReloadListener) ListenerName() string {
	return l.name
}
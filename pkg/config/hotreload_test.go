package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"
)

func TestHotReloadManager_StartStop(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "hotreload-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	configManager := NewConfigManager(tempDir)
	hrm, err := NewHotReloadManager(configManager)
	if err != nil {
		t.Fatalf("Failed to create hot reload manager: %v", err)
	}

	// Test initial state
	if hrm.IsRunning() {
		t.<PERSON>r("Hot reload manager should not be running initially")
	}

	// Test start
	err = hrm.Start()
	if err != nil {
		t.Fatalf("Failed to start hot reload manager: %v", err)
	}

	if !hrm.IsRunning() {
		t.<PERSON>("Hot reload manager should be running after start")
	}

	// Test double start
	err = hrm.Start()
	if err == nil {
		t.Error("Starting already running manager should fail")
	}

	// Test stop
	err = hrm.Stop()
	if err != nil {
		t.Fatalf("Failed to stop hot reload manager: %v", err)
	}

	if hrm.IsRunning() {
		t.Error("Hot reload manager should not be running after stop")
	}
}

func TestHotReloadManager_WatchUnwatch(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "hotreload-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	configManager := NewConfigManager(tempDir)
	hrm, err := NewHotReloadManager(configManager)
	if err != nil {
		t.Fatalf("Failed to create hot reload manager: %v", err)
	}

	// Create a config file
	configFile := filepath.Join(tempDir, "development.json")
	config := map[string]interface{}{
		"server": map[string]interface{}{
			"host": "localhost",
			"port": 8080,
		},
	}

	data, _ := json.Marshal(config)
	err = os.WriteFile(configFile, data, 0644)
	if err != nil {
		t.Fatalf("Failed to create config file: %v", err)
	}

	// Test watching non-existent file
	err = hrm.WatchEnvironment(Testing)
	if err == nil {
		t.Error("Watching non-existent file should fail")
	}

	// Test watching existing file
	err = hrm.WatchEnvironment(Development)
	if err != nil {
		t.Fatalf("Failed to watch development environment: %v", err)
	}

	// Verify file is being watched
	watchedEnvs := hrm.GetWatchedEnvironments()
	if len(watchedEnvs) != 1 || watchedEnvs[0] != Development {
		t.Errorf("Expected 1 watched environment (development), got %v", watchedEnvs)
	}

	// Test unwatching
	err = hrm.UnwatchEnvironment(Development)
	if err != nil {
		t.Fatalf("Failed to unwatch development environment: %v", err)
	}

	// Verify file is no longer being watched
	watchedEnvs = hrm.GetWatchedEnvironments()
	if len(watchedEnvs) != 0 {
		t.Errorf("Expected 0 watched environments, got %v", watchedEnvs)
	}
}

func TestHotReloadManager_Listeners(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "hotreload-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	configManager := NewConfigManager(tempDir)
	hrm, err := NewHotReloadManager(configManager)
	if err != nil {
		t.Fatalf("Failed to create hot reload manager: %v", err)
	}

	// Create test listener
	listener := &TestHotReloadListener{
		name:    "test-listener",
		reloads: make([]ReloadEvent, 0),
		errors:  make([]error, 0),
	}

	// Add listener
	hrm.AddListener(listener)

	// Test removing listener
	hrm.RemoveListener("test-listener")

	// Add listener again for further testing
	hrm.AddListener(listener)

	// Verify listener is added (implicitly tested through reload events)
}

func TestConfigService_StartStop(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "config-service-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	service, err := NewConfigService(tempDir)
	if err != nil {
		t.Fatalf("Failed to create config service: %v", err)
	}

	// Test start
	err = service.Start()
	if err != nil {
		t.Fatalf("Failed to start config service: %v", err)
	}

	// Verify hot reload manager is running
	if !service.hotReloadManager.IsRunning() {
		t.Error("Hot reload manager should be running after service start")
	}

	// Test stop
	err = service.Stop()
	if err != nil {
		t.Fatalf("Failed to stop config service: %v", err)
	}

	// Verify hot reload manager is stopped
	if service.hotReloadManager.IsRunning() {
		t.Error("Hot reload manager should be stopped after service stop")
	}
}

func TestConfigService_GetSetConfig(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "config-service-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	service, err := NewConfigService(tempDir)
	if err != nil {
		t.Fatalf("Failed to create config service: %v", err)
	}

	err = service.Start()
	if err != nil {
		t.Fatalf("Failed to start config service: %v", err)
	}
	defer service.Stop()

	// Set active environment
	err = service.SetActiveEnvironment(Development)
	if err != nil {
		t.Fatalf("Failed to set active environment: %v", err)
	}

	// Test getting config
	host, err := service.GetConfig("server.host")
	if err != nil {
		t.Fatalf("Failed to get config: %v", err)
	}

	if host != "localhost" {
		t.Errorf("Expected 'localhost', got %v", host)
	}

	// Test setting valid config
	err = service.SetConfig("server.port", 9000.0)
	if err != nil {
		t.Fatalf("Failed to set valid config: %v", err)
	}

	// Verify config was set
	port, err := service.GetConfig("server.port")
	if err != nil {
		t.Fatalf("Failed to get updated config: %v", err)
	}

	if port != 9000.0 {
		t.Errorf("Expected 9000.0, got %v", port)
	}

	// Test setting invalid config (should be handled by error strategies)
	err = service.SetConfig("server.port", "invalid-port")
	// This might not fail due to error handling strategies, so we just check it doesn't panic
}

func TestConfigService_Validation(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "config-service-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	service, err := NewConfigService(tempDir)
	if err != nil {
		t.Fatalf("Failed to create config service: %v", err)
	}

	err = service.Start()
	if err != nil {
		t.Fatalf("Failed to start config service: %v", err)
	}
	defer service.Stop()

	// Set up a configuration that would violate security constraints
	service.configManager.SetConfigForEnv(Development, "features.debug_mode", true)
	service.configManager.SetConfigForEnv(Development, "logging.level", "debug")
	service.configManager.SetConfigForEnv(Development, "server.host", "0.0.0.0")

	// Try to switch to this environment - should fail or be corrected
	err = service.SetActiveEnvironment(Development)
	// The error handling might correct the issues, so we don't necessarily expect failure

	if err != nil {
		t.Logf("Environment switch failed as expected due to validation: %v", err)
	}
}

func TestLoggingReloadListener(t *testing.T) {
	listener := NewLoggingReloadListener()

	if listener.ListenerName() != "logging_reload_listener" {
		t.Errorf(
			"Expected listener name 'logging_reload_listener', got %s",
			listener.ListenerName(),
		)
	}

	// Test config reload notification
	oldConfig := map[string]interface{}{
		"server": map[string]interface{}{
			"port": 8080.0,
		},
	}

	newConfig := map[string]interface{}{
		"server": map[string]interface{}{
			"port": 9000.0,
			"host": "localhost",
		},
	}

	err := listener.OnConfigReload(Development, oldConfig, newConfig)
	if err != nil {
		t.Errorf("OnConfigReload should not return error: %v", err)
	}

	// Test error notification
	listener.OnReloadError(Development, fmt.Errorf("test error"))
	// Should not panic or return error
}

func TestServiceReloadListener(t *testing.T) {
	listener := NewServiceReloadListener()

	// Create mock reloadable service
	service := &MockReloadableService{
		name:     "test-service",
		reloaded: false,
	}

	// Add service
	listener.AddService(service)

	// Test config reload
	config := map[string]interface{}{
		"test": "value",
	}

	err := listener.OnConfigReload(Development, nil, config)
	if err != nil {
		t.Errorf("OnConfigReload should not return error: %v", err)
	}

	// Give some time for goroutine to execute
	time.Sleep(10 * time.Millisecond)

	if !service.reloaded {
		t.Error("Service should have been reloaded")
	}

	// Test removing service
	listener.RemoveService("test-service")

	// Reset and test again
	service.reloaded = false
	listener.OnConfigReload(Development, nil, config)
	time.Sleep(10 * time.Millisecond)

	if service.reloaded {
		t.Error("Removed service should not have been reloaded")
	}
}

func TestHotReloadManager_DebounceTime(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "hotreload-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	configManager := NewConfigManager(tempDir)
	hrm, err := NewHotReloadManager(configManager)
	if err != nil {
		t.Fatalf("Failed to create hot reload manager: %v", err)
	}

	// Test setting debounce time
	hrm.SetDebounceTime(100 * time.Millisecond)

	// The debounce time is used internally, so we can't directly verify it
	// But we can verify the method doesn't panic
}

// Test helper types

type ReloadEvent struct {
	Environment Environment
	OldConfig   map[string]interface{}
	NewConfig   map[string]interface{}
}

type TestHotReloadListener struct {
	name    string
	reloads []ReloadEvent
	errors  []error
}

func (l *TestHotReloadListener) OnConfigReload(
	env Environment,
	oldConfig, newConfig map[string]interface{},
) error {
	l.reloads = append(l.reloads, ReloadEvent{
		Environment: env,
		OldConfig:   oldConfig,
		NewConfig:   newConfig,
	})
	return nil
}

func (l *TestHotReloadListener) OnReloadError(env Environment, err error) {
	l.errors = append(l.errors, err)
}

func (l *TestHotReloadListener) ListenerName() string {
	return l.name
}

type MockReloadableService struct {
	name     string
	reloaded bool
}

func (s *MockReloadableService) Reload(config map[string]interface{}) error {
	s.reloaded = true
	return nil
}

func (s *MockReloadableService) ServiceName() string {
	return s.name
}


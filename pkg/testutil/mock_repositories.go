package testutil

import (
	"fmt"
	"sync"

	"eionz.com/demo/pkg/domain"
)

// MockLayoutRepository is a mock implementation of LayoutRepository for testing
type MockLayoutRepository struct {
	layouts map[string]*domain.Layout
	mutex   sync.RWMutex
	
	// Configuration for testing behavior
	shouldFailOnSave   bool
	shouldFailOnGet    bool
	shouldFailOnDelete bool
	saveCallCount      int
	getCallCount       int
	deleteCallCount    int
}

// NewMockLayoutRepository creates a new mock layout repository
func NewMockLayoutRepository() *MockLayoutRepository {
	return &MockLayoutRepository{
		layouts: make(map[string]*domain.Layout),
	}
}

func (r *MockLayoutRepository) GetByID(id string) (*domain.Layout, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	r.getCallCount++
	
	if r.shouldFailOnGet {
		return nil, fmt.Errorf("mock repository configured to fail on get")
	}
	
	layout, exists := r.layouts[id]
	if !exists {
		return nil, fmt.Errorf("layout with id %s not found", id)
	}
	
	return layout, nil
}

func (r *MockLayoutRepository) Save(layout *domain.Layout) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	r.saveCallCount++
	
	if r.shouldFailOnSave {
		return fmt.Errorf("mock repository configured to fail on save")
	}
	
	r.layouts[layout.ID()] = layout
	return nil
}

func (r *MockLayoutRepository) Delete(id string) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	r.deleteCallCount++
	
	if r.shouldFailOnDelete {
		return fmt.Errorf("mock repository configured to fail on delete")
	}
	
	delete(r.layouts, id)
	return nil
}

func (r *MockLayoutRepository) GetByUserID(userID string) (*domain.Layout, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	// Simple implementation - just return the first layout found
	for _, layout := range r.layouts {
		return layout, nil
	}
	
	return nil, fmt.Errorf("no layout found for user %s", userID)
}

func (r *MockLayoutRepository) FindByDimensions(dimensions domain.Dimensions) ([]*domain.Layout, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	var results []*domain.Layout
	for _, layout := range r.layouts {
		if layout.GetDimensions().Equals(dimensions) {
			results = append(results, layout)
		}
	}
	
	return results, nil
}

// Test helper methods

func (r *MockLayoutRepository) SetShouldFailOnSave(shouldFail bool) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.shouldFailOnSave = shouldFail
}

func (r *MockLayoutRepository) SetShouldFailOnGet(shouldFail bool) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.shouldFailOnGet = shouldFail
}

func (r *MockLayoutRepository) SetShouldFailOnDelete(shouldFail bool) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.shouldFailOnDelete = shouldFail
}

func (r *MockLayoutRepository) GetSaveCallCount() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.saveCallCount
}

func (r *MockLayoutRepository) GetGetCallCount() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.getCallCount
}

func (r *MockLayoutRepository) GetDeleteCallCount() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.deleteCallCount
}

func (r *MockLayoutRepository) GetLayoutCount() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return len(r.layouts)
}

func (r *MockLayoutRepository) Clear() {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.layouts = make(map[string]*domain.Layout)
	r.saveCallCount = 0
	r.getCallCount = 0
	r.deleteCallCount = 0
}

// MockContentRepository is a mock implementation of ContentRepository for testing
type MockContentRepository struct {
	contents map[string]*domain.Content
	mutex    sync.RWMutex
	
	// Configuration for testing behavior
	shouldFailOnSave   bool
	shouldFailOnGet    bool
	shouldFailOnDelete bool
	saveCallCount      int
	getCallCount       int
	deleteCallCount    int
}

// NewMockContentRepository creates a new mock content repository
func NewMockContentRepository() *MockContentRepository {
	return &MockContentRepository{
		contents: make(map[string]*domain.Content),
	}
}

func (r *MockContentRepository) GetByID(id string) (*domain.Content, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	r.getCallCount++
	
	if r.shouldFailOnGet {
		return nil, fmt.Errorf("mock repository configured to fail on get")
	}
	
	content, exists := r.contents[id]
	if !exists {
		return nil, fmt.Errorf("content with id %s not found", id)
	}
	
	return content, nil
}

func (r *MockContentRepository) Save(content *domain.Content) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	r.saveCallCount++
	
	if r.shouldFailOnSave {
		return fmt.Errorf("mock repository configured to fail on save")
	}
	
	r.contents[content.ID()] = content
	return nil
}

func (r *MockContentRepository) Delete(id string) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	r.deleteCallCount++
	
	if r.shouldFailOnDelete {
		return fmt.Errorf("mock repository configured to fail on delete")
	}
	
	delete(r.contents, id)
	return nil
}

func (r *MockContentRepository) FindBySource(source domain.ContentSource) ([]*domain.Content, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	var results []*domain.Content
	for _, content := range r.contents {
		if content.Source().Equals(source) {
			results = append(results, content)
		}
	}
	
	return results, nil
}

func (r *MockContentRepository) FindByState(state domain.ContentState) ([]*domain.Content, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	var results []*domain.Content
	for _, content := range r.contents {
		if content.State() == state {
			results = append(results, content)
		}
	}
	
	return results, nil
}

func (r *MockContentRepository) FindByMimeType(mimeType string) ([]*domain.Content, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	var results []*domain.Content
	for _, content := range r.contents {
		if content.Metadata().MimeType == mimeType {
			results = append(results, content)
		}
	}
	
	return results, nil
}

// Test helper methods

func (r *MockContentRepository) SetShouldFailOnSave(shouldFail bool) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.shouldFailOnSave = shouldFail
}

func (r *MockContentRepository) SetShouldFailOnGet(shouldFail bool) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.shouldFailOnGet = shouldFail
}

func (r *MockContentRepository) SetShouldFailOnDelete(shouldFail bool) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.shouldFailOnDelete = shouldFail
}

func (r *MockContentRepository) GetSaveCallCount() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.saveCallCount
}

func (r *MockContentRepository) GetGetCallCount() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.getCallCount
}

func (r *MockContentRepository) GetDeleteCallCount() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.deleteCallCount
}

func (r *MockContentRepository) GetContentCount() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return len(r.contents)
}

func (r *MockContentRepository) Clear() {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.contents = make(map[string]*domain.Content)
	r.saveCallCount = 0
	r.getCallCount = 0
	r.deleteCallCount = 0
}
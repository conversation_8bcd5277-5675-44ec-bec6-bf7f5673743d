// Package testutil provides testing utilities and helpers for the TermiLLM platform
package testutil

import (
	"context"
	"fmt"
	"testing"
	"time"

	"eionz.com/demo/pkg/container"
	"eionz.com/demo/pkg/domain"
	"eionz.com/demo/pkg/eventbus"
)

// TestFixture provides a complete testing environment
type TestFixture struct {
	t             *testing.T
	Container     *container.Container
	EventBus      *eventbus.EventBus
	Context       context.Context
	Cancel        context.CancelFunc
	MockHandlers  map[string]*MockEventHandler
	MockRepos     map[string]interface{}
	TestData      map[string]interface{}
	tempDir       string
	cleanup       []func()
}

// NewTestFixture creates a new test fixture with all necessary components
func NewTestFixture(t *testing.T) *TestFixture {
	ctx, cancel := context.WithCancel(context.Background())
	
	// Create container
	cont := container.NewContainer()
	
	// Create event bus with test-friendly configuration
	config := eventbus.EventBusConfig{
		MaxRetries:      1,
		RetryDelay:      10 * time.Millisecond,
		BufferSize:      100,
		MaxConcurrency:  2,
		EnableMetrics:   true,
		TimeoutDuration: 1 * time.Second,
	}
	bus := eventbus.NewEventBus(config)
	
	fixture := &TestFixture{
		t:            t,
		Container:    cont,
		EventBus:     bus,
		Context:      ctx,
		Cancel:       cancel,
		MockHandlers: make(map[string]*MockEventHandler),
		MockRepos:    make(map[string]interface{}),
		TestData:     make(map[string]interface{}),
		cleanup:      make([]func(), 0),
	}
	
	// Register common test services
	fixture.registerTestServices()
	
	return fixture
}

// Setup initializes the test fixture
func (f *TestFixture) Setup() error {
	// Start event bus
	if err := f.EventBus.Start(); err != nil {
		return err
	}
	
	// Register cleanup for event bus
	f.AddCleanup(func() {
		f.EventBus.Stop()
	})
	
	return nil
}

// Cleanup runs all registered cleanup functions
func (f *TestFixture) Cleanup() {
	f.Cancel()
	
	// Run cleanup functions in reverse order
	for i := len(f.cleanup) - 1; i >= 0; i-- {
		f.cleanup[i]()
	}
}

// AddCleanup registers a cleanup function
func (f *TestFixture) AddCleanup(fn func()) {
	f.cleanup = append(f.cleanup, fn)
}

// SetTestData stores test data for later retrieval
func (f *TestFixture) SetTestData(key string, value interface{}) {
	f.TestData[key] = value
}

// GetTestData retrieves stored test data
func (f *TestFixture) GetTestData(key string) (interface{}, bool) {
	value, exists := f.TestData[key]
	return value, exists
}

// CreateMockEventHandler creates and registers a mock event handler
func (f *TestFixture) CreateMockEventHandler(name string, eventTypes []string) *MockEventHandler {
	handler := NewMockEventHandler(name, eventTypes)
	f.MockHandlers[name] = handler
	
	// Subscribe to event bus
	for _, eventType := range eventTypes {
		f.EventBus.SubscribeToEventType(eventType, handler)
	}
	
	return handler
}

// GetMockEventHandler retrieves a mock event handler by name
func (f *TestFixture) GetMockEventHandler(name string) (*MockEventHandler, bool) {
	handler, exists := f.MockHandlers[name]
	return handler, exists
}

// PublishEvent publishes an event and waits for processing
func (f *TestFixture) PublishEvent(event domain.DomainEvent) error {
	err := f.EventBus.Publish(event)
	if err != nil {
		return err
	}
	
	// Wait a short time for event processing
	time.Sleep(20 * time.Millisecond)
	return nil
}

// AssertEventHandled verifies that an event was handled by a specific handler
func (f *TestFixture) AssertEventHandled(handlerName, eventType string, count int) {
	handler, exists := f.GetMockEventHandler(handlerName)
	if !exists {
		f.t.Errorf("Handler %s not found", handlerName)
		return
	}
	
	actualCount := handler.GetEventCount(eventType)
	if actualCount != count {
		f.t.Errorf("Expected handler %s to handle %d events of type %s, got %d", 
			handlerName, count, eventType, actualCount)
	}
}

// AssertNoEvents verifies that no events were handled by a handler
func (f *TestFixture) AssertNoEvents(handlerName string) {
	handler, exists := f.GetMockEventHandler(handlerName)
	if !exists {
		f.t.Errorf("Handler %s not found", handlerName)
		return
	}
	
	totalCount := handler.GetTotalEventCount()
	if totalCount > 0 {
		f.t.Errorf("Expected handler %s to handle no events, got %d", handlerName, totalCount)
	}
}

// registerTestServices registers common services for testing
func (f *TestFixture) registerTestServices() {
	// Register mock repositories
	f.Container.RegisterSingleton("layoutRepository", func() (interface{}, error) {
		repo := NewMockLayoutRepository()
		f.MockRepos["layoutRepository"] = repo
		return repo, nil
	})
	
	f.Container.RegisterSingleton("contentRepository", func() (interface{}, error) {
		repo := NewMockContentRepository()
		f.MockRepos["contentRepository"] = repo
		return repo, nil
	})
	
	// Register event bus
	f.Container.RegisterInstance("eventBus", f.EventBus)
}

// MockEventHandler is a mock implementation of EventHandler for testing
type MockEventHandler struct {
	name         string
	eventTypes   []string
	handledEvents map[string][]domain.DomainEvent
	shouldFail   bool
	failureError error
}

// NewMockEventHandler creates a new mock event handler
func NewMockEventHandler(name string, eventTypes []string) *MockEventHandler {
	return &MockEventHandler{
		name:          name,
		eventTypes:    eventTypes,
		handledEvents: make(map[string][]domain.DomainEvent),
		shouldFail:    false,
	}
}

func (h *MockEventHandler) Handle(ctx context.Context, event domain.DomainEvent) error {
	if h.shouldFail {
		if h.failureError != nil {
			return h.failureError
		}
		return fmt.Errorf("mock handler %s configured to fail", h.name)
	}
	
	eventType := event.EventType()
	if h.handledEvents[eventType] == nil {
		h.handledEvents[eventType] = make([]domain.DomainEvent, 0)
	}
	
	h.handledEvents[eventType] = append(h.handledEvents[eventType], event)
	return nil
}

func (h *MockEventHandler) CanHandle(eventType string) bool {
	for _, et := range h.eventTypes {
		if et == eventType {
			return true
		}
	}
	return false
}

func (h *MockEventHandler) HandlerName() string {
	return h.name
}

// SetShouldFail configures the handler to fail
func (h *MockEventHandler) SetShouldFail(shouldFail bool, err error) {
	h.shouldFail = shouldFail
	h.failureError = err
}

// GetEventCount returns the number of events of a specific type handled
func (h *MockEventHandler) GetEventCount(eventType string) int {
	events, exists := h.handledEvents[eventType]
	if !exists {
		return 0
	}
	return len(events)
}

// GetEvents returns all events of a specific type handled
func (h *MockEventHandler) GetEvents(eventType string) []domain.DomainEvent {
	events, exists := h.handledEvents[eventType]
	if !exists {
		return make([]domain.DomainEvent, 0)
	}
	
	// Return copy to prevent external modification
	result := make([]domain.DomainEvent, len(events))
	copy(result, events)
	return result
}

// GetTotalEventCount returns the total number of events handled
func (h *MockEventHandler) GetTotalEventCount() int {
	total := 0
	for _, events := range h.handledEvents {
		total += len(events)
	}
	return total
}

// GetAllEvents returns all events handled by this handler
func (h *MockEventHandler) GetAllEvents() map[string][]domain.DomainEvent {
	result := make(map[string][]domain.DomainEvent)
	for eventType, events := range h.handledEvents {
		result[eventType] = make([]domain.DomainEvent, len(events))
		copy(result[eventType], events)
	}
	return result
}

// Reset clears all handled events
func (h *MockEventHandler) Reset() {
	h.handledEvents = make(map[string][]domain.DomainEvent)
	h.shouldFail = false
	h.failureError = nil
}
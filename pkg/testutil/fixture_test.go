package testutil

import (
	"testing"

	"eionz.com/demo/pkg/domain"
)

func TestTestFixture_Creation(t *testing.T) {
	fixture := NewTestFixture(t)
	defer fixture.Cleanup()
	
	if fixture.Container == nil {
		t.Error("Container should not be nil")
	}
	
	if fixture.EventBus == nil {
		t.Error("EventBus should not be nil")
	}
	
	if fixture.Context == nil {
		t.Error("Context should not be nil")
	}
}

func TestTestFixture_Setup(t *testing.T) {
	fixture := NewTestFixture(t)
	defer fixture.Cleanup()
	
	err := fixture.Setup()
	if err != nil {
		t.Fatalf("Setup should not fail: %v", err)
	}
	
	if !fixture.EventBus.IsStarted() {
		t.Error("EventBus should be started after setup")
	}
}

func TestTestFixture_MockEventHandler(t *testing.T) {
	fixture := NewTestFixture(t)
	defer fixture.Cleanup()
	
	err := fixture.Setup()
	if err != nil {
		t.Fatalf("Setup failed: %v", err)
	}
	
	// Create mock handler
	handler := fixture.CreateMockEventHandler("test-handler", []string{"TestEvent"})
	
	if handler == nil {
		t.Fatal("Handler should not be nil")
	}
	
	// Verify handler is registered
	retrievedHandler, exists := fixture.GetMockEventHandler("test-handler")
	if !exists {
		t.Error("Handler should be registered")
	}
	
	if retrievedHandler != handler {
		t.Error("Retrieved handler should be the same instance")
	}
}

func TestTestFixture_EventHandling(t *testing.T) {
	fixture := NewTestFixture(t)
	defer fixture.Cleanup()
	
	err := fixture.Setup()
	if err != nil {
		t.Fatalf("Setup failed: %v", err)
	}
	
	// Create mock handler
	handler := fixture.CreateMockEventHandler("test-handler", []string{"LayoutCreated"})
	
	// Create and publish event
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	event := domain.NewLayoutCreatedEvent(layoutID, dimensions)
	
	err = fixture.PublishEvent(event)
	if err != nil {
		t.Fatalf("Publishing event failed: %v", err)
	}
	
	// Verify event was handled
	fixture.AssertEventHandled("test-handler", "LayoutCreated", 1)
	
	// Verify event details
	handledEvents := handler.GetEvents("LayoutCreated")
	if len(handledEvents) != 1 {
		t.Errorf("Expected 1 handled event, got %d", len(handledEvents))
	}
	
	if handledEvents[0].EventType() != "LayoutCreated" {
		t.Errorf("Expected LayoutCreated event, got %s", handledEvents[0].EventType())
	}
}

func TestTestFixture_TestData(t *testing.T) {
	fixture := NewTestFixture(t)
	defer fixture.Cleanup()
	
	// Set test data
	fixture.SetTestData("key1", "value1")
	fixture.SetTestData("key2", 42)
	
	// Retrieve test data
	value1, exists1 := fixture.GetTestData("key1")
	if !exists1 {
		t.Error("key1 should exist")
	}
	
	if value1 != "value1" {
		t.Errorf("Expected 'value1', got %v", value1)
	}
	
	value2, exists2 := fixture.GetTestData("key2")
	if !exists2 {
		t.Error("key2 should exist")
	}
	
	if value2 != 42 {
		t.Errorf("Expected 42, got %v", value2)
	}
	
	// Test non-existent key
	_, exists3 := fixture.GetTestData("nonexistent")
	if exists3 {
		t.Error("nonexistent key should not exist")
	}
}

func TestMockLayoutRepository(t *testing.T) {
	repo := NewMockLayoutRepository()
	
	// Test save and get
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	layout := domain.NewLayout(layoutID, dimensions)
	
	err := repo.Save(layout)
	if err != nil {
		t.Fatalf("Save should not fail: %v", err)
	}
	
	retrieved, err := repo.GetByID(layout.ID())
	if err != nil {
		t.Fatalf("GetByID should not fail: %v", err)
	}
	
	if retrieved.ID() != layout.ID() {
		t.Errorf("Expected layout ID %s, got %s", layout.ID(), retrieved.ID())
	}
	
	// Test delete
	err = repo.Delete(layout.ID())
	if err != nil {
		t.Fatalf("Delete should not fail: %v", err)
	}
	
	_, err = repo.GetByID(layout.ID())
	if err == nil {
		t.Error("GetByID should fail after delete")
	}
}

func TestMockLayoutRepository_FailureConfiguration(t *testing.T) {
	repo := NewMockLayoutRepository()
	
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	layout := domain.NewLayout(layoutID, dimensions)
	
	// Configure to fail on save
	repo.SetShouldFailOnSave(true)
	
	err := repo.Save(layout)
	if err == nil {
		t.Error("Save should fail when configured to fail")
	}
	
	// Reset failure configuration
	repo.SetShouldFailOnSave(false)
	
	err = repo.Save(layout)
	if err != nil {
		t.Fatalf("Save should succeed after reset: %v", err)
	}
	
	// Configure to fail on get
	repo.SetShouldFailOnGet(true)
	
	_, err = repo.GetByID(layout.ID())
	if err == nil {
		t.Error("GetByID should fail when configured to fail")
	}
}

func TestMockContentRepository(t *testing.T) {
	repo := NewMockContentRepository()
	
	// Test save and get
	contentID := domain.NewContentID("test-content")
	source := domain.NewContentSource(domain.SourceTypeString, "test", nil)
	content := domain.NewContent(contentID, source)
	
	err := repo.Save(content)
	if err != nil {
		t.Fatalf("Save should not fail: %v", err)
	}
	
	retrieved, err := repo.GetByID(content.ID())
	if err != nil {
		t.Fatalf("GetByID should not fail: %v", err)
	}
	
	if retrieved.ID() != content.ID() {
		t.Errorf("Expected content ID %s, got %s", content.ID(), retrieved.ID())
	}
	
	// Test find by source
	contents, err := repo.FindBySource(source)
	if err != nil {
		t.Fatalf("FindBySource should not fail: %v", err)
	}
	
	if len(contents) != 1 {
		t.Errorf("Expected 1 content, got %d", len(contents))
	}
	
	if contents[0].ID() != content.ID() {
		t.Errorf("Expected content ID %s, got %s", content.ID(), contents[0].ID())
	}
}

func TestMockEventHandler(t *testing.T) {
	handler := NewMockEventHandler("test-handler", []string{"TestEvent1", "TestEvent2"})
	
	// Test CanHandle
	if !handler.CanHandle("TestEvent1") {
		t.Error("Handler should handle TestEvent1")
	}
	
	if !handler.CanHandle("TestEvent2") {
		t.Error("Handler should handle TestEvent2")
	}
	
	if handler.CanHandle("TestEvent3") {
		t.Error("Handler should not handle TestEvent3")
	}
	
	// Test event handling basics
	if handler.GetTotalEventCount() != 0 {
		t.Error("Initial event count should be 0")
	}
	
	if handler.GetEventCount("TestEvent1") != 0 {
		t.Error("Initial TestEvent1 count should be 0")
	}
	
	// Test failure configuration
	handler.SetShouldFail(true, nil)
	
	if !handler.shouldFail {
		t.Error("Handler should be configured to fail")
	}
	
	handler.Reset()
	
	if handler.shouldFail {
		t.Error("Handler should not be configured to fail after reset")
	}
	
	if handler.GetTotalEventCount() != 0 {
		t.Error("Event count should be 0 after reset")
	}
}
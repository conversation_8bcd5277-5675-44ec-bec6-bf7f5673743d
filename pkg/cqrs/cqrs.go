// Package cqrs implements Command Query Responsibility Segregation pattern
package cqrs

import (
	"context"
	"fmt"
	"reflect"
	"sync"
	"time"

	"eionz.com/demo/pkg/domain"
)

// Command represents a command in the CQRS pattern
type Command interface {
	CommandID() string
	CommandType() string
	AggregateID() string
	Timestamp() time.Time
	Validate() error
}

// Query represents a query in the CQRS pattern
type Query interface {
	QueryID() string
	QueryType() string
	Timestamp() time.Time
	Validate() error
}

// CommandResult represents the result of command execution
type CommandResult struct {
	CommandID    string
	Success      bool
	Error        error
	Events       []domain.DomainEvent
	AggregateID  string
	Version      int
	ExecutedAt   time.Time
	ExecutionTime time.Duration
	Metadata     map[string]interface{}
}

// QueryResult represents the result of query execution
type QueryResult struct {
	QueryID       string
	Success       bool
	Error         error
	Data          interface{}
	Count         int64
	ExecutedAt    time.Time
	ExecutionTime time.Duration
	Metadata      map[string]interface{}
}

// CommandHandler processes commands
type CommandHandler interface {
	Handle(ctx context.Context, command Command) (*CommandResult, error)
	CanHandle(commandType string) bool
	HandlerName() string
}

// QueryHandler processes queries
type QueryHandler interface {
	Handle(ctx context.Context, query Query) (*QueryResult, error)
	CanHandle(queryType string) bool
	HandlerName() string
}

// CommandBus routes commands to appropriate handlers
type CommandBus interface {
	Execute(ctx context.Context, command Command) (*CommandResult, error)
	RegisterHandler(handler CommandHandler) error
	UnregisterHandler(handlerName string) error
	GetRegisteredHandlers() []string
}

// QueryBus routes queries to appropriate handlers
type QueryBus interface {
	Execute(ctx context.Context, query Query) (*QueryResult, error)
	RegisterHandler(handler QueryHandler) error
	UnregisterHandler(handlerName string) error
	GetRegisteredHandlers() []string
}

// BaseCommand provides common command functionality
type BaseCommand struct {
	id          string
	commandType string
	aggregateID string
	timestamp   time.Time
	metadata    map[string]interface{}
}

func NewBaseCommand(commandType, aggregateID string) BaseCommand {
	return BaseCommand{
		id:          generateID(),
		commandType: commandType,
		aggregateID: aggregateID,
		timestamp:   time.Now(),
		metadata:    make(map[string]interface{}),
	}
}

func (bc BaseCommand) CommandID() string {
	return bc.id
}

func (bc BaseCommand) CommandType() string {
	return bc.commandType
}

func (bc BaseCommand) AggregateID() string {
	return bc.aggregateID
}

func (bc BaseCommand) Timestamp() time.Time {
	return bc.timestamp
}

func (bc BaseCommand) Validate() error {
	if bc.id == "" {
		return fmt.Errorf("command ID cannot be empty")
	}
	if bc.commandType == "" {
		return fmt.Errorf("command type cannot be empty")
	}
	if bc.aggregateID == "" {
		return fmt.Errorf("aggregate ID cannot be empty")
	}
	return nil
}

func (bc *BaseCommand) SetMetadata(key string, value interface{}) {
	bc.metadata[key] = value
}

func (bc BaseCommand) GetMetadata(key string) (interface{}, bool) {
	value, exists := bc.metadata[key]
	return value, exists
}

// BaseQuery provides common query functionality
type BaseQuery struct {
	id        string
	queryType string
	timestamp time.Time
	metadata  map[string]interface{}
}

func NewBaseQuery(queryType string) BaseQuery {
	return BaseQuery{
		id:        generateID(),
		queryType: queryType,
		timestamp: time.Now(),
		metadata:  make(map[string]interface{}),
	}
}

func (bq BaseQuery) QueryID() string {
	return bq.id
}

func (bq BaseQuery) QueryType() string {
	return bq.queryType
}

func (bq BaseQuery) Timestamp() time.Time {
	return bq.timestamp
}

func (bq BaseQuery) Validate() error {
	if bq.id == "" {
		return fmt.Errorf("query ID cannot be empty")
	}
	if bq.queryType == "" {
		return fmt.Errorf("query type cannot be empty")
	}
	return nil
}

func (bq *BaseQuery) SetMetadata(key string, value interface{}) {
	bq.metadata[key] = value
}

func (bq BaseQuery) GetMetadata(key string) (interface{}, bool) {
	value, exists := bq.metadata[key]
	return value, exists
}

// InMemoryCommandBus provides an in-memory implementation of CommandBus
type InMemoryCommandBus struct {
	handlers map[string]CommandHandler
	mutex    sync.RWMutex
	
	// Middleware
	middleware []CommandMiddleware
	
	// Metrics
	executionCount   int64
	totalExecutionTime time.Duration
	errorCount       int64
}

// CommandMiddleware allows intercepting command execution
type CommandMiddleware interface {
	Execute(ctx context.Context, command Command, next func(context.Context, Command) (*CommandResult, error)) (*CommandResult, error)
}

func NewInMemoryCommandBus() *InMemoryCommandBus {
	return &InMemoryCommandBus{
		handlers:   make(map[string]CommandHandler),
		middleware: make([]CommandMiddleware, 0),
	}
}

func (cb *InMemoryCommandBus) Execute(ctx context.Context, command Command) (*CommandResult, error) {
	if command == nil {
		return nil, fmt.Errorf("command cannot be nil")
	}
	
	if err := command.Validate(); err != nil {
		return &CommandResult{
			CommandID:  command.CommandID(),
			Success:    false,
			Error:      err,
			ExecutedAt: time.Now(),
		}, err
	}
	
	startTime := time.Now()
	
	// Find handler
	cb.mutex.RLock()
	var handler CommandHandler
	for _, h := range cb.handlers {
		if h.CanHandle(command.CommandType()) {
			handler = h
			break
		}
	}
	cb.mutex.RUnlock()
	
	if handler == nil {
		err := fmt.Errorf("no handler found for command type: %s", command.CommandType())
		cb.mutex.Lock()
		cb.errorCount++
		cb.mutex.Unlock()
		
		return &CommandResult{
			CommandID:  command.CommandID(),
			Success:    false,
			Error:      err,
			ExecutedAt: time.Now(),
		}, err
	}
	
	// Execute with middleware chain
	var result *CommandResult
	var err error
	
	if len(cb.middleware) > 0 {
		result, err = cb.executeWithMiddleware(ctx, command, handler, 0)
	} else {
		result, err = handler.Handle(ctx, command)
	}
	
	// Update metrics
	executionTime := time.Since(startTime)
	cb.mutex.Lock()
	cb.executionCount++
	cb.totalExecutionTime += executionTime
	if err != nil {
		cb.errorCount++
	}
	cb.mutex.Unlock()
	
	if result != nil {
		result.ExecutionTime = executionTime
		if result.ExecutedAt.IsZero() {
			result.ExecutedAt = time.Now()
		}
	}
	
	return result, err
}

func (cb *InMemoryCommandBus) executeWithMiddleware(ctx context.Context, command Command, handler CommandHandler, index int) (*CommandResult, error) {
	if index >= len(cb.middleware) {
		return handler.Handle(ctx, command)
	}
	
	middleware := cb.middleware[index]
	return middleware.Execute(ctx, command, func(ctx context.Context, cmd Command) (*CommandResult, error) {
		return cb.executeWithMiddleware(ctx, cmd, handler, index+1)
	})
}

func (cb *InMemoryCommandBus) RegisterHandler(handler CommandHandler) error {
	if handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}
	
	cb.mutex.Lock()
	defer cb.mutex.Unlock()
	
	handlerName := handler.HandlerName()
	if _, exists := cb.handlers[handlerName]; exists {
		return fmt.Errorf("handler %s is already registered", handlerName)
	}
	
	cb.handlers[handlerName] = handler
	return nil
}

func (cb *InMemoryCommandBus) UnregisterHandler(handlerName string) error {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()
	
	if _, exists := cb.handlers[handlerName]; !exists {
		return fmt.Errorf("handler %s is not registered", handlerName)
	}
	
	delete(cb.handlers, handlerName)
	return nil
}

func (cb *InMemoryCommandBus) GetRegisteredHandlers() []string {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	
	handlers := make([]string, 0, len(cb.handlers))
	for name := range cb.handlers {
		handlers = append(handlers, name)
	}
	
	return handlers
}

func (cb *InMemoryCommandBus) AddMiddleware(middleware CommandMiddleware) {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()
	
	cb.middleware = append(cb.middleware, middleware)
}

func (cb *InMemoryCommandBus) GetMetrics() (int64, time.Duration, int64) {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	
	return cb.executionCount, cb.totalExecutionTime, cb.errorCount
}

// InMemoryQueryBus provides an in-memory implementation of QueryBus
type InMemoryQueryBus struct {
	handlers map[string]QueryHandler
	mutex    sync.RWMutex
	
	// Middleware
	middleware []QueryMiddleware
	
	// Metrics
	executionCount     int64
	totalExecutionTime time.Duration
	errorCount         int64
}

// QueryMiddleware allows intercepting query execution
type QueryMiddleware interface {
	Execute(ctx context.Context, query Query, next func(context.Context, Query) (*QueryResult, error)) (*QueryResult, error)
}

func NewInMemoryQueryBus() *InMemoryQueryBus {
	return &InMemoryQueryBus{
		handlers:   make(map[string]QueryHandler),
		middleware: make([]QueryMiddleware, 0),
	}
}

func (qb *InMemoryQueryBus) Execute(ctx context.Context, query Query) (*QueryResult, error) {
	if query == nil {
		return nil, fmt.Errorf("query cannot be nil")
	}
	
	if err := query.Validate(); err != nil {
		return &QueryResult{
			QueryID:    query.QueryID(),
			Success:    false,
			Error:      err,
			ExecutedAt: time.Now(),
		}, err
	}
	
	startTime := time.Now()
	
	// Find handler
	qb.mutex.RLock()
	var handler QueryHandler
	for _, h := range qb.handlers {
		if h.CanHandle(query.QueryType()) {
			handler = h
			break
		}
	}
	qb.mutex.RUnlock()
	
	if handler == nil {
		err := fmt.Errorf("no handler found for query type: %s", query.QueryType())
		qb.mutex.Lock()
		qb.errorCount++
		qb.mutex.Unlock()
		
		return &QueryResult{
			QueryID:    query.QueryID(),
			Success:    false,
			Error:      err,
			ExecutedAt: time.Now(),
		}, err
	}
	
	// Execute with middleware chain
	var result *QueryResult
	var err error
	
	if len(qb.middleware) > 0 {
		result, err = qb.executeWithMiddleware(ctx, query, handler, 0)
	} else {
		result, err = handler.Handle(ctx, query)
	}
	
	// Update metrics
	executionTime := time.Since(startTime)
	qb.mutex.Lock()
	qb.executionCount++
	qb.totalExecutionTime += executionTime
	if err != nil {
		qb.errorCount++
	}
	qb.mutex.Unlock()
	
	if result != nil {
		result.ExecutionTime = executionTime
		if result.ExecutedAt.IsZero() {
			result.ExecutedAt = time.Now()
		}
	}
	
	return result, err
}

func (qb *InMemoryQueryBus) executeWithMiddleware(ctx context.Context, query Query, handler QueryHandler, index int) (*QueryResult, error) {
	if index >= len(qb.middleware) {
		return handler.Handle(ctx, query)
	}
	
	middleware := qb.middleware[index]
	return middleware.Execute(ctx, query, func(ctx context.Context, q Query) (*QueryResult, error) {
		return qb.executeWithMiddleware(ctx, q, handler, index+1)
	})
}

func (qb *InMemoryQueryBus) RegisterHandler(handler QueryHandler) error {
	if handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}
	
	qb.mutex.Lock()
	defer qb.mutex.Unlock()
	
	handlerName := handler.HandlerName()
	if _, exists := qb.handlers[handlerName]; exists {
		return fmt.Errorf("handler %s is already registered", handlerName)
	}
	
	qb.handlers[handlerName] = handler
	return nil
}

func (qb *InMemoryQueryBus) UnregisterHandler(handlerName string) error {
	qb.mutex.Lock()
	defer qb.mutex.Unlock()
	
	if _, exists := qb.handlers[handlerName]; !exists {
		return fmt.Errorf("handler %s is not registered", handlerName)
	}
	
	delete(qb.handlers, handlerName)
	return nil
}

func (qb *InMemoryQueryBus) GetRegisteredHandlers() []string {
	qb.mutex.RLock()
	defer qb.mutex.RUnlock()
	
	handlers := make([]string, 0, len(qb.handlers))
	for name := range qb.handlers {
		handlers = append(handlers, name)
	}
	
	return handlers
}

func (qb *InMemoryQueryBus) AddMiddleware(middleware QueryMiddleware) {
	qb.mutex.Lock()
	defer qb.mutex.Unlock()
	
	qb.middleware = append(qb.middleware, middleware)
}

func (qb *InMemoryQueryBus) GetMetrics() (int64, time.Duration, int64) {
	qb.mutex.RLock()
	defer qb.mutex.RUnlock()
	
	return qb.executionCount, qb.totalExecutionTime, qb.errorCount
}

// Utility functions

func generateID() string {
	// Simple ID generation - in production, use UUID or similar
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// Validation helpers

func ValidateCommandHandler(handler CommandHandler) error {
	if handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}
	
	if handler.HandlerName() == "" {
		return fmt.Errorf("handler name cannot be empty")
	}
	
	// Use reflection to check if handler implements required methods
	handlerType := reflect.TypeOf(handler)
	if handlerType.Kind() == reflect.Ptr {
		handlerType = handlerType.Elem()
	}
	
	// Check if Handle method exists with correct signature
	handleMethod, exists := handlerType.MethodByName("Handle")
	if !exists {
		return fmt.Errorf("handler must implement Handle method")
	}
	
	// Verify method signature
	if handleMethod.Type.NumIn() != 3 { // receiver, context, command
		return fmt.Errorf("Handle method must have correct signature: Handle(context.Context, Command) (*CommandResult, error)")
	}
	
	return nil
}

func ValidateQueryHandler(handler QueryHandler) error {
	if handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}
	
	if handler.HandlerName() == "" {
		return fmt.Errorf("handler name cannot be empty")
	}
	
	// Use reflection to check if handler implements required methods
	handlerType := reflect.TypeOf(handler)
	if handlerType.Kind() == reflect.Ptr {
		handlerType = handlerType.Elem()
	}
	
	// Check if Handle method exists with correct signature
	handleMethod, exists := handlerType.MethodByName("Handle")
	if !exists {
		return fmt.Errorf("handler must implement Handle method")
	}
	
	// Verify method signature
	if handleMethod.Type.NumIn() != 3 { // receiver, context, query
		return fmt.Errorf("Handle method must have correct signature: Handle(context.Context, Query) (*QueryResult, error)")
	}
	
	return nil
}
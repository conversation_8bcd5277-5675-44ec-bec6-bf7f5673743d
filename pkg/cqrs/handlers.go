package cqrs

import (
	"context"
	"fmt"

	"eionz.com/demo/pkg/domain"
)

// Layout Commands

// CreateLayoutCommand creates a new layout
type CreateLayoutCommand struct {
	BaseCommand
	Dimensions domain.Dimensions
}

func NewCreateLayoutCommand(aggregateID string, dimensions domain.Dimensions) *CreateLayoutCommand {
	return &CreateLayoutCommand{
		BaseCommand: NewBaseCommand("CreateLayout", aggregateID),
		Dimensions:  dimensions,
	}
}

func (cmd *CreateLayoutCommand) Validate() error {
	if err := cmd.BaseCommand.Validate(); err != nil {
		return err
	}
	
	if cmd.Dimensions.Width <= 0 || cmd.Dimensions.Height <= 0 {
		return fmt.Errorf("dimensions must be positive")
	}
	
	return nil
}

// ResizeLayoutCommand resizes an existing layout
type ResizeLayoutCommand struct {
	BaseCommand
	NewDimensions domain.Dimensions
}

func NewResizeLayoutCommand(aggregateID string, newDimensions domain.Dimensions) *ResizeLayoutCommand {
	return &ResizeLayoutCommand{
		BaseCommand:   NewBaseCommand("ResizeLayout", aggregateID),
		NewDimensions: newDimensions,
	}
}

func (cmd *ResizeLayoutCommand) Validate() error {
	if err := cmd.BaseCommand.Validate(); err != nil {
		return err
	}
	
	if cmd.NewDimensions.Width <= 0 || cmd.NewDimensions.Height <= 0 {
		return fmt.Errorf("new dimensions must be positive")
	}
	
	return nil
}

// SetFocusCommand sets focus to a component
type SetFocusCommand struct {
	BaseCommand
	ComponentID domain.ComponentID
}

func NewSetFocusCommand(aggregateID string, componentID domain.ComponentID) *SetFocusCommand {
	return &SetFocusCommand{
		BaseCommand: NewBaseCommand("SetFocus", aggregateID),
		ComponentID: componentID,
	}
}

func (cmd *SetFocusCommand) Validate() error {
	if err := cmd.BaseCommand.Validate(); err != nil {
		return err
	}
	
	if cmd.ComponentID.String() == "" {
		return fmt.Errorf("component ID cannot be empty")
	}
	
	return nil
}

// ToggleSidebarCommand toggles sidebar visibility
type ToggleSidebarCommand struct {
	BaseCommand
}

func NewToggleSidebarCommand(aggregateID string) *ToggleSidebarCommand {
	return &ToggleSidebarCommand{
		BaseCommand: NewBaseCommand("ToggleSidebar", aggregateID),
	}
}

// Content Commands

// LoadContentCommand loads content from a source
type LoadContentCommand struct {
	BaseCommand
	Source  domain.ContentSource
	Options map[string]interface{}
}

func NewLoadContentCommand(aggregateID string, source domain.ContentSource) *LoadContentCommand {
	return &LoadContentCommand{
		BaseCommand: NewBaseCommand("LoadContent", aggregateID),
		Source:      source,
		Options:     make(map[string]interface{}),
	}
}

func (cmd *LoadContentCommand) Validate() error {
	if err := cmd.BaseCommand.Validate(); err != nil {
		return err
	}
	
	if cmd.Source.Location == "" {
		return fmt.Errorf("content source location cannot be empty")
	}
	
	return nil
}

func (cmd *LoadContentCommand) SetOption(key string, value interface{}) {
	cmd.Options[key] = value
}

// TransformContentCommand transforms existing content
type TransformContentCommand struct {
	BaseCommand
	Transformation string
	Options        map[string]interface{}
}

func NewTransformContentCommand(aggregateID string, transformation string) *TransformContentCommand {
	return &TransformContentCommand{
		BaseCommand:    NewBaseCommand("TransformContent", aggregateID),
		Transformation: transformation,
		Options:        make(map[string]interface{}),
	}
}

func (cmd *TransformContentCommand) Validate() error {
	if err := cmd.BaseCommand.Validate(); err != nil {
		return err
	}
	
	if cmd.Transformation == "" {
		return fmt.Errorf("transformation type cannot be empty")
	}
	
	return nil
}

// Layout Queries

// GetLayoutQuery retrieves layout information
type GetLayoutQuery struct {
	BaseQuery
	LayoutID string
}

func NewGetLayoutQuery(layoutID string) *GetLayoutQuery {
	return &GetLayoutQuery{
		BaseQuery: NewBaseQuery("GetLayout"),
		LayoutID:  layoutID,
	}
}

func (q *GetLayoutQuery) Validate() error {
	if err := q.BaseQuery.Validate(); err != nil {
		return err
	}
	
	if q.LayoutID == "" {
		return fmt.Errorf("layout ID cannot be empty")
	}
	
	return nil
}

// ListLayoutsQuery lists layouts with optional filtering
type ListLayoutsQuery struct {
	BaseQuery
	UserID     string
	Dimensions *domain.Dimensions
	Limit      int
	Offset     int
}

func NewListLayoutsQuery() *ListLayoutsQuery {
	return &ListLayoutsQuery{
		BaseQuery: NewBaseQuery("ListLayouts"),
		Limit:     10,
		Offset:    0,
	}
}

func (q *ListLayoutsQuery) Validate() error {
	if err := q.BaseQuery.Validate(); err != nil {
		return err
	}
	
	if q.Limit < 0 {
		return fmt.Errorf("limit cannot be negative")
	}
	
	if q.Offset < 0 {
		return fmt.Errorf("offset cannot be negative")
	}
	
	return nil
}

func (q *ListLayoutsQuery) SetUserFilter(userID string) {
	q.UserID = userID
}

func (q *ListLayoutsQuery) SetDimensionFilter(dimensions domain.Dimensions) {
	q.Dimensions = &dimensions
}

func (q *ListLayoutsQuery) SetPagination(limit, offset int) {
	q.Limit = limit
	q.Offset = offset
}

// Content Queries

// GetContentQuery retrieves content information
type GetContentQuery struct {
	BaseQuery
	ContentID      string
	IncludeData    bool
	IncludeHistory bool
}

func NewGetContentQuery(contentID string) *GetContentQuery {
	return &GetContentQuery{
		BaseQuery:      NewBaseQuery("GetContent"),
		ContentID:      contentID,
		IncludeData:    false,
		IncludeHistory: false,
	}
}

func (q *GetContentQuery) Validate() error {
	if err := q.BaseQuery.Validate(); err != nil {
		return err
	}
	
	if q.ContentID == "" {
		return fmt.Errorf("content ID cannot be empty")
	}
	
	return nil
}

func (q *GetContentQuery) WithData() *GetContentQuery {
	q.IncludeData = true
	return q
}

func (q *GetContentQuery) WithHistory() *GetContentQuery {
	q.IncludeHistory = true
	return q
}

// SearchContentQuery searches content by various criteria
type SearchContentQuery struct {
	BaseQuery
	SearchTerm string
	SourceType *domain.SourceType
	MimeType   string
	State      *domain.ContentState
	Limit      int
	Offset     int
	SortBy     string
	SortOrder  string
}

func NewSearchContentQuery(searchTerm string) *SearchContentQuery {
	return &SearchContentQuery{
		BaseQuery:  NewBaseQuery("SearchContent"),
		SearchTerm: searchTerm,
		Limit:      20,
		Offset:     0,
		SortBy:     "created_at",
		SortOrder:  "desc",
	}
}

func (q *SearchContentQuery) Validate() error {
	if err := q.BaseQuery.Validate(); err != nil {
		return err
	}
	
	if q.Limit < 0 {
		return fmt.Errorf("limit cannot be negative")
	}
	
	if q.Offset < 0 {
		return fmt.Errorf("offset cannot be negative")
	}
	
	validSortOrders := map[string]bool{"asc": true, "desc": true}
	if !validSortOrders[q.SortOrder] {
		return fmt.Errorf("sort order must be 'asc' or 'desc'")
	}
	
	return nil
}

func (q *SearchContentQuery) FilterBySourceType(sourceType domain.SourceType) *SearchContentQuery {
	q.SourceType = &sourceType
	return q
}

func (q *SearchContentQuery) FilterByMimeType(mimeType string) *SearchContentQuery {
	q.MimeType = mimeType
	return q
}

func (q *SearchContentQuery) FilterByState(state domain.ContentState) *SearchContentQuery {
	q.State = &state
	return q
}

func (q *SearchContentQuery) SetPagination(limit, offset int) *SearchContentQuery {
	q.Limit = limit
	q.Offset = offset
	return q
}

func (q *SearchContentQuery) SetSorting(sortBy, sortOrder string) *SearchContentQuery {
	q.SortBy = sortBy
	q.SortOrder = sortOrder
	return q
}

// Layout Command Handlers

// LayoutCommandHandler handles layout-related commands
type LayoutCommandHandler struct {
	layoutRepository domain.LayoutRepository
	layoutService    domain.LayoutService
}

func NewLayoutCommandHandler(layoutRepo domain.LayoutRepository, layoutService domain.LayoutService) *LayoutCommandHandler {
	return &LayoutCommandHandler{
		layoutRepository: layoutRepo,
		layoutService:    layoutService,
	}
}

func (h *LayoutCommandHandler) Handle(ctx context.Context, command Command) (*CommandResult, error) {
	switch cmd := command.(type) {
	case *CreateLayoutCommand:
		return h.handleCreateLayout(ctx, cmd)
	case *ResizeLayoutCommand:
		return h.handleResizeLayout(ctx, cmd)
	case *SetFocusCommand:
		return h.handleSetFocus(ctx, cmd)
	case *ToggleSidebarCommand:
		return h.handleToggleSidebar(ctx, cmd)
	default:
		return nil, fmt.Errorf("unsupported command type: %T", command)
	}
}

func (h *LayoutCommandHandler) CanHandle(commandType string) bool {
	supportedCommands := map[string]bool{
		"CreateLayout":   true,
		"ResizeLayout":   true,
		"SetFocus":       true,
		"ToggleSidebar":  true,
	}
	
	return supportedCommands[commandType]
}

func (h *LayoutCommandHandler) HandlerName() string {
	return "LayoutCommandHandler"
}

func (h *LayoutCommandHandler) handleCreateLayout(ctx context.Context, cmd *CreateLayoutCommand) (*CommandResult, error) {
	_ = domain.NewLayoutID(cmd.AggregateID()) // layoutID for future use
	
	// Create new layout using domain service
	layout, err := h.layoutService.CreateLayout(cmd.Dimensions)
	if err != nil {
		return &CommandResult{
			CommandID:   cmd.CommandID(),
			Success:     false,
			Error:       err,
			AggregateID: cmd.AggregateID(),
		}, err
	}
	
	// Save layout
	if err := h.layoutRepository.Save(layout); err != nil {
		return &CommandResult{
			CommandID:   cmd.CommandID(),
			Success:     false,
			Error:       err,
			AggregateID: cmd.AggregateID(),
		}, err
	}
	
	// Get domain events
	events := layout.GetDomainEvents()
	layout.ClearDomainEvents()
	
	return &CommandResult{
		CommandID:   cmd.CommandID(),
		Success:     true,
		Events:      events,
		AggregateID: cmd.AggregateID(),
		Version:     layout.Version(),
	}, nil
}

func (h *LayoutCommandHandler) handleResizeLayout(ctx context.Context, cmd *ResizeLayoutCommand) (*CommandResult, error) {
	// Load layout
	layout, err := h.layoutRepository.GetByID(cmd.AggregateID())
	if err != nil {
		return &CommandResult{
			CommandID:   cmd.CommandID(),
			Success:     false,
			Error:       err,
			AggregateID: cmd.AggregateID(),
		}, err
	}
	
	// Resize layout
	if err := layout.Resize(cmd.NewDimensions); err != nil {
		return &CommandResult{
			CommandID:   cmd.CommandID(),
			Success:     false,
			Error:       err,
			AggregateID: cmd.AggregateID(),
		}, err
	}
	
	// Save layout
	if err := h.layoutRepository.Save(layout); err != nil {
		return &CommandResult{
			CommandID:   cmd.CommandID(),
			Success:     false,
			Error:       err,
			AggregateID: cmd.AggregateID(),
		}, err
	}
	
	// Get domain events
	events := layout.GetDomainEvents()
	layout.ClearDomainEvents()
	
	return &CommandResult{
		CommandID:   cmd.CommandID(),
		Success:     true,
		Events:      events,
		AggregateID: cmd.AggregateID(),
		Version:     layout.Version(),
	}, nil
}

func (h *LayoutCommandHandler) handleSetFocus(ctx context.Context, cmd *SetFocusCommand) (*CommandResult, error) {
	// Load layout
	layout, err := h.layoutRepository.GetByID(cmd.AggregateID())
	if err != nil {
		return &CommandResult{
			CommandID:   cmd.CommandID(),
			Success:     false,
			Error:       err,
			AggregateID: cmd.AggregateID(),
		}, err
	}
	
	// Set focus
	layout.SetFocus(cmd.ComponentID)
	
	// Save layout
	if err := h.layoutRepository.Save(layout); err != nil {
		return &CommandResult{
			CommandID:   cmd.CommandID(),
			Success:     false,
			Error:       err,
			AggregateID: cmd.AggregateID(),
		}, err
	}
	
	// Get domain events
	events := layout.GetDomainEvents()
	layout.ClearDomainEvents()
	
	return &CommandResult{
		CommandID:   cmd.CommandID(),
		Success:     true,
		Events:      events,
		AggregateID: cmd.AggregateID(),
		Version:     layout.Version(),
	}, nil
}

func (h *LayoutCommandHandler) handleToggleSidebar(ctx context.Context, cmd *ToggleSidebarCommand) (*CommandResult, error) {
	// Load layout
	layout, err := h.layoutRepository.GetByID(cmd.AggregateID())
	if err != nil {
		return &CommandResult{
			CommandID:   cmd.CommandID(),
			Success:     false,
			Error:       err,
			AggregateID: cmd.AggregateID(),
		}, err
	}
	
	// Toggle sidebar
	layout.ToggleSidebarVisibility()
	
	// Save layout
	if err := h.layoutRepository.Save(layout); err != nil {
		return &CommandResult{
			CommandID:   cmd.CommandID(),
			Success:     false,
			Error:       err,
			AggregateID: cmd.AggregateID(),
		}, err
	}
	
	// Get domain events
	events := layout.GetDomainEvents()
	layout.ClearDomainEvents()
	
	return &CommandResult{
		CommandID:   cmd.CommandID(),
		Success:     true,
		Events:      events,
		AggregateID: cmd.AggregateID(),
		Version:     layout.Version(),
	}, nil
}

// Layout Query Handler

// LayoutQueryHandler handles layout-related queries
type LayoutQueryHandler struct {
	layoutRepository domain.LayoutRepository
}

func NewLayoutQueryHandler(layoutRepo domain.LayoutRepository) *LayoutQueryHandler {
	return &LayoutQueryHandler{
		layoutRepository: layoutRepo,
	}
}

func (h *LayoutQueryHandler) Handle(ctx context.Context, query Query) (*QueryResult, error) {
	switch q := query.(type) {
	case *GetLayoutQuery:
		return h.handleGetLayout(ctx, q)
	case *ListLayoutsQuery:
		return h.handleListLayouts(ctx, q)
	default:
		return nil, fmt.Errorf("unsupported query type: %T", query)
	}
}

func (h *LayoutQueryHandler) CanHandle(queryType string) bool {
	supportedQueries := map[string]bool{
		"GetLayout":   true,
		"ListLayouts": true,
	}
	
	return supportedQueries[queryType]
}

func (h *LayoutQueryHandler) HandlerName() string {
	return "LayoutQueryHandler"
}

func (h *LayoutQueryHandler) handleGetLayout(ctx context.Context, query *GetLayoutQuery) (*QueryResult, error) {
	layout, err := h.layoutRepository.GetByID(query.LayoutID)
	if err != nil {
		return &QueryResult{
			QueryID: query.QueryID(),
			Success: false,
			Error:   err,
		}, err
	}
	
	return &QueryResult{
		QueryID: query.QueryID(),
		Success: true,
		Data:    layout,
		Count:   1,
	}, nil
}

func (h *LayoutQueryHandler) handleListLayouts(ctx context.Context, query *ListLayoutsQuery) (*QueryResult, error) {
	var layouts []*domain.Layout
	var err error
	
	if query.UserID != "" {
		// Get layout by user ID
		layout, err := h.layoutRepository.GetByUserID(query.UserID)
		if err != nil {
			return &QueryResult{
				QueryID: query.QueryID(),
				Success: false,
				Error:   err,
			}, err
		}
		layouts = []*domain.Layout{layout}
	} else if query.Dimensions != nil {
		// Find by dimensions
		layouts, err = h.layoutRepository.FindByDimensions(*query.Dimensions)
		if err != nil {
			return &QueryResult{
				QueryID: query.QueryID(),
				Success: false,
				Error:   err,
			}, err
		}
	} else {
		// This would require a more complete repository interface
		// For now, return empty result
		layouts = []*domain.Layout{}
	}
	
	// Apply pagination
	start := query.Offset
	end := start + query.Limit
	
	if start >= len(layouts) {
		layouts = []*domain.Layout{}
	} else if end > len(layouts) {
		layouts = layouts[start:]
	} else {
		layouts = layouts[start:end]
	}
	
	return &QueryResult{
		QueryID: query.QueryID(),
		Success: true,
		Data:    layouts,
		Count:   int64(len(layouts)),
	}, nil
}
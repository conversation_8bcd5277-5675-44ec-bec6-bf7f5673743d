package cqrs

import (
	"context"
	"testing"
	"time"

	"eionz.com/demo/pkg/domain"
	"eionz.com/demo/pkg/testutil"
)

func TestBaseCommand(t *testing.T) {
	aggregateID := "test-aggregate"
	commandType := "TestCommand"

	cmd := NewBaseCommand(commandType, aggregateID)

	if cmd.CommandID() == "" {
		t.<PERSON>rror("Command ID should not be empty")
	}

	if cmd.CommandType() != commandType {
		t.<PERSON><PERSON>("Expected command type %s, got %s", commandType, cmd.CommandType())
	}

	if cmd.AggregateID() != aggregateID {
		t.Errorf("Expected aggregate ID %s, got %s", aggregateID, cmd.AggregateID())
	}

	if cmd.Timestamp().IsZero() {
		t.Error("Timestamp should not be zero")
	}

	// Test validation
	err := cmd.Validate()
	if err != nil {
		t.Errorf("Valid command should pass validation: %v", err)
	}

	// Test metadata
	cmd.SetMetadata("key", "value")
	value, exists := cmd.GetMetadata("key")
	if !exists {
		t.<PERSON>rror("Metadata should exist")
	}
	if value != "value" {
		t.<PERSON>rf("Expected metadata value 'value', got %v", value)
	}
}

func TestBaseQuery(t *testing.T) {
	queryType := "TestQuery"

	query := NewBaseQuery(queryType)

	if query.QueryID() == "" {
		t.Error("Query ID should not be empty")
	}

	if query.QueryType() != queryType {
		t.Errorf("Expected query type %s, got %s", queryType, query.QueryType())
	}

	if query.Timestamp().IsZero() {
		t.Error("Timestamp should not be zero")
	}

	// Test validation
	err := query.Validate()
	if err != nil {
		t.Errorf("Valid query should pass validation: %v", err)
	}

	// Test metadata
	query.SetMetadata("key", "value")
	value, exists := query.GetMetadata("key")
	if !exists {
		t.Error("Metadata should exist")
	}
	if value != "value" {
		t.Errorf("Expected metadata value 'value', got %v", value)
	}
}

func TestInMemoryCommandBus(t *testing.T) {
	bus := NewInMemoryCommandBus()

	// Test initial state
	handlers := bus.GetRegisteredHandlers()
	if len(handlers) != 0 {
		t.Errorf("Expected 0 handlers initially, got %d", len(handlers))
	}

	// Create mock handler
	handler := &MockCommandHandler{
		name:              "TestHandler",
		supportedCommands: []string{"TestCommand"},
	}

	// Register handler
	err := bus.RegisterHandler(handler)
	if err != nil {
		t.Fatalf("Failed to register handler: %v", err)
	}

	// Check registered handlers
	handlers = bus.GetRegisteredHandlers()
	if len(handlers) != 1 {
		t.Errorf("Expected 1 handler after registration, got %d", len(handlers))
	}

	if handlers[0] != "TestHandler" {
		t.Errorf("Expected handler name 'TestHandler', got %s", handlers[0])
	}

	// Test command execution
	cmd := &MockCommand{
		BaseCommand: NewBaseCommand("TestCommand", "test-aggregate"),
	}

	result, err := bus.Execute(context.Background(), cmd)
	if err != nil {
		t.Fatalf("Command execution should succeed: %v", err)
	}

	if !result.Success {
		t.Error("Command result should be successful")
	}

	if result.CommandID != cmd.CommandID() {
		t.Errorf("Expected command ID %s, got %s", cmd.CommandID(), result.CommandID)
	}

	// Test metrics
	execCount, totalTime, errorCount := bus.GetMetrics()
	if execCount != 1 {
		t.Errorf("Expected 1 execution, got %d", execCount)
	}

	if totalTime <= 0 {
		t.Error("Total execution time should be positive")
	}

	if errorCount != 0 {
		t.Errorf("Expected 0 errors, got %d", errorCount)
	}

	// Test unregistering handler
	err = bus.UnregisterHandler("TestHandler")
	if err != nil {
		t.Fatalf("Failed to unregister handler: %v", err)
	}

	handlers = bus.GetRegisteredHandlers()
	if len(handlers) != 0 {
		t.Errorf("Expected 0 handlers after unregistration, got %d", len(handlers))
	}
}

func TestInMemoryQueryBus(t *testing.T) {
	bus := NewInMemoryQueryBus()

	// Create mock handler
	handler := &MockQueryHandler{
		name:             "TestQueryHandler",
		supportedQueries: []string{"TestQuery"},
	}

	// Register handler
	err := bus.RegisterHandler(handler)
	if err != nil {
		t.Fatalf("Failed to register handler: %v", err)
	}

	// Test query execution
	query := &MockQuery{
		BaseQuery: NewBaseQuery("TestQuery"),
	}

	result, err := bus.Execute(context.Background(), query)
	if err != nil {
		t.Fatalf("Query execution should succeed: %v", err)
	}

	if !result.Success {
		t.Error("Query result should be successful")
	}

	if result.QueryID != query.QueryID() {
		t.Errorf("Expected query ID %s, got %s", query.QueryID(), result.QueryID)
	}
}

func TestCreateLayoutCommand(t *testing.T) {
	dimensions, err := domain.NewDimensions(100, 50)
	if err != nil {
		t.Fatalf("Failed to create dimensions: %v", err)
	}

	cmd := NewCreateLayoutCommand("layout-1", dimensions)

	if cmd.CommandType() != "CreateLayout" {
		t.Errorf("Expected command type 'CreateLayout', got %s", cmd.CommandType())
	}

	if cmd.AggregateID() != "layout-1" {
		t.Errorf("Expected aggregate ID 'layout-1', got %s", cmd.AggregateID())
	}

	if !cmd.Dimensions.Equals(dimensions) {
		t.Error("Command dimensions should match input dimensions")
	}

	// Test validation
	err = cmd.Validate()
	if err != nil {
		t.Errorf("Valid command should pass validation: %v", err)
	}

	// Test invalid dimensions
	invalidCmd := NewCreateLayoutCommand("layout-1", domain.Dimensions{Width: -1, Height: 50})
	err = invalidCmd.Validate()
	if err == nil {
		t.Error("Command with invalid dimensions should fail validation")
	}
}

func TestLayoutCommandHandler(t *testing.T) {
	// Create test fixtures
	fixture := testutil.NewTestFixture(t)
	defer fixture.Cleanup()

	err := fixture.Setup()
	if err != nil {
		t.Fatalf("Failed to setup test fixture: %v", err)
	}

	// Get mock repository
	mockRepo, exists := fixture.MockRepos["layoutRepository"]
	if !exists {
		t.Fatal("Layout repository should be available in test fixture")
	}

	layoutRepo := mockRepo.(*testutil.MockLayoutRepository)

	// Create a simple mock layout service
	layoutService := &MockLayoutService{}

	// Create handler
	handler := NewLayoutCommandHandler(layoutRepo, layoutService)

	// Test handler properties
	if handler.HandlerName() != "LayoutCommandHandler" {
		t.Errorf("Expected handler name 'LayoutCommandHandler', got %s", handler.HandlerName())
	}

	if !handler.CanHandle("CreateLayout") {
		t.Error("Handler should support CreateLayout command")
	}

	if handler.CanHandle("UnsupportedCommand") {
		t.Error("Handler should not support unsupported commands")
	}

	// Test create layout command
	dimensions, _ := domain.NewDimensions(100, 50)
	cmd := NewCreateLayoutCommand("layout-1", dimensions)

	result, err := handler.Handle(context.Background(), cmd)
	if err != nil {
		t.Fatalf("Handle should succeed: %v", err)
	}

	if !result.Success {
		t.Error("Command result should be successful")
	}

	if result.CommandID != cmd.CommandID() {
		t.Errorf("Expected command ID %s, got %s", cmd.CommandID(), result.CommandID)
	}

	// Verify layout was saved
	if layoutRepo.GetSaveCallCount() != 1 {
		t.Errorf("Expected 1 save call, got %d", layoutRepo.GetSaveCallCount())
	}
}

func TestGetLayoutQuery(t *testing.T) {
	query := NewGetLayoutQuery("layout-1")

	if query.QueryType() != "GetLayout" {
		t.Errorf("Expected query type 'GetLayout', got %s", query.QueryType())
	}

	if query.LayoutID != "layout-1" {
		t.Errorf("Expected layout ID 'layout-1', got %s", query.LayoutID)
	}

	// Test validation
	err := query.Validate()
	if err != nil {
		t.Errorf("Valid query should pass validation: %v", err)
	}

	// Test invalid query
	invalidQuery := NewGetLayoutQuery("")
	err = invalidQuery.Validate()
	if err == nil {
		t.Error("Query with empty layout ID should fail validation")
	}
}

func TestLayoutQueryHandler(t *testing.T) {
	// Create test fixtures
	fixture := testutil.NewTestFixture(t)
	defer fixture.Cleanup()

	err := fixture.Setup()
	if err != nil {
		t.Fatalf("Failed to setup test fixture: %v", err)
	}

	// Get mock repository
	mockRepo, exists := fixture.MockRepos["layoutRepository"]
	if !exists {
		t.Fatal("Layout repository should be available in test fixture")
	}

	layoutRepo := mockRepo.(*testutil.MockLayoutRepository)

	// Create and save a test layout
	layoutID := domain.NewLayoutID("layout-1")
	dimensions, _ := domain.NewDimensions(100, 50)
	layout := domain.NewLayout(layoutID, dimensions)
	layoutRepo.Save(layout)

	// Create handler
	handler := NewLayoutQueryHandler(layoutRepo)

	// Test get layout query
	query := NewGetLayoutQuery("layout-1")
	result, err := handler.Handle(context.Background(), query)
	if err != nil {
		t.Fatalf("Handle should succeed: %v", err)
	}

	if !result.Success {
		t.Error("Query result should be successful")
	}

	if result.Count != 1 {
		t.Errorf("Expected count 1, got %d", result.Count)
	}

	// Verify returned layout
	returnedLayout, ok := result.Data.(*domain.Layout)
	if !ok {
		t.Fatal("Result data should be a layout")
	}

	if returnedLayout.ID() != layout.ID() {
		t.Errorf("Expected layout ID %s, got %s", layout.ID(), returnedLayout.ID())
	}
}

// Mock implementations for testing

type MockCommand struct {
	BaseCommand
}

func (mc *MockCommand) Validate() error {
	return mc.BaseCommand.Validate()
}

type MockQuery struct {
	BaseQuery
}

func (mq *MockQuery) Validate() error {
	return mq.BaseQuery.Validate()
}

type MockCommandHandler struct {
	name              string
	supportedCommands []string
	handleFunc        func(context.Context, Command) (*CommandResult, error)
}

func (mch *MockCommandHandler) Handle(
	ctx context.Context,
	command Command,
) (*CommandResult, error) {
	if mch.handleFunc != nil {
		return mch.handleFunc(ctx, command)
	}

	return &CommandResult{
		CommandID:   command.CommandID(),
		Success:     true,
		AggregateID: command.AggregateID(),
		ExecutedAt:  time.Now(),
	}, nil
}

func (mch *MockCommandHandler) CanHandle(commandType string) bool {
	for _, supported := range mch.supportedCommands {
		if supported == commandType {
			return true
		}
	}
	return false
}

func (mch *MockCommandHandler) HandlerName() string {
	return mch.name
}

type MockQueryHandler struct {
	name             string
	supportedQueries []string
	handleFunc       func(context.Context, Query) (*QueryResult, error)
}

func (mqh *MockQueryHandler) Handle(ctx context.Context, query Query) (*QueryResult, error) {
	if mqh.handleFunc != nil {
		return mqh.handleFunc(ctx, query)
	}

	return &QueryResult{
		QueryID:    query.QueryID(),
		Success:    true,
		Data:       "mock data",
		Count:      1,
		ExecutedAt: time.Now(),
	}, nil
}

func (mqh *MockQueryHandler) CanHandle(queryType string) bool {
	for _, supported := range mqh.supportedQueries {
		if supported == queryType {
			return true
		}
	}
	return false
}

func (mqh *MockQueryHandler) HandlerName() string {
	return mqh.name
}

type MockLayoutService struct{}

func (mls *MockLayoutService) CreateLayout(dimensions domain.Dimensions) (*domain.Layout, error) {
	layoutID := domain.NewLayoutID("mock-layout")
	return domain.NewLayout(layoutID, dimensions), nil
}

func (mls *MockLayoutService) ResizeLayout(
	layoutID domain.LayoutID,
	dimensions domain.Dimensions,
) error {
	return nil
}

func (mls *MockLayoutService) SetFocus(
	layoutID domain.LayoutID,
	componentID domain.ComponentID,
) error {
	return nil
}

func (mls *MockLayoutService) ToggleSidebar(layoutID domain.LayoutID) error {
	return nil
}

func (mls *MockLayoutService) OptimizeLayout(layout *domain.Layout) (*domain.Layout, error) {
	return layout, nil
}


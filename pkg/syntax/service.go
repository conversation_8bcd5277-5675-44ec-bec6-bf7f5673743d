package syntax

import (
	"fmt"
	"sync"

	"eionz.com/demo/pkg/domain"
)

// SyntaxHighlightingService provides syntax highlighting functionality
type SyntaxHighlightingService struct {
	highlighter *SyntaxHighlighter
	cache       map[string]string // Cache highlighted content by content ID
	mu          sync.RWMutex
	enabled     bool
}

// NewSyntaxHighlightingService creates a new syntax highlighting service
func NewSyntaxHighlightingService() *SyntaxHighlightingService {
	return &SyntaxHighlightingService{
		highlighter: NewSyntaxHighlighter(),
		cache:       make(map[string]string),
		enabled:     true,
	}
}

// NewSyntaxHighlightingServiceWithTheme creates a service with a custom theme
func NewSyntaxHighlightingServiceWithTheme(theme SyntaxTheme) *SyntaxHighlightingService {
	return &SyntaxHighlightingService{
		highlighter: NewSyntaxHighlighterWithTheme(theme),
		cache:       make(map[string]string),
		enabled:     true,
	}
}

// SetTheme updates the highlighting theme
func (shs *SyntaxHighlightingService) SetTheme(theme SyntaxTheme) {
	shs.mu.Lock()
	defer shs.mu.Unlock()
	
	shs.highlighter.theme = theme
	// Clear cache since theme changed
	shs.cache = make(map[string]string)
}

// SetEnabled enables or disables syntax highlighting
func (shs *SyntaxHighlightingService) SetEnabled(enabled bool) {
	shs.mu.Lock()
	defer shs.mu.Unlock()
	
	shs.enabled = enabled
}

// IsEnabled returns whether syntax highlighting is enabled
func (shs *SyntaxHighlightingService) IsEnabled() bool {
	shs.mu.RLock()
	defer shs.mu.RUnlock()
	
	return shs.enabled
}

// HighlightContent applies syntax highlighting to content
func (shs *SyntaxHighlightingService) HighlightContent(content *domain.Content, options map[string]interface{}) (*domain.Content, error) {
	shs.mu.Lock()
	defer shs.mu.Unlock()
	
	if !shs.enabled {
		return content, nil
	}
	
	if !shs.highlighter.CanTransform(content) {
		return content, nil
	}
	
	// Check cache first
	contentID := content.ID()
	if cached, exists := shs.cache[contentID]; exists {
		// Create new content with cached highlighted data
		highlightedContent := *content
		highlightedContent.SetLoaded([]byte(cached), content.Metadata())
		return &highlightedContent, nil
	}
	
	// Apply highlighting
	highlightedData, newMetadata, err := shs.highlighter.Transform(content, options)
	if err != nil {
		return nil, fmt.Errorf("failed to highlight content: %w", err)
	}
	
	// Cache the result
	shs.cache[contentID] = string(highlightedData)
	
	// Create new content with highlighted data
	highlightedContent := *content
	err = highlightedContent.Transform(highlightedData, newMetadata)
	if err != nil {
		return nil, fmt.Errorf("failed to transform content: %w", err)
	}
	
	return &highlightedContent, nil
}

// HighlightText directly highlights text with specified language
func (shs *SyntaxHighlightingService) HighlightText(text, language string) string {
	if !shs.enabled {
		return text
	}
	
	shs.mu.RLock()
	highlighter := shs.highlighter
	shs.mu.RUnlock()
	
	return highlighter.highlightText(text, language)
}

// ClearCache clears the highlighting cache
func (shs *SyntaxHighlightingService) ClearCache() {
	shs.mu.Lock()
	defer shs.mu.Unlock()
	
	shs.cache = make(map[string]string)
}

// GetCacheSize returns the number of cached items
func (shs *SyntaxHighlightingService) GetCacheSize() int {
	shs.mu.RLock()
	defer shs.mu.RUnlock()
	
	return len(shs.cache)
}

// GetSupportedLanguages returns supported languages for highlighting
func (shs *SyntaxHighlightingService) GetSupportedLanguages() []string {
	return GetSupportedLanguages()
}

// ContentHighlighter provides an interface for integrating with content loaders
type ContentHighlighter interface {
	HighlightContent(content *domain.Content, options map[string]interface{}) (*domain.Content, error)
	HighlightText(text, language string) string
	SetEnabled(enabled bool)
	IsEnabled() bool
	ClearCache()
}

// Global syntax highlighting service instance
var globalSyntaxService *SyntaxHighlightingService
var globalSyntaxServiceOnce sync.Once

// GetGlobalSyntaxService returns the global syntax highlighting service instance
func GetGlobalSyntaxService() *SyntaxHighlightingService {
	globalSyntaxServiceOnce.Do(func() {
		globalSyntaxService = NewSyntaxHighlightingService()
	})
	return globalSyntaxService
}

// InitializeGlobalSyntaxService initializes the global service with a specific theme
func InitializeGlobalSyntaxService(theme SyntaxTheme) {
	globalSyntaxServiceOnce.Do(func() {
		globalSyntaxService = NewSyntaxHighlightingServiceWithTheme(theme)
	})
}
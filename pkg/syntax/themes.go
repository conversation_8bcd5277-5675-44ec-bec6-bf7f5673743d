package syntax

import (
	"strings"

	"github.com/charmbracelet/lipgloss"
)

// DefaultSyntaxTheme returns a default syntax highlighting theme
func DefaultSyntaxTheme() SyntaxTheme {
	return SyntaxTheme{
		Keyword:     lipgloss.NewStyle().Foreground(lipgloss.Color("#569CD6")), // Blue
		String:      lipgloss.NewStyle().Foreground(lipgloss.Color("#CE9178")), // Orange
		Comment:     lipgloss.NewStyle().Foreground(lipgloss.Color("#6A9955")), // Green
		Function:    lipgloss.NewStyle().Foreground(lipgloss.Color("#DCDCAA")), // Yellow
		Type:        lipgloss.NewStyle().Foreground(lipgloss.Color("#4EC9B0")), // Cyan
		Number:      lipgloss.NewStyle().Foreground(lipgloss.Color("#B5CEA8")), // Light Green
		Operator:    lipgloss.NewStyle().Foreground(lipgloss.Color("#D4D4D4")), // Light Gray
		Variable:    lipgloss.NewStyle().Foreground(lipgloss.Color("#9CDCFE")), // Light Blue
		Constant:    lipgloss.NewStyle().Foreground(lipgloss.Color("#4FC1FF")), // Bright Blue
		Punctuation: lipgloss.NewStyle().Foreground(lipgloss.Color("#D4D4D4")), // Light Gray
		Error:       lipgloss.NewStyle().Foreground(lipgloss.Color("#F44747")), // Red
		Background:  lipgloss.NewStyle().Foreground(lipgloss.Color("#D4D4D4")), // Default text
	}
}

// DarkSyntaxTheme returns a dark theme optimized for terminal use
func DarkSyntaxTheme() SyntaxTheme {
	return SyntaxTheme{
		Keyword:     lipgloss.NewStyle().Foreground(lipgloss.Color("12")),  // Bright Blue
		String:      lipgloss.NewStyle().Foreground(lipgloss.Color("11")),  // Bright Yellow
		Comment:     lipgloss.NewStyle().Foreground(lipgloss.Color("8")),   // Gray
		Function:    lipgloss.NewStyle().Foreground(lipgloss.Color("10")),  // Bright Green
		Type:        lipgloss.NewStyle().Foreground(lipgloss.Color("14")),  // Bright Cyan
		Number:      lipgloss.NewStyle().Foreground(lipgloss.Color("13")),  // Bright Magenta
		Operator:    lipgloss.NewStyle().Foreground(lipgloss.Color("7")),   // Light Gray
		Variable:    lipgloss.NewStyle().Foreground(lipgloss.Color("9")),   // Bright Red
		Constant:    lipgloss.NewStyle().Foreground(lipgloss.Color("6")),   // Cyan
		Punctuation: lipgloss.NewStyle().Foreground(lipgloss.Color("7")),   // Light Gray
		Error:       lipgloss.NewStyle().Foreground(lipgloss.Color("1")),   // Red
		Background:  lipgloss.NewStyle().Foreground(lipgloss.Color("15")),  // White
	}
}

// LightSyntaxTheme returns a light theme
func LightSyntaxTheme() SyntaxTheme {
	return SyntaxTheme{
		Keyword:     lipgloss.NewStyle().Foreground(lipgloss.Color("4")),   // Blue
		String:      lipgloss.NewStyle().Foreground(lipgloss.Color("2")),   // Green
		Comment:     lipgloss.NewStyle().Foreground(lipgloss.Color("8")),   // Gray
		Function:    lipgloss.NewStyle().Foreground(lipgloss.Color("5")),   // Magenta
		Type:        lipgloss.NewStyle().Foreground(lipgloss.Color("6")),   // Cyan
		Number:      lipgloss.NewStyle().Foreground(lipgloss.Color("1")),   // Red
		Operator:    lipgloss.NewStyle().Foreground(lipgloss.Color("0")),   // Black
		Variable:    lipgloss.NewStyle().Foreground(lipgloss.Color("3")),   // Yellow
		Constant:    lipgloss.NewStyle().Foreground(lipgloss.Color("4")),   // Blue
		Punctuation: lipgloss.NewStyle().Foreground(lipgloss.Color("0")),   // Black
		Error:       lipgloss.NewStyle().Foreground(lipgloss.Color("1")),   // Red
		Background:  lipgloss.NewStyle().Foreground(lipgloss.Color("0")),   // Black
	}
}

// MonochromeSyntaxTheme returns a monochrome theme using only styling
func MonochromeSyntaxTheme() SyntaxTheme {
	return SyntaxTheme{
		Keyword:     lipgloss.NewStyle().Bold(true),
		String:      lipgloss.NewStyle().Italic(true),
		Comment:     lipgloss.NewStyle().Faint(true),
		Function:    lipgloss.NewStyle().Underline(true),
		Type:        lipgloss.NewStyle().Bold(true).Underline(true),
		Number:      lipgloss.NewStyle().Bold(true),
		Operator:    lipgloss.NewStyle(),
		Variable:    lipgloss.NewStyle().Italic(true),
		Constant:    lipgloss.NewStyle().Bold(true).Italic(true),
		Punctuation: lipgloss.NewStyle(),
		Error:       lipgloss.NewStyle().Bold(true).Blink(true),
		Background:  lipgloss.NewStyle(),
	}
}

// GetLanguageFromExtension returns the language identifier for a file extension
func GetLanguageFromExtension(ext string) string {
	ext = strings.ToLower(ext)
	
	languageMap := map[string]string{
		".go":     "go",
		".js":     "javascript",
		".jsx":    "javascript",
		".ts":     "typescript",
		".tsx":    "typescript",
		".py":     "python",
		".pyw":    "python",
		".json":   "json",
		".yaml":   "yaml",
		".yml":    "yaml",
		".md":     "markdown",
		".markdown": "markdown",
		".sql":    "sql",
		".xml":    "xml",
		".html":   "html",
		".htm":    "html",
		".css":    "css",
		".scss":   "scss",
		".sass":   "sass",
		".sh":     "shell",
		".bash":   "shell",
		".zsh":    "shell",
		".fish":   "shell",
		".java":   "java",
		".c":      "c",
		".cpp":    "cpp",
		".cxx":    "cpp",
		".cc":     "cpp",
		".h":      "c",
		".hpp":    "cpp",
		".cs":     "csharp",
		".php":    "php",
		".rb":     "ruby",
		".rs":     "rust",
		".scala":  "scala",
		".kt":     "kotlin",
		".swift":  "swift",
		".dart":   "dart",
		".lua":    "lua",
		".r":      "r",
		".R":      "r",
		".pl":     "perl",
		".pm":     "perl",
		".tex":    "latex",
		".dockerfile": "dockerfile",
		".toml":   "toml",
		".ini":    "ini",
		".cfg":    "ini",
		".conf":   "ini",
		".log":    "log",
	}
	
	if language, exists := languageMap[ext]; exists {
		return language
	}
	
	return "text"
}

// IsSupportedFileExtension checks if a file extension is supported for syntax highlighting
func IsSupportedFileExtension(ext string) bool {
	return GetLanguageFromExtension(ext) != "text"
}

// GetSupportedLanguages returns a list of all supported programming languages
func GetSupportedLanguages() []string {
	return []string{
		"go", "javascript", "typescript", "python", "json", "yaml", "markdown",
		"sql", "xml", "html", "css", "scss", "sass", "shell", "java", "c", "cpp",
		"csharp", "php", "ruby", "rust", "scala", "kotlin", "swift", "dart",
		"lua", "r", "perl", "latex", "dockerfile", "toml", "ini", "log", "text",
	}
}

// GetThemeByName returns a predefined theme by name
func GetThemeByName(name string) SyntaxTheme {
	switch strings.ToLower(name) {
	case "dark":
		return DarkSyntaxTheme()
	case "light":
		return LightSyntaxTheme()
	case "monochrome", "mono":
		return MonochromeSyntaxTheme()
	default:
		return DefaultSyntaxTheme()
	}
}

// CreateCustomTheme creates a custom theme from color specifications
func CreateCustomTheme(colors map[string]string) SyntaxTheme {
	theme := DefaultSyntaxTheme()
	
	if color, ok := colors["keyword"]; ok {
		theme.Keyword = lipgloss.NewStyle().Foreground(lipgloss.Color(color))
	}
	if color, ok := colors["string"]; ok {
		theme.String = lipgloss.NewStyle().Foreground(lipgloss.Color(color))
	}
	if color, ok := colors["comment"]; ok {
		theme.Comment = lipgloss.NewStyle().Foreground(lipgloss.Color(color))
	}
	if color, ok := colors["function"]; ok {
		theme.Function = lipgloss.NewStyle().Foreground(lipgloss.Color(color))
	}
	if color, ok := colors["type"]; ok {
		theme.Type = lipgloss.NewStyle().Foreground(lipgloss.Color(color))
	}
	if color, ok := colors["number"]; ok {
		theme.Number = lipgloss.NewStyle().Foreground(lipgloss.Color(color))
	}
	if color, ok := colors["operator"]; ok {
		theme.Operator = lipgloss.NewStyle().Foreground(lipgloss.Color(color))
	}
	if color, ok := colors["variable"]; ok {
		theme.Variable = lipgloss.NewStyle().Foreground(lipgloss.Color(color))
	}
	if color, ok := colors["constant"]; ok {
		theme.Constant = lipgloss.NewStyle().Foreground(lipgloss.Color(color))
	}
	if color, ok := colors["punctuation"]; ok {
		theme.Punctuation = lipgloss.NewStyle().Foreground(lipgloss.Color(color))
	}
	if color, ok := colors["error"]; ok {
		theme.Error = lipgloss.NewStyle().Foreground(lipgloss.Color(color))
	}
	if color, ok := colors["background"]; ok {
		theme.Background = lipgloss.NewStyle().Foreground(lipgloss.Color(color))
	}
	
	return theme
}
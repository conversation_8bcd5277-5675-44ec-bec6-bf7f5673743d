package syntax

import (
	"fmt"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/charmbracelet/lipgloss"
	"eionz.com/demo/pkg/domain"
)

// SyntaxHighlighter implements the domain.Transformer interface
type SyntaxHighlighter struct {
	theme SyntaxTheme
}

// SyntaxTheme defines colors for different syntax elements
type SyntaxTheme struct {
	Keyword     lipgloss.Style
	String      lipgloss.Style
	Comment     lipgloss.Style
	Function    lipgloss.Style
	Type        lipgloss.Style
	Number      lipgloss.Style
	Operator    lipgloss.Style
	Variable    lipgloss.Style
	Constant    lipgloss.Style
	Punctuation lipgloss.Style
	Error       lipgloss.Style
	Background  lipgloss.Style
}

// NewSyntaxHighlighter creates a new syntax highlighter with default theme
func NewSyntaxHighlighter() *SyntaxHighlighter {
	return &SyntaxHighlighter{
		theme: DefaultSyntaxTheme(),
	}
}

// NewSyntaxHighlighterWithTheme creates a syntax highlighter with custom theme
func NewSyntaxHighlighterWithTheme(theme SyntaxTheme) *SyntaxHighlighter {
	return &SyntaxHighlighter{
		theme: theme,
	}
}

// CanTransform checks if content can be syntax highlighted
func (sh *SyntaxHighlighter) CanTransform(content *domain.Content) bool {
	metadata := content.Metadata()
	
	// Check by MIME type
	if strings.HasPrefix(metadata.MimeType, "text/") {
		return true
	}
	
	// Check by file extension from source
	source := content.Source()
	if source.Type == domain.SourceTypeFile {
		ext := strings.ToLower(filepath.Ext(source.Location))
		return IsSupportedFileExtension(ext)
	}
	
	return false
}

// Transform applies syntax highlighting to content
func (sh *SyntaxHighlighter) Transform(content *domain.Content, options map[string]interface{}) ([]byte, domain.ContentMetadata, error) {
	if !content.IsLoaded() {
		return nil, domain.ContentMetadata{}, fmt.Errorf("content must be loaded before highlighting")
	}
	
	data := content.Data()
	if len(data) == 0 {
		return data, content.Metadata(), nil
	}
	
	text := string(data)
	
	// Determine language from file extension or explicit option
	language := "text"
	if lang, ok := options["language"].(string); ok && lang != "" {
		language = lang
	} else {
		// Infer from file extension
		source := content.Source()
		if source.Type == domain.SourceTypeFile {
			ext := strings.ToLower(filepath.Ext(source.Location))
			language = GetLanguageFromExtension(ext)
		}
	}
	
	// Apply syntax highlighting
	highlighted := sh.highlightText(text, language)
	
	// Create new metadata
	newMetadata := content.Metadata()
	newMetadata.MimeType = "text/x-highlighted"
	if newMetadata.Tags == nil {
		newMetadata.Tags = make([]string, 0)
	}
	newMetadata.Tags = append(newMetadata.Tags, "highlighted", "syntax-"+language)
	
	return []byte(highlighted), newMetadata, nil
}

// Name returns the transformer name
func (sh *SyntaxHighlighter) Name() string {
	return "syntax-highlighter"
}

// highlightText applies syntax highlighting based on language
func (sh *SyntaxHighlighter) highlightText(text, language string) string {
	switch language {
	case "go":
		return sh.highlightGo(text)
	case "javascript", "js":
		return sh.highlightJavaScript(text)
	case "python", "py":
		return sh.highlightPython(text)
	case "json":
		return sh.highlightJSON(text)
	case "yaml", "yml":
		return sh.highlightYAML(text)
	case "markdown", "md":
		return sh.highlightMarkdown(text)
	case "sql":
		return sh.highlightSQL(text)
	case "xml", "html":
		return sh.highlightXML(text)
	case "css":
		return sh.highlightCSS(text)
	case "shell", "bash", "sh":
		return sh.highlightShell(text)
	default:
		return sh.highlightPlainText(text)
	}
}

// highlightGo applies Go syntax highlighting
func (sh *SyntaxHighlighter) highlightGo(text string) string {
	// Go keywords
	keywords := []string{
		"break", "case", "chan", "const", "continue", "default", "defer", "else",
		"fallthrough", "for", "func", "go", "goto", "if", "import", "interface",
		"map", "package", "range", "return", "select", "struct", "switch", "type",
		"var",
	}
	
	// Go built-in types
	types := []string{
		"bool", "byte", "complex64", "complex128", "error", "float32", "float64",
		"int", "int8", "int16", "int32", "int64", "rune", "string",
		"uint", "uint8", "uint16", "uint32", "uint64", "uintptr",
	}
	
	// Go built-in functions
	functions := []string{
		"append", "cap", "close", "complex", "copy", "delete", "imag", "len",
		"make", "new", "panic", "print", "println", "real", "recover",
	}
	
	highlighted := text
	
	// Highlight strings
	highlighted = sh.highlightStrings(highlighted)
	
	// Highlight comments
	highlighted = sh.highlightComments(highlighted)
	
	// Highlight keywords
	for _, keyword := range keywords {
		highlighted = sh.highlightKeyword(highlighted, keyword)
	}
	
	// Highlight types
	for _, t := range types {
		highlighted = sh.highlightType(highlighted, t)
	}
	
	// Highlight functions
	for _, fn := range functions {
		highlighted = sh.highlightFunction(highlighted, fn)
	}
	
	// Highlight numbers
	highlighted = sh.highlightNumbers(highlighted)
	
	return highlighted
}

// highlightPython applies Python syntax highlighting
func (sh *SyntaxHighlighter) highlightPython(text string) string {
	keywords := []string{
		"and", "as", "assert", "break", "class", "continue", "def", "del", "elif",
		"else", "except", "exec", "finally", "for", "from", "global", "if",
		"import", "in", "is", "lambda", "not", "or", "pass", "print", "raise",
		"return", "try", "while", "with", "yield",
	}
	
	highlighted := text
	
	// Highlight strings (Python supports both single and double quotes)
	highlighted = sh.highlightPythonStrings(highlighted)
	
	// Highlight comments
	highlighted = sh.highlightPythonComments(highlighted)
	
	// Highlight keywords
	for _, keyword := range keywords {
		highlighted = sh.highlightKeyword(highlighted, keyword)
	}
	
	// Highlight numbers
	highlighted = sh.highlightNumbers(highlighted)
	
	return highlighted
}

// highlightJSON applies JSON syntax highlighting
func (sh *SyntaxHighlighter) highlightJSON(text string) string {
	highlighted := text
	
	// Highlight strings (JSON keys and values)
	highlighted = sh.highlightJSONStrings(highlighted)
	
	// Highlight numbers
	highlighted = sh.highlightNumbers(highlighted)
	
	// Highlight boolean values
	highlighted = sh.highlightJSONBooleans(highlighted)
	
	// Highlight null
	highlighted = sh.highlightJSONNull(highlighted)
	
	return highlighted
}

// highlightMarkdown applies Markdown syntax highlighting
func (sh *SyntaxHighlighter) highlightMarkdown(text string) string {
	highlighted := text
	
	// Highlight headers
	highlighted = sh.highlightMarkdownHeaders(highlighted)
	
	// Highlight code blocks
	highlighted = sh.highlightMarkdownCode(highlighted)
	
	// Highlight bold and italic
	highlighted = sh.highlightMarkdownEmphasis(highlighted)
	
	// Highlight links
	highlighted = sh.highlightMarkdownLinks(highlighted)
	
	return highlighted
}

// highlightPlainText applies minimal highlighting for plain text
func (sh *SyntaxHighlighter) highlightPlainText(text string) string {
	// For plain text, just ensure proper styling
	return sh.theme.Background.Render(text)
}

// Helper methods for highlighting specific elements

func (sh *SyntaxHighlighter) highlightStrings(text string) string {
	// Highlight double-quoted strings
	re := regexp.MustCompile(`"([^"\\]|\\.)*"`)
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.String.Render(match)
	})
}

func (sh *SyntaxHighlighter) highlightComments(text string) string {
	// Highlight line comments (//)
	re := regexp.MustCompile(`//.*$`)
	text = re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Comment.Render(match)
	})
	
	// Highlight block comments (/* */)
	re = regexp.MustCompile(`/\*[\s\S]*?\*/`)
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Comment.Render(match)
	})
}

func (sh *SyntaxHighlighter) highlightKeyword(text, keyword string) string {
	// Use word boundaries to avoid partial matches
	re := regexp.MustCompile(`\b` + regexp.QuoteMeta(keyword) + `\b`)
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Keyword.Render(match)
	})
}

func (sh *SyntaxHighlighter) highlightType(text, typeName string) string {
	re := regexp.MustCompile(`\b` + regexp.QuoteMeta(typeName) + `\b`)
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Type.Render(match)
	})
}

func (sh *SyntaxHighlighter) highlightFunction(text, funcName string) string {
	re := regexp.MustCompile(`\b` + regexp.QuoteMeta(funcName) + `\b`)
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Function.Render(match)
	})
}

func (sh *SyntaxHighlighter) highlightNumbers(text string) string {
	// Highlight integers and floats
	re := regexp.MustCompile(`\b\d+\.?\d*\b`)
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Number.Render(match)
	})
}

func (sh *SyntaxHighlighter) highlightPythonStrings(text string) string {
	// Python supports single quotes, double quotes, and triple quotes
	patterns := []string{
		`"""[\s\S]*?"""`,  // Triple double quotes
		`'''[\s\S]*?'''`,  // Triple single quotes
		`"([^"\\]|\\.)*"`, // Double quotes
		`'([^'\\]|\\.)*'`, // Single quotes
	}
	
	highlighted := text
	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		highlighted = re.ReplaceAllStringFunc(highlighted, func(match string) string {
			return sh.theme.String.Render(match)
		})
	}
	
	return highlighted
}

func (sh *SyntaxHighlighter) highlightPythonComments(text string) string {
	// Python only has line comments starting with #
	re := regexp.MustCompile(`#.*$`)
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Comment.Render(match)
	})
}

func (sh *SyntaxHighlighter) highlightJSONStrings(text string) string {
	// JSON strings are always double-quoted
	re := regexp.MustCompile(`"([^"\\]|\\.)*"`)
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.String.Render(match)
	})
}

func (sh *SyntaxHighlighter) highlightJSONBooleans(text string) string {
	re := regexp.MustCompile(`\b(true|false)\b`)
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Constant.Render(match)
	})
}

func (sh *SyntaxHighlighter) highlightJSONNull(text string) string {
	re := regexp.MustCompile(`\bnull\b`)
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Constant.Render(match)
	})
}

func (sh *SyntaxHighlighter) highlightMarkdownHeaders(text string) string {
	// Highlight headers (# ## ### etc.)
	re := regexp.MustCompile(`^#{1,6}\s+.*$`)
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Keyword.Render(match)
	})
}

func (sh *SyntaxHighlighter) highlightMarkdownCode(text string) string {
	// Highlight inline code (`code`)
	re := regexp.MustCompile("`[^`]+`")
	text = re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Function.Render(match)
	})
	
	// Highlight code blocks (```code```)
	re = regexp.MustCompile("```[\\s\\S]*?```")
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Function.Render(match)
	})
}

func (sh *SyntaxHighlighter) highlightMarkdownEmphasis(text string) string {
	// Highlight bold (**text**)
	re := regexp.MustCompile(`\*\*[^*]+\*\*`)
	text = re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Keyword.Render(match)
	})
	
	// Highlight italic (*text*)
	re = regexp.MustCompile(`\*[^*]+\*`)
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Type.Render(match)
	})
}

func (sh *SyntaxHighlighter) highlightMarkdownLinks(text string) string {
	// Highlight links [text](url)
	re := regexp.MustCompile(`\[[^\]]+\]\([^)]+\)`)
	return re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.String.Render(match)
	})
}

// Additional language support methods can be added here...

func (sh *SyntaxHighlighter) highlightJavaScript(text string) string {
	keywords := []string{
		"break", "case", "catch", "class", "const", "continue", "debugger", "default",
		"delete", "do", "else", "export", "extends", "finally", "for", "function",
		"if", "import", "in", "instanceof", "let", "new", "return", "super", "switch",
		"this", "throw", "try", "typeof", "var", "void", "while", "with", "yield",
	}
	
	highlighted := text
	highlighted = sh.highlightStrings(highlighted)
	highlighted = sh.highlightComments(highlighted)
	
	for _, keyword := range keywords {
		highlighted = sh.highlightKeyword(highlighted, keyword)
	}
	
	highlighted = sh.highlightNumbers(highlighted)
	return highlighted
}

func (sh *SyntaxHighlighter) highlightYAML(text string) string {
	// Highlight YAML keys
	re := regexp.MustCompile(`^(\s*)([a-zA-Z_][a-zA-Z0-9_]*):`)
	highlighted := re.ReplaceAllStringFunc(text, func(match string) string {
		parts := strings.Split(match, ":")
		if len(parts) >= 2 {
			key := strings.TrimSpace(parts[0])
			return strings.Replace(match, key, sh.theme.Keyword.Render(key), 1)
		}
		return match
	})
	
	// Highlight YAML comments
	re = regexp.MustCompile(`#.*$`)
	highlighted = re.ReplaceAllStringFunc(highlighted, func(match string) string {
		return sh.theme.Comment.Render(match)
	})
	
	// Highlight strings
	highlighted = sh.highlightStrings(highlighted)
	highlighted = sh.highlightNumbers(highlighted)
	
	return highlighted
}

func (sh *SyntaxHighlighter) highlightSQL(text string) string {
	keywords := []string{
		"SELECT", "FROM", "WHERE", "INSERT", "UPDATE", "DELETE", "CREATE", "DROP",
		"ALTER", "TABLE", "INDEX", "JOIN", "LEFT", "RIGHT", "INNER", "OUTER",
		"ON", "GROUP", "BY", "ORDER", "HAVING", "LIMIT", "OFFSET", "UNION",
		"AND", "OR", "NOT", "NULL", "IS", "IN", "LIKE", "BETWEEN",
	}
	
	highlighted := text
	highlighted = sh.highlightStrings(highlighted)
	
	// SQL comments
	re := regexp.MustCompile(`--.*$`)
	highlighted = re.ReplaceAllStringFunc(highlighted, func(match string) string {
		return sh.theme.Comment.Render(match)
	})
	
	for _, keyword := range keywords {
		highlighted = sh.highlightKeyword(highlighted, keyword)
	}
	
	highlighted = sh.highlightNumbers(highlighted)
	return highlighted
}

func (sh *SyntaxHighlighter) highlightXML(text string) string {
	// Highlight XML tags
	re := regexp.MustCompile(`<[^>]+>`)
	highlighted := re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Keyword.Render(match)
	})
	
	// Highlight strings in attributes
	re = regexp.MustCompile(`"[^"]*"`)
	highlighted = re.ReplaceAllStringFunc(highlighted, func(match string) string {
		return sh.theme.String.Render(match)
	})
	
	// Highlight XML comments
	re = regexp.MustCompile(`<!--[\s\S]*?-->`)
	highlighted = re.ReplaceAllStringFunc(highlighted, func(match string) string {
		return sh.theme.Comment.Render(match)
	})
	
	return highlighted
}

func (sh *SyntaxHighlighter) highlightCSS(text string) string {
	// Highlight CSS selectors
	re := regexp.MustCompile(`^[^{]+(?=\s*{)`)
	highlighted := re.ReplaceAllStringFunc(text, func(match string) string {
		return sh.theme.Keyword.Render(match)
	})
	
	// Highlight CSS properties
	re = regexp.MustCompile(`\s*([a-zA-Z-]+)\s*:`)
	highlighted = re.ReplaceAllStringFunc(highlighted, func(match string) string {
		return strings.Replace(match, strings.TrimSpace(strings.TrimSuffix(match, ":")), 
			sh.theme.Type.Render(strings.TrimSpace(strings.TrimSuffix(match, ":"))), 1) + ":"
	})
	
	// Highlight strings and comments
	highlighted = sh.highlightStrings(highlighted)
	
	// CSS comments
	re = regexp.MustCompile(`/\*[\s\S]*?\*/`)
	highlighted = re.ReplaceAllStringFunc(highlighted, func(match string) string {
		return sh.theme.Comment.Render(match)
	})
	
	highlighted = sh.highlightNumbers(highlighted)
	return highlighted
}

func (sh *SyntaxHighlighter) highlightShell(text string) string {
	keywords := []string{
		"if", "then", "else", "elif", "fi", "for", "while", "do", "done",
		"case", "esac", "function", "return", "exit", "break", "continue",
		"local", "readonly", "export", "unset", "source", "eval",
	}
	
	highlighted := text
	
	// Highlight shell comments
	re := regexp.MustCompile(`#.*$`)
	highlighted = re.ReplaceAllStringFunc(highlighted, func(match string) string {
		return sh.theme.Comment.Render(match)
	})
	
	// Highlight strings
	highlighted = sh.highlightStrings(highlighted)
	highlighted = sh.highlightPythonStrings(highlighted) // Shell supports single quotes too
	
	// Highlight variables
	re = regexp.MustCompile(`\$[a-zA-Z_][a-zA-Z0-9_]*`)
	highlighted = re.ReplaceAllStringFunc(highlighted, func(match string) string {
		return sh.theme.Variable.Render(match)
	})
	
	for _, keyword := range keywords {
		highlighted = sh.highlightKeyword(highlighted, keyword)
	}
	
	return highlighted
}
package history

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"
)

// HistoryEntry represents a single command history entry
type HistoryEntry struct {
	Command   string    `json:"command"`
	Timestamp time.Time `json:"timestamp"`
	Success   bool      `json:"success"`
	Error     string    `json:"error,omitempty"`
}

// CommandHistory manages command history with persistence and search
type CommandHistory struct {
	entries     []HistoryEntry
	filePath    string
	maxEntries  int
	currentPos  int
	searchMode  bool
	searchTerm  string
	searchResults []int // indices of matching entries
}

// NewCommandHistory creates a new command history manager
func NewCommandHistory(filePath string, maxEntries int) *CommandHistory {
	if maxEntries <= 0 {
		maxEntries = 1000
	}
	
	ch := &CommandHistory{
		entries:    make([]HistoryEntry, 0),
		filePath:   filePath,
		maxEntries: maxEntries,
		currentPos: -1,
		searchMode: false,
	}
	
	// Load existing history
	ch.Load()
	
	return ch
}

// Add adds a new command to the history
func (ch *CommandHistory) Add(command string, success bool, error string) {
	// Don't add empty commands
	if strings.TrimSpace(command) == "" {
		return
	}
	
	// Don't add duplicate consecutive commands
	if len(ch.entries) > 0 {
		lastEntry := ch.entries[len(ch.entries)-1]
		if lastEntry.Command == command {
			return
		}
	}
	
	entry := HistoryEntry{
		Command:   command,
		Timestamp: time.Now(),
		Success:   success,
		Error:     error,
	}
	
	ch.entries = append(ch.entries, entry)
	
	// Limit history size
	if len(ch.entries) > ch.maxEntries {
		ch.entries = ch.entries[len(ch.entries)-ch.maxEntries:]
	}
	
	// Reset position and search
	ch.currentPos = -1
	ch.exitSearchMode()
	
	// Auto-save
	ch.Save()
}

// GetPrevious returns the previous command in history
func (ch *CommandHistory) GetPrevious() (string, bool) {
	if ch.searchMode {
		return ch.getPreviousSearchResult()
	}
	
	if len(ch.entries) == 0 {
		return "", false
	}
	
	if ch.currentPos == -1 {
		ch.currentPos = len(ch.entries) - 1
	} else if ch.currentPos > 0 {
		ch.currentPos--
	}
	
	if ch.currentPos >= 0 && ch.currentPos < len(ch.entries) {
		return ch.entries[ch.currentPos].Command, true
	}
	
	return "", false
}

// GetNext returns the next command in history
func (ch *CommandHistory) GetNext() (string, bool) {
	if ch.searchMode {
		return ch.getNextSearchResult()
	}
	
	if len(ch.entries) == 0 || ch.currentPos == -1 {
		return "", false
	}
	
	ch.currentPos++
	if ch.currentPos >= len(ch.entries) {
		ch.currentPos = -1
		return "", false
	}
	
	return ch.entries[ch.currentPos].Command, true
}

// Reset resets the history position
func (ch *CommandHistory) Reset() {
	ch.currentPos = -1
	ch.exitSearchMode()
}

// Search searches for commands containing the given term
func (ch *CommandHistory) Search(term string) []HistoryEntry {
	var matches []HistoryEntry
	
	searchTerm := strings.ToLower(term)
	for _, entry := range ch.entries {
		if strings.Contains(strings.ToLower(entry.Command), searchTerm) {
			matches = append(matches, entry)
		}
	}
	
	// Sort by timestamp (most recent first)
	sort.Slice(matches, func(i, j int) bool {
		return matches[i].Timestamp.After(matches[j].Timestamp)
	})
	
	return matches
}

// StartSearch enters search mode and returns initial results
func (ch *CommandHistory) StartSearch(term string) []HistoryEntry {
	ch.searchMode = true
	ch.searchTerm = term
	
	// Find matching entry indices
	ch.searchResults = make([]int, 0)
	searchTerm := strings.ToLower(term)
	
	for i, entry := range ch.entries {
		if strings.Contains(strings.ToLower(entry.Command), searchTerm) {
			ch.searchResults = append(ch.searchResults, i)
		}
	}
	
	// Sort search results by timestamp (most recent first)
	sort.Slice(ch.searchResults, func(i, j int) bool {
		return ch.entries[ch.searchResults[i]].Timestamp.After(ch.entries[ch.searchResults[j]].Timestamp)
	})
	
	ch.currentPos = -1
	
	// Return the matches as entries
	var matches []HistoryEntry
	for _, idx := range ch.searchResults {
		matches = append(matches, ch.entries[idx])
	}
	
	return matches
}

// UpdateSearch updates the search term and results
func (ch *CommandHistory) UpdateSearch(term string) []HistoryEntry {
	if !ch.searchMode {
		return ch.StartSearch(term)
	}
	
	ch.searchTerm = term
	return ch.StartSearch(term) // Restart search with new term
}

// IsSearchMode returns whether we're in search mode
func (ch *CommandHistory) IsSearchMode() bool {
	return ch.searchMode
}

// GetSearchTerm returns the current search term
func (ch *CommandHistory) GetSearchTerm() string {
	return ch.searchTerm
}

// ExitSearchMode exits search mode
func (ch *CommandHistory) ExitSearchMode() {
	ch.exitSearchMode()
}

// exitSearchMode exits search mode (internal)
func (ch *CommandHistory) exitSearchMode() {
	ch.searchMode = false
	ch.searchTerm = ""
	ch.searchResults = nil
	ch.currentPos = -1
}

// getPreviousSearchResult gets the previous search result
func (ch *CommandHistory) getPreviousSearchResult() (string, bool) {
	if len(ch.searchResults) == 0 {
		return "", false
	}
	
	if ch.currentPos == -1 {
		ch.currentPos = 0
	} else if ch.currentPos < len(ch.searchResults)-1 {
		ch.currentPos++
	}
	
	if ch.currentPos >= 0 && ch.currentPos < len(ch.searchResults) {
		entryIdx := ch.searchResults[ch.currentPos]
		return ch.entries[entryIdx].Command, true
	}
	
	return "", false
}

// getNextSearchResult gets the next search result
func (ch *CommandHistory) getNextSearchResult() (string, bool) {
	if len(ch.searchResults) == 0 || ch.currentPos == -1 {
		return "", false
	}
	
	if ch.currentPos > 0 {
		ch.currentPos--
		entryIdx := ch.searchResults[ch.currentPos]
		return ch.entries[entryIdx].Command, true
	}
	
	return "", false
}

// GetAll returns all history entries
func (ch *CommandHistory) GetAll() []HistoryEntry {
	// Return a copy to prevent external modification
	entries := make([]HistoryEntry, len(ch.entries))
	copy(entries, ch.entries)
	return entries
}

// GetRecent returns the most recent N entries
func (ch *CommandHistory) GetRecent(n int) []HistoryEntry {
	if n <= 0 {
		return []HistoryEntry{}
	}
	
	start := len(ch.entries) - n
	if start < 0 {
		start = 0
	}
	
	entries := make([]HistoryEntry, len(ch.entries[start:]))
	copy(entries, ch.entries[start:])
	return entries
}

// GetStatistics returns usage statistics
func (ch *CommandHistory) GetStatistics() HistoryStatistics {
	stats := HistoryStatistics{
		TotalCommands: len(ch.entries),
		CommandCounts: make(map[string]int),
		SuccessRate:   0.0,
	}
	
	if len(ch.entries) == 0 {
		return stats
	}
	
	successCount := 0
	for _, entry := range ch.entries {
		// Count command frequency (just the first word)
		command := strings.Fields(entry.Command)
		if len(command) > 0 {
			stats.CommandCounts[command[0]]++
		}
		
		if entry.Success {
			successCount++
		}
	}
	
	stats.SuccessRate = float64(successCount) / float64(len(ch.entries)) * 100
	
	// Find most used command
	maxCount := 0
	for cmd, count := range stats.CommandCounts {
		if count > maxCount {
			maxCount = count
			stats.MostUsedCommand = cmd
		}
	}
	
	return stats
}

// Clear removes all history entries
func (ch *CommandHistory) Clear() {
	ch.entries = make([]HistoryEntry, 0)
	ch.currentPos = -1
	ch.exitSearchMode()
	ch.Save()
}

// Size returns the number of entries in history
func (ch *CommandHistory) Size() int {
	return len(ch.entries)
}

// Save persists the history to disk
func (ch *CommandHistory) Save() error {
	if ch.filePath == "" {
		return nil // No persistence if no file path
	}
	
	// Create directory if it doesn't exist
	dir := filepath.Dir(ch.filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create history directory: %w", err)
	}
	
	// Marshal to JSON
	data, err := json.MarshalIndent(ch.entries, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal history: %w", err)
	}
	
	// Write to file
	if err := os.WriteFile(ch.filePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write history file: %w", err)
	}
	
	return nil
}

// Load loads the history from disk
func (ch *CommandHistory) Load() error {
	if ch.filePath == "" {
		return nil // No persistence if no file path
	}
	
	// Check if file exists
	if _, err := os.Stat(ch.filePath); os.IsNotExist(err) {
		return nil // No error if file doesn't exist yet
	}
	
	// Read file
	data, err := os.ReadFile(ch.filePath)
	if err != nil {
		return fmt.Errorf("failed to read history file: %w", err)
	}
	
	// Unmarshal JSON
	var entries []HistoryEntry
	if err := json.Unmarshal(data, &entries); err != nil {
		return fmt.Errorf("failed to unmarshal history: %w", err)
	}
	
	ch.entries = entries
	
	// Limit to maxEntries
	if len(ch.entries) > ch.maxEntries {
		ch.entries = ch.entries[len(ch.entries)-ch.maxEntries:]
	}
	
	return nil
}

// HistoryStatistics contains usage statistics
type HistoryStatistics struct {
	TotalCommands    int            `json:"total_commands"`
	MostUsedCommand  string         `json:"most_used_command"`
	CommandCounts    map[string]int `json:"command_counts"`
	SuccessRate      float64        `json:"success_rate"`
}

// HistorySearcher provides search functionality for command history
type HistorySearcher struct {
	history *CommandHistory
}

// NewHistorySearcher creates a new history searcher
func NewHistorySearcher(history *CommandHistory) *HistorySearcher {
	return &HistorySearcher{
		history: history,
	}
}

// SearchCommands searches for commands with various criteria
func (hs *HistorySearcher) SearchCommands(query string) []HistoryEntry {
	return hs.history.Search(query)
}

// SearchByPattern searches using regex patterns
func (hs *HistorySearcher) SearchByPattern(pattern string) []HistoryEntry {
	// For now, implement as simple substring search
	// Could be enhanced with actual regex support
	return hs.history.Search(pattern)
}

// SearchSuccessful returns only successful commands
func (hs *HistorySearcher) SearchSuccessful() []HistoryEntry {
	var matches []HistoryEntry
	for _, entry := range hs.history.entries {
		if entry.Success {
			matches = append(matches, entry)
		}
	}
	return matches
}

// SearchFailed returns only failed commands
func (hs *HistorySearcher) SearchFailed() []HistoryEntry {
	var matches []HistoryEntry
	for _, entry := range hs.history.entries {
		if !entry.Success {
			matches = append(matches, entry)
		}
	}
	return matches
}

// SearchByTimeRange returns commands within a time range
func (hs *HistorySearcher) SearchByTimeRange(start, end time.Time) []HistoryEntry {
	var matches []HistoryEntry
	for _, entry := range hs.history.entries {
		if entry.Timestamp.After(start) && entry.Timestamp.Before(end) {
			matches = append(matches, entry)
		}
	}
	return matches
}

// GetHistoryPath returns the default history file path
func GetHistoryPath() string {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return ".termillm_history.json"
	}
	return filepath.Join(homeDir, ".termillm", "history.json")
}
package app

import (
	"fmt"
	"os"
	"strings"
	"time"

	"eionz.com/demo/pkg/plugin"
)

// handleListPlugins lists all loaded plugins
func (ac *AppCoordinator) handleListPlugins(cmd Command) (*CommandResult, error) {
	plugins := ac.pluginManager.ListPlugins()
	
	if len(plugins) == 0 {
		return &CommandResult{
			Success: true,
			Output:  "No plugins loaded",
		}, nil
	}
	
	var output strings.Builder
	output.WriteString("Loaded plugins:\n")
	
	for _, p := range plugins {
		metadata := p.Metadata()
		name := metadata.Name
		version := metadata.Version.String()
		description := metadata.Description
		
		output.WriteString(fmt.Sprintf("  • %s (v%s)", name, version))
		if description != "" {
			output.WriteString(fmt.Sprintf(" - %s", description))
		}
		output.WriteString("\n")
	}
	
	return &CommandResult{
		Success: true,
		Output:  output.String(),
		Data: map[string]interface{}{
			"count":   len(plugins),
			"plugins": plugins,
		},
	}, nil
}

// handleLoadPlugin loads a plugin from a file
func (ac *AppCoordinator) handleLoadPlugin(cmd Command) (*CommandResult, error) {
	if len(cmd.Args) < 2 {
		return &CommandResult{
			Success: false,
			Error:   "usage: plugin.load <name> <path>",
		}, nil
	}
	
	name := cmd.Args[0]
	path := cmd.Args[1]
	
	// Load plugin
	if _, err := ac.pluginLoader.LoadFromPath(path); err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("failed to load plugin: %v", err),
		}, nil
	}
	
	// Get the loaded plugin
	plugins := ac.pluginManager.ListPlugins()
	if len(plugins) == 0 {
		return &CommandResult{
			Success: false,
			Error:   "no plugin was loaded",
		}, nil
	}
	
	loadedPlugin := plugins[len(plugins)-1] // Get the last loaded plugin
	
	// Register with plugin manager
	if err := ac.pluginManager.RegisterPlugin(plugin.NewPluginID(name), loadedPlugin); err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("failed to register plugin: %v", err),
		}, nil
	}
	
	// Update configuration
	if ac.appConfig.Plugins == nil {
		ac.appConfig.Plugins = make(map[string]string)
	}
	ac.appConfig.Plugins[name] = path
	
	// Emit UI event
	ac.emitUIEvent(UIEvent{
		Type:   "plugin.loaded",
		Source: "coordinator",
		Data: map[string]interface{}{
			"name": name,
			"path": path,
		},
	})
	
	return &CommandResult{
		Success: true,
		Output:  fmt.Sprintf("Plugin '%s' loaded successfully from %s", name, path),
		Data: map[string]interface{}{
			"name":   name,
			"path":   path,
			"plugin": loadedPlugin,
		},
	}, nil
}

// handleUnloadPlugin unloads a plugin
func (ac *AppCoordinator) handleUnloadPlugin(cmd Command) (*CommandResult, error) {
	if len(cmd.Args) < 1 {
		return &CommandResult{
			Success: false,
			Error:   "usage: plugin.unload <name>",
		}, nil
	}
	
	name := cmd.Args[0]
	
	// Unload plugin
	if err := ac.pluginManager.UnregisterPlugin(plugin.NewPluginID(name)); err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("failed to unload plugin: %v", err),
		}, nil
	}
	
	// Remove from configuration
	if ac.appConfig.Plugins != nil {
		delete(ac.appConfig.Plugins, name)
	}
	
	// Emit UI event
	ac.emitUIEvent(UIEvent{
		Type:   "plugin.unloaded",
		Source: "coordinator",
		Data: map[string]interface{}{
			"name": name,
		},
	})
	
	return &CommandResult{
		Success: true,
		Output:  fmt.Sprintf("Plugin '%s' unloaded successfully", name),
		Data: map[string]interface{}{
			"name": name,
		},
	}, nil
}

// handleReloadPlugin reloads a plugin
func (ac *AppCoordinator) handleReloadPlugin(cmd Command) (*CommandResult, error) {
	if len(cmd.Args) < 1 {
		return &CommandResult{
			Success: false,
			Error:   "usage: plugin.reload <name>",
		}, nil
	}
	
	name := cmd.Args[0]
	
	// Get the plugin path
	path, exists := ac.appConfig.Plugins[name]
	if !exists {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("plugin '%s' not found in configuration", name),
		}, nil
	}
	
	// Unload first
	if err := ac.pluginManager.UnregisterPlugin(plugin.NewPluginID(name)); err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("failed to unload plugin for reload: %v", err),
		}, nil
	}
	
	// Reload
	if _, err := ac.pluginLoader.LoadFromPath(path); err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("failed to reload plugin: %v", err),
		}, nil
	}
	
	// Get the reloaded plugin
	plugins := ac.pluginManager.ListPlugins()
	if len(plugins) == 0 {
		return &CommandResult{
			Success: false,
			Error:   "no plugin was reloaded",
		}, nil
	}
	
	reloadedPlugin := plugins[len(plugins)-1]
	
	// Re-register
	if err := ac.pluginManager.RegisterPlugin(plugin.NewPluginID(name), reloadedPlugin); err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("failed to re-register plugin: %v", err),
		}, nil
	}
	
	// Emit UI event
	ac.emitUIEvent(UIEvent{
		Type:   "plugin.reloaded",
		Source: "coordinator",
		Data: map[string]interface{}{
			"name": name,
			"path": path,
		},
	})
	
	return &CommandResult{
		Success: true,
		Output:  fmt.Sprintf("Plugin '%s' reloaded successfully", name),
		Data: map[string]interface{}{
			"name":   name,
			"plugin": reloadedPlugin,
		},
	}, nil
}

// handlePluginInfo shows information about a plugin
func (ac *AppCoordinator) handlePluginInfo(cmd Command) (*CommandResult, error) {
	if len(cmd.Args) < 1 {
		return &CommandResult{
			Success: false,
			Error:   "usage: plugin.info <name>",
		}, nil
	}
	
	name := cmd.Args[0]
	
	plugin, err := ac.pluginManager.GetPlugin(plugin.NewPluginID(name))
	if err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("plugin not found: %v", err),
		}, nil
	}
	
	metadata := plugin.Metadata()
	
	var output strings.Builder
	output.WriteString(fmt.Sprintf("Plugin: %s\n", name))
	
	output.WriteString(fmt.Sprintf("Version: %s\n", metadata.Version.String()))
	output.WriteString(fmt.Sprintf("Description: %s\n", metadata.Description))
	output.WriteString(fmt.Sprintf("Author: %s\n", metadata.Author))
	
	if len(metadata.Keywords) > 0 {
		output.WriteString("Keywords:\n")
		for _, keyword := range metadata.Keywords {
			output.WriteString(fmt.Sprintf("  • %s\n", keyword))
		}
	}
	
	if path, exists := ac.appConfig.Plugins[name]; exists {
		output.WriteString(fmt.Sprintf("Path: %s\n", path))
	}
	
	return &CommandResult{
		Success: true,
		Output:  output.String(),
		Data: map[string]interface{}{
			"name":     name,
			"metadata": metadata,
			"plugin":   plugin,
		},
	}, nil
}

// handleGetConfig gets a configuration value
func (ac *AppCoordinator) handleGetConfig(cmd Command) (*CommandResult, error) {
	if len(cmd.Args) < 1 {
		return &CommandResult{
			Success: false,
			Error:   "usage: config.get <key>",
		}, nil
	}
	
	key := cmd.Args[0]
	
	value, err := ac.configService.GetConfig(key)
	if err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("failed to get config: %v", err),
		}, nil
	}
	
	return &CommandResult{
		Success: true,
		Output:  fmt.Sprintf("%s = %v", key, value),
		Data: map[string]interface{}{
			"key":   key,
			"value": value,
		},
	}, nil
}

// handleSetConfig sets a configuration value
func (ac *AppCoordinator) handleSetConfig(cmd Command) (*CommandResult, error) {
	if len(cmd.Args) < 2 {
		return &CommandResult{
			Success: false,
			Error:   "usage: config.set <key> <value>",
		}, nil
	}
	
	key := cmd.Args[0]
	value := cmd.Args[1]
	
	// Try to parse value as different types
	var parsedValue interface{} = value
	
	// Try boolean
	if value == "true" {
		parsedValue = true
	} else if value == "false" {
		parsedValue = false
	}
	
	if err := ac.configService.SetConfig(key, parsedValue); err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("failed to set config: %v", err),
		}, nil
	}
	
	// Emit UI event
	ac.emitUIEvent(UIEvent{
		Type:   "config.changed",
		Source: "coordinator",
		Data: map[string]interface{}{
			"key":   key,
			"value": parsedValue,
		},
	})
	
	return &CommandResult{
		Success: true,
		Output:  fmt.Sprintf("Set %s = %v", key, parsedValue),
		Data: map[string]interface{}{
			"key":   key,
			"value": parsedValue,
		},
	}, nil
}

// handleReloadConfig reloads configuration
func (ac *AppCoordinator) handleReloadConfig(cmd Command) (*CommandResult, error) {
	// Reload application configuration
	if err := ac.loadAppConfig(); err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("failed to reload config: %v", err),
		}, nil
	}
	
	// Emit UI event
	ac.emitUIEvent(UIEvent{
		Type:   "config.reloaded",
		Source: "coordinator",
		Data:   map[string]interface{}{},
	})
	
	return &CommandResult{
		Success: true,
		Output:  "Configuration reloaded successfully",
	}, nil
}

// handleSystemStatus shows system status
func (ac *AppCoordinator) handleSystemStatus(cmd Command) (*CommandResult, error) {
	plugins := ac.pluginManager.ListPlugins()
	
	var output strings.Builder
	output.WriteString("System Status:\n")
	output.WriteString(fmt.Sprintf("  Plugins loaded: %d\n", len(plugins)))
	output.WriteString(fmt.Sprintf("  Hot-reload enabled: %t\n", ac.appConfig.EnableHotReload))
	output.WriteString(fmt.Sprintf("  Sandbox mode: %t\n", ac.appConfig.Security.SandboxMode))
	output.WriteString(fmt.Sprintf("  File access: %t\n", ac.appConfig.Security.AllowFileAccess))
	output.WriteString(fmt.Sprintf("  Network access: %t\n", ac.appConfig.Security.AllowNetworkAccess))
	
	return &CommandResult{
		Success: true,
		Output:  output.String(),
		Data: map[string]interface{}{
			"plugin_count": len(plugins),
			"config":       ac.appConfig,
		},
	}, nil
}

// handleSystemHelp shows available commands
func (ac *AppCoordinator) handleSystemHelp(cmd Command) (*CommandResult, error) {
	commands := ac.GetAvailableCommands()
	
	var output strings.Builder
	output.WriteString("Available commands:\n")
	
	// Group commands by category
	categories := map[string][]string{
		"Plugin Management": {},
		"Configuration":     {},
		"File Operations":   {},
		"System":           {},
		"Metrics":          {},
		"Plugin Commands":  {},
	}
	
	for _, cmd := range commands {
		switch {
		case strings.HasPrefix(cmd, "plugin."):
			categories["Plugin Management"] = append(categories["Plugin Management"], cmd)
		case strings.HasPrefix(cmd, "config."):
			categories["Configuration"] = append(categories["Configuration"], cmd)
		case strings.HasPrefix(cmd, "system."):
			categories["System"] = append(categories["System"], cmd)
		case cmd == "ls" || cmd == "cat" || cmd == "find" || cmd == "pwd":
			categories["File Operations"] = append(categories["File Operations"], cmd)
		case strings.HasPrefix(cmd, "metrics."):
			categories["Metrics"] = append(categories["Metrics"], cmd)
		default:
			categories["Plugin Commands"] = append(categories["Plugin Commands"], cmd)
		}
	}
	
	for category, cmds := range categories {
		if len(cmds) > 0 {
			output.WriteString(fmt.Sprintf("\n%s:\n", category))
			for _, cmd := range cmds {
				output.WriteString(fmt.Sprintf("  • %s\n", cmd))
			}
		}
	}
	
	return &CommandResult{
		Success: true,
		Output:  output.String(),
		Data: map[string]interface{}{
			"commands":   commands,
			"categories": categories,
		},
	}, nil
}

// File Operation Handlers (delegate to file_ops plugin)

// handleListFiles lists files in a directory (ls command)
func (ac *AppCoordinator) handleListFiles(cmd Command) (*CommandResult, error) {
	directory := "."
	if len(cmd.Args) > 0 {
		directory = cmd.Args[0]
	}

	// Try to get the file_ops plugin
	_, err := ac.pluginManager.GetPlugin(plugin.NewPluginID("file_ops"))
	if err != nil {
		return &CommandResult{
			Success: false,
			Error:   "File operations plugin not available. Load it with: plugin.load ./examples/plugins/file_ops.go",
		}, nil
	}

	// Create plugin execution context
	securityCtx := plugin.NewSecurityContext("terminal-user", "terminal-session")
	securityCtx.AddPermission(plugin.PermissionReadFiles)
	
	pluginCtx := plugin.NewSecurePluginContext(
		"file_ops",
		"File Operations",
		"1.0.0",
		securityCtx,
	)

	// Execute the plugin command
	result, err := ac.pluginExecutor.ExecuteWithContext(pluginCtx, "ListFiles", plugin.ExecutionOptions{
		Timeout:    30 * time.Second,
		MaxMemory:  1024 * 1024,
		MaxCPUTime: 10 * time.Second,
		Input: map[string]interface{}{
			"directory": directory,
		},
	})

	if err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("Failed to execute file listing: %v", err),
		}, nil
	}

	return &CommandResult{
		Success: true,
		Output:  fmt.Sprintf("Files in %s:\n%v", directory, result.Result),
		Data:    map[string]interface{}{"directory": directory, "result": result.Result},
	}, nil
}

// handleReadFile reads and displays file content (cat command)
func (ac *AppCoordinator) handleReadFile(cmd Command) (*CommandResult, error) {
	if len(cmd.Args) < 1 {
		return &CommandResult{
			Success: false,
			Error:   "usage: cat <filename>",
		}, nil
	}

	filename := cmd.Args[0]

	// Try to get the file_ops plugin
	_, err := ac.pluginManager.GetPlugin(plugin.NewPluginID("file_ops"))
	if err != nil {
		return &CommandResult{
			Success: false,
			Error:   "File operations plugin not available. Load it with: plugin.load ./examples/plugins/file_ops.go",
		}, nil
	}

	// Create plugin execution context
	securityCtx := plugin.NewSecurityContext("terminal-user", "terminal-session")
	securityCtx.AddPermission(plugin.PermissionReadFiles)
	
	pluginCtx := plugin.NewSecurePluginContext(
		"file_ops",
		"File Operations", 
		"1.0.0",
		securityCtx,
	)

	// Execute the plugin command
	result, err := ac.pluginExecutor.ExecuteWithContext(pluginCtx, "ReadFile", plugin.ExecutionOptions{
		Timeout:    30 * time.Second,
		MaxMemory:  1024 * 1024,
		MaxCPUTime: 10 * time.Second,
		Input: map[string]interface{}{
			"filename": filename,
		},
	})

	if err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("Failed to read file: %v", err),
		}, nil
	}

	return &CommandResult{
		Success: true,
		Output:  fmt.Sprintf("%v", result.Result),
		Data:    map[string]interface{}{"filename": filename, "content": result.Result},
	}, nil
}

// handleFindFiles searches for files (find command)
func (ac *AppCoordinator) handleFindFiles(cmd Command) (*CommandResult, error) {
	if len(cmd.Args) < 1 {
		return &CommandResult{
			Success: false,
			Error:   "usage: find <pattern> [directory]",
		}, nil
	}

	pattern := cmd.Args[0]
	directory := "."
	if len(cmd.Args) > 1 {
		directory = cmd.Args[1]
	}

	// Try to get the file_ops plugin
	_, err := ac.pluginManager.GetPlugin(plugin.NewPluginID("file_ops"))
	if err != nil {
		return &CommandResult{
			Success: false,
			Error:   "File operations plugin not available. Load it with: plugin.load ./examples/plugins/file_ops.go",
		}, nil
	}

	// Create plugin execution context
	securityCtx := plugin.NewSecurityContext("terminal-user", "terminal-session")
	securityCtx.AddPermission(plugin.PermissionReadFiles)
	
	pluginCtx := plugin.NewSecurePluginContext(
		"file_ops",
		"File Operations",
		"1.0.0", 
		securityCtx,
	)

	// Execute the plugin command
	result, err := ac.pluginExecutor.ExecuteWithContext(pluginCtx, "SearchFiles", plugin.ExecutionOptions{
		Timeout:    30 * time.Second,
		MaxMemory:  1024 * 1024,
		MaxCPUTime: 10 * time.Second,
		Input: map[string]interface{}{
			"pattern":   pattern,
			"directory": directory,
		},
	})

	if err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("Failed to search files: %v", err),
		}, nil
	}

	return &CommandResult{
		Success: true,
		Output:  fmt.Sprintf("Files matching '%s' in %s:\n%v", pattern, directory, result.Result),
		Data:    map[string]interface{}{"pattern": pattern, "directory": directory, "matches": result.Result},
	}, nil
}

// handlePrintWorkingDir shows current working directory (pwd command)
func (ac *AppCoordinator) handlePrintWorkingDir(cmd Command) (*CommandResult, error) {
	wd, err := os.Getwd()
	if err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("Failed to get working directory: %v", err),
		}, nil
	}

	return &CommandResult{
		Success: true,
		Output:  wd,
		Data:    map[string]interface{}{"working_directory": wd},
	}, nil
}

// Metrics handlers

// handleMetricsSummary shows usage metrics summary
func (ac *AppCoordinator) handleMetricsSummary(cmd Command) (*CommandResult, error) {
	summary := ac.metricsCollector.GetSummary()
	
	return &CommandResult{
		Success: true,
		Output:  summary,
		Data: map[string]interface{}{
			"metrics": ac.metricsCollector.GetMetrics(),
		},
	}, nil
}

// handleMetricsExport exports metrics to a file
func (ac *AppCoordinator) handleMetricsExport(cmd Command) (*CommandResult, error) {
	if len(cmd.Args) < 1 {
		return &CommandResult{
			Success: false,
			Error:   "usage: metrics.export <filepath> [format]",
		}, nil
	}

	filepath := cmd.Args[0]
	format := "json" // default
	if len(cmd.Args) > 1 {
		format = cmd.Args[1]
	}

	if err := ac.metricsCollector.ExportMetrics(filepath, format); err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("failed to export metrics: %v", err),
		}, nil
	}

	return &CommandResult{
		Success: true,
		Output:  fmt.Sprintf("Metrics exported to %s in %s format", filepath, format),
		Data: map[string]interface{}{
			"filepath": filepath,
			"format":   format,
		},
	}, nil
}

// handleMetricsReset resets all collected metrics
func (ac *AppCoordinator) handleMetricsReset(cmd Command) (*CommandResult, error) {
	if err := ac.metricsCollector.ResetMetrics(); err != nil {
		return &CommandResult{
			Success: false,
			Error:   fmt.Sprintf("failed to reset metrics: %v", err),
		}, nil
	}

	return &CommandResult{
		Success: true,
		Output:  "All metrics have been reset",
	}, nil
}
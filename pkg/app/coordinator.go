// Package app provides application-level coordination and integration
package app

import (
	"context"
	"fmt"
	"sync"
	"time"

	"eionz.com/demo/pkg/config"
	"eionz.com/demo/pkg/cqrs"
	"eionz.com/demo/pkg/eventsourcing"
	"eionz.com/demo/pkg/plugin"
)

// AppCoordinator manages the integration between UI and backend systems
type AppCoordinator struct {
	// Core systems
	configService   *config.ConfigService
	pluginManager   plugin.PluginManager
	commandBus      *cqrs.InMemoryCommandBus
	queryBus        *cqrs.InMemoryQueryBus
	eventStore      *eventsourcing.InMemoryEventStore
	pluginLoader    plugin.PluginLoader
	pluginExecutor  plugin.SecurePluginExecutor
	metricsCollector *MetricsCollector

	// UI integration
	uiCallbacks     map[string]UICallback
	commandHandlers map[string]CommandHandler
	
	// State
	ctx    context.Context
	cancel context.CancelFunc
	mutex  sync.RWMutex
	
	// Configuration
	appConfig *AppConfig
}

// UICallback represents a callback function for UI updates
type UICallback func(event UIEvent) error

// CommandHandler handles user commands from the terminal
type CommandHandler func(cmd Command) (*CommandResult, error)

// UIEvent represents events that affect the UI
type UIEvent struct {
	Type    string                 `json:"type"`
	Source  string                 `json:"source"`
	Data    map[string]interface{} `json:"data"`
	Context map[string]interface{} `json:"context,omitempty"`
}

// Command represents a user command from the terminal
type Command struct {
	Name   string                 `json:"name"`
	Args   []string               `json:"args"`
	Flags  map[string]interface{} `json:"flags"`
	Source string                 `json:"source"`
}

// CommandResult represents the result of executing a command
type CommandResult struct {
	Success bool                   `json:"success"`
	Output  string                 `json:"output"`
	Data    map[string]interface{} `json:"data,omitempty"`
	Error   string                 `json:"error,omitempty"`
}

// AppConfig holds application-level configuration
type AppConfig struct {
	PluginDir       string            `json:"plugin_dir"`
	ConfigDir       string            `json:"config_dir"`
	EnableHotReload bool              `json:"enable_hot_reload"`
	Security        SecurityConfig    `json:"security"`
	UI              UIConfig          `json:"ui"`
	Plugins         map[string]string `json:"plugins"` // name -> path
}

// SecurityConfig holds security settings
type SecurityConfig struct {
	AllowFileAccess    bool     `json:"allow_file_access"`
	AllowNetworkAccess bool     `json:"allow_network_access"`
	TrustedPlugins     []string `json:"trusted_plugins"`
	SandboxMode        bool     `json:"sandbox_mode"`
}

// UIConfig holds UI-specific settings
type UIConfig struct {
	Theme           string `json:"theme"`
	ShowLineNumbers bool   `json:"show_line_numbers"`
	WordWrap        bool   `json:"word_wrap"`
	TabSize         int    `json:"tab_size"`
}

// NewAppCoordinator creates a new application coordinator
func NewAppCoordinator(configDir string) (*AppCoordinator, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	// Initialize configuration service
	configService, err := config.NewConfigService(configDir)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create config service: %w", err)
	}
	
	// Initialize plugin systems
	pluginManager := plugin.NewSimplePluginManager()
	sandboxManager := plugin.NewSimpleSandboxManager()
	pluginRegistry := plugin.NewSimplePluginRegistry()
	pluginLoader := plugin.NewDefaultPluginLoader(sandboxManager, pluginRegistry, "./plugins")
	
	// Create security manager and executor
	securityManager := plugin.NewSecurityManager()
	pluginExecutor := plugin.NewDefaultSecurePluginExecutor(pluginManager, securityManager, sandboxManager)
	
	// Initialize CQRS
	commandBus := cqrs.NewInMemoryCommandBus()
	queryBus := cqrs.NewInMemoryQueryBus()
	
	// Initialize event store
	eventStore := eventsourcing.NewInMemoryEventStore()
	
	// Initialize metrics collector
	metricsCollector, err := NewMetricsCollector("./metrics", true)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create metrics collector: %w", err)
	}
	
	coordinator := &AppCoordinator{
		configService:    configService,
		pluginManager:    pluginManager,
		commandBus:       commandBus,
		queryBus:         queryBus,
		eventStore:       eventStore,
		pluginLoader:     pluginLoader,
		pluginExecutor:   pluginExecutor,
		metricsCollector: metricsCollector,
		uiCallbacks:      make(map[string]UICallback),
		commandHandlers:  make(map[string]CommandHandler),
		ctx:              ctx,
		cancel:           cancel,
	}
	
	// Set up default command handlers
	coordinator.setupDefaultCommandHandlers()
	
	return coordinator, nil
}

// Start initializes and starts all application systems
func (ac *AppCoordinator) Start() error {
	ac.mutex.Lock()
	defer ac.mutex.Unlock()
	
	// Start configuration service
	if err := ac.configService.Start(); err != nil {
		return fmt.Errorf("failed to start config service: %w", err)
	}
	
	// Load application configuration
	if err := ac.loadAppConfig(); err != nil {
		return fmt.Errorf("failed to load app config: %w", err)
	}
	
	// Configure security based on config
	if err := ac.configureSecurity(); err != nil {
		return fmt.Errorf("failed to configure security: %w", err)
	}
	
	// Load plugins
	if err := ac.loadPlugins(); err != nil {
		return fmt.Errorf("failed to load plugins: %w", err)
	}
	
	// Set up configuration hot-reload listener
	if ac.appConfig.EnableHotReload {
		ac.configService.AddReloadListener(ac)
	}
	
	return nil
}

// Stop gracefully shuts down all application systems
func (ac *AppCoordinator) Stop() error {
	ac.mutex.Lock()
	defer ac.mutex.Unlock()
	
	ac.cancel()
	
	// Unload all plugins
	if err := ac.pluginManager.UnloadAll(); err != nil {
		return fmt.Errorf("failed to unload plugins: %w", err)
	}
	
	// Stop configuration service
	if err := ac.configService.Stop(); err != nil {
		return fmt.Errorf("failed to stop config service: %w", err)
	}
	
	return nil
}

// RegisterUICallback registers a callback for UI events
func (ac *AppCoordinator) RegisterUICallback(eventType string, callback UICallback) {
	ac.mutex.Lock()
	defer ac.mutex.Unlock()
	
	ac.uiCallbacks[eventType] = callback
}

// RegisterCommandHandler registers a handler for terminal commands
func (ac *AppCoordinator) RegisterCommandHandler(command string, handler CommandHandler) {
	ac.mutex.Lock()
	defer ac.mutex.Unlock()
	
	ac.commandHandlers[command] = handler
}

// ExecuteCommand executes a user command from the terminal
func (ac *AppCoordinator) ExecuteCommand(cmd Command) (*CommandResult, error) {
	start := time.Now()
	
	ac.mutex.RLock()
	handler, exists := ac.commandHandlers[cmd.Name]
	ac.mutex.RUnlock()

	var result *CommandResult
	var err error
	
	if !exists {
		// Try to execute as plugin command
		result, err = ac.executePluginCommand(cmd)
	} else {
		result, err = handler(cmd)
	}
	
	// Record metrics
	duration := time.Since(start)
	success := err == nil && (result == nil || result.Success)
	errorMsg := ""
	if err != nil {
		errorMsg = err.Error()
	} else if result != nil && !result.Success {
		errorMsg = result.Error
	}
	
	ac.metricsCollector.RecordCommand(cmd.Name, cmd.Args, success, duration, errorMsg)
	
	return result, err
}

// GetAvailableCommands returns a list of available commands
func (ac *AppCoordinator) GetAvailableCommands() []string {
	ac.mutex.RLock()
	defer ac.mutex.RUnlock()
	
	commands := make([]string, 0, len(ac.commandHandlers))
	for cmd := range ac.commandHandlers {
		commands = append(commands, cmd)
	}
	
	// Add plugin commands
	plugins := ac.pluginManager.ListPlugins()
	for _, p := range plugins {
		if metadata := p.Metadata(); metadata.Name != "" {
			// Use Keywords as commands for now
			for _, cmd := range metadata.Keywords {
				commands = append(commands, cmd)
			}
		}
	}
	
	return commands
}

// GetPluginManager returns the plugin manager
func (ac *AppCoordinator) GetPluginManager() plugin.PluginManager {
	return ac.pluginManager
}

// GetConfigService returns the configuration service
func (ac *AppCoordinator) GetConfigService() *config.ConfigService {
	return ac.configService
}

// GetAppConfig returns the current application configuration
func (ac *AppCoordinator) GetAppConfig() *AppConfig {
	ac.mutex.RLock()
	defer ac.mutex.RUnlock()
	
	if ac.appConfig == nil {
		return &AppConfig{} // Return empty config as fallback
	}
	
	// Return a copy to prevent modification
	configCopy := *ac.appConfig
	return &configCopy
}

// setupDefaultCommandHandlers sets up built-in command handlers
func (ac *AppCoordinator) setupDefaultCommandHandlers() {
	// Plugin management commands
	ac.commandHandlers["plugin.list"] = ac.handleListPlugins
	ac.commandHandlers["plugin.load"] = ac.handleLoadPlugin
	ac.commandHandlers["plugin.unload"] = ac.handleUnloadPlugin
	ac.commandHandlers["plugin.reload"] = ac.handleReloadPlugin
	ac.commandHandlers["plugin.info"] = ac.handlePluginInfo
	
	// Configuration commands
	ac.commandHandlers["config.get"] = ac.handleGetConfig
	ac.commandHandlers["config.set"] = ac.handleSetConfig
	ac.commandHandlers["config.reload"] = ac.handleReloadConfig
	
	// File operation commands (will delegate to file_ops plugin)
	ac.commandHandlers["ls"] = ac.handleListFiles
	ac.commandHandlers["cat"] = ac.handleReadFile
	ac.commandHandlers["find"] = ac.handleFindFiles
	ac.commandHandlers["pwd"] = ac.handlePrintWorkingDir
	
	// System commands
	ac.commandHandlers["system.status"] = ac.handleSystemStatus
	ac.commandHandlers["system.help"] = ac.handleSystemHelp
	
	// Metrics commands
	ac.commandHandlers["metrics.summary"] = ac.handleMetricsSummary
	ac.commandHandlers["metrics.export"] = ac.handleMetricsExport
	ac.commandHandlers["metrics.reset"] = ac.handleMetricsReset
}

// loadAppConfig loads the application configuration
func (ac *AppCoordinator) loadAppConfig() error {
	// Set active environment
	env := config.Development // Default to development
	if err := ac.configService.SetActiveEnvironment(env); err != nil {
		return err
	}
	
	// Load application config with defaults
	ac.appConfig = &AppConfig{
		PluginDir:       "./plugins",
		ConfigDir:       "./config",
		EnableHotReload: true,
		Security: SecurityConfig{
			AllowFileAccess:    true,
			AllowNetworkAccess: false,
			TrustedPlugins:     []string{},
			SandboxMode:        true,
		},
		UI: UIConfig{
			Theme:           "default",
			ShowLineNumbers: true,
			WordWrap:        true,
			TabSize:         4,
		},
		Plugins: map[string]string{
			// Temporarily disable plugins to test metrics
		},
	}
	
	// Override with configuration values if they exist
	if pluginDir, err := ac.configService.GetConfig("app.plugin_dir"); err == nil {
		if dir, ok := pluginDir.(string); ok {
			ac.appConfig.PluginDir = dir
		}
	}
	
	if hotReload, err := ac.configService.GetConfig("app.enable_hot_reload"); err == nil {
		if enabled, ok := hotReload.(bool); ok {
			ac.appConfig.EnableHotReload = enabled
		}
	}
	
	return nil
}

// configureSecurity configures the security manager based on configuration
func (ac *AppCoordinator) configureSecurity() error {
	// For now, use basic string-based permissions rather than Permission objects
	// This is a simplified approach for the single-user terminal application
	
	if ac.appConfig.Security.AllowFileAccess {
		// File access is controlled through configuration, no need to set permissions here
		// In a real implementation, this would configure the security manager
	}
	
	if ac.appConfig.Security.AllowNetworkAccess {
		// Network access is controlled through configuration
		// In a real implementation, this would configure the security manager
	}
	
	return nil
}

// loadPlugins loads configured plugins
func (ac *AppCoordinator) loadPlugins() error {
	for name, path := range ac.appConfig.Plugins {
		loadedPlugin, err := ac.pluginLoader.LoadFromPath(path)
		if err != nil {
			fmt.Printf("Warning: failed to load plugin %s from %s: %v\n", name, path, err)
			continue
		}
		
		// Register with plugin manager
		pluginID := plugin.NewPluginID(name)
		if err := ac.pluginManager.RegisterPlugin(pluginID, loadedPlugin); err != nil {
			fmt.Printf("Warning: failed to register plugin %s: %v\n", name, err)
		} else {
			// Record successful plugin load
			ac.metricsCollector.RecordPluginLoad(name)
		}
	}
	
	return nil
}

// executePluginCommand executes a command through a plugin
func (ac *AppCoordinator) executePluginCommand(cmd Command) (*CommandResult, error) {
	// Find plugin that can handle this command
	plugins := ac.pluginManager.ListPlugins()
	
	for _, p := range plugins {
		metadata := p.Metadata()
		if metadata.Name == "" {
			continue
		}
		
		// For now, use a simple approach - check if plugin name matches command
		if p.Metadata().Name == cmd.Name {
			start := time.Now()
			pluginName := p.Metadata().Name
			
			// Create a basic security context for plugin execution
			securityCtx := plugin.NewSecurityContext("default-user", "default-session")
			
			// Create plugin context with proper arguments
			pluginCtx := plugin.NewSecurePluginContext(
				p.Metadata().ID.String(),
				p.Metadata().Name,
				p.Metadata().Version.String(),
				securityCtx,
			)
			
			// Execute plugin with proper arguments
			result, err := ac.pluginExecutor.ExecuteWithContext(pluginCtx, "execute", plugin.ExecutionOptions{
				Timeout:    time.Minute,
				MaxMemory:  1024 * 1024, // 1MB
				MaxCPUTime: time.Minute,
				Input:      map[string]interface{}{
					"command": cmd.Name,
					"args":    cmd.Args,
					"flags":   cmd.Flags,
				},
			})
			
			// Record plugin execution metrics
			duration := time.Since(start)
			success := err == nil
			ac.metricsCollector.RecordPluginExecution(pluginName, success, duration)
			
			if err != nil {
				return &CommandResult{
					Success: false,
					Error:   err.Error(),
				}, nil
			}
			
			return &CommandResult{
				Success: true,
				Output:  fmt.Sprintf("%v", result.Result),
				Data:    map[string]interface{}{"result": result.Result},
			}, nil
		}
	}
	
	return &CommandResult{
		Success: false,
		Error:   fmt.Sprintf("unknown command: %s", cmd.Name),
	}, nil
}

// emitUIEvent emits an event to registered UI callbacks
func (ac *AppCoordinator) emitUIEvent(event UIEvent) {
	ac.mutex.RLock()
	callback, exists := ac.uiCallbacks[event.Type]
	ac.mutex.RUnlock()
	
	if exists {
		go func() {
			if err := callback(event); err != nil {
				fmt.Printf("UI callback error for event %s: %v\n", event.Type, err)
			}
		}()
	}
}

// Hot-reload listener implementation
func (ac *AppCoordinator) OnConfigReload(env config.Environment, oldConfig, newConfig map[string]interface{}) error {
	// Reload application configuration
	if err := ac.loadAppConfig(); err != nil {
		return err
	}
	
	// Emit UI event for configuration change
	ac.emitUIEvent(UIEvent{
		Type:   "config.reloaded",
		Source: "coordinator",
		Data: map[string]interface{}{
			"environment": env,
			"config":      newConfig,
		},
	})
	
	return nil
}

func (ac *AppCoordinator) OnReloadError(env config.Environment, err error) {
	ac.emitUIEvent(UIEvent{
		Type:   "config.error",
		Source: "coordinator",
		Data: map[string]interface{}{
			"environment": env,
			"error":       err.Error(),
		},
	})
}

func (ac *AppCoordinator) ListenerName() string {
	return "app_coordinator"
}
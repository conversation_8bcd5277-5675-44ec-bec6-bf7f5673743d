package app

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// MetricsCollector collects and tracks usage metrics
type MetricsCollector struct {
	mutex       sync.RWMutex
	data        *MetricsData
	metricsFile string
	enabled     bool
}

// MetricsData holds all collected metrics
type MetricsData struct {
	SessionStart     time.Time                 `json:"session_start"`
	SessionDuration  time.Duration             `json:"session_duration"`
	CommandCount     int64                     `json:"command_count"`
	CommandHistory   []CommandMetric           `json:"command_history"`
	CommandFrequency map[string]int64          `json:"command_frequency"`
	PluginUsage      map[string]*PluginMetrics `json:"plugin_usage"`
	ErrorCount       int64                     `json:"error_count"`
	LastUpdate       time.Time                 `json:"last_update"`
}

// CommandMetric tracks individual command execution
type CommandMetric struct {
	Command   string        `json:"command"`
	Args      []string      `json:"args,omitempty"`
	Success   bool          `json:"success"`
	Duration  time.Duration `json:"duration"`
	Timestamp time.Time     `json:"timestamp"`
	Error     string        `json:"error,omitempty"`
}

// PluginMetrics tracks plugin-specific usage
type PluginMetrics struct {
	LoadCount    int64         `json:"load_count"`
	ExecuteCount int64         `json:"execute_count"`
	ErrorCount   int64         `json:"error_count"`
	TotalTime    time.Duration `json:"total_time"`
	LastUsed     time.Time     `json:"last_used"`
	FirstUsed    time.Time     `json:"first_used"`
}

// NewMetricsCollector creates a new metrics collector
func NewMetricsCollector(metricsDir string, enabled bool) (*MetricsCollector, error) {
	if !enabled {
		return &MetricsCollector{
			enabled: false,
			data:    &MetricsData{},
		}, nil
	}

	// Ensure metrics directory exists
	if err := os.MkdirAll(metricsDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create metrics directory: %w", err)
	}

	metricsFile := filepath.Join(metricsDir, "usage_metrics.json")
	
	collector := &MetricsCollector{
		enabled:     true,
		metricsFile: metricsFile,
		data: &MetricsData{
			SessionStart:     time.Now(),
			CommandHistory:   make([]CommandMetric, 0),
			CommandFrequency: make(map[string]int64),
			PluginUsage:      make(map[string]*PluginMetrics),
		},
	}

	// Load existing metrics if file exists
	if err := collector.loadMetrics(); err != nil {
		// If load fails, start with fresh metrics
		collector.data.SessionStart = time.Now()
	}

	return collector, nil
}

// RecordCommand records a command execution
func (mc *MetricsCollector) RecordCommand(command string, args []string, success bool, duration time.Duration, errorMsg string) {
	if !mc.enabled {
		return
	}

	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	// Update counters
	mc.data.CommandCount++
	if !success {
		mc.data.ErrorCount++
	}
	
	// Update frequency map
	mc.data.CommandFrequency[command]++

	// Add to history (keep last 100 commands)
	metric := CommandMetric{
		Command:   command,
		Args:      args,
		Success:   success,
		Duration:  duration,
		Timestamp: time.Now(),
		Error:     errorMsg,
	}

	mc.data.CommandHistory = append(mc.data.CommandHistory, metric)
	if len(mc.data.CommandHistory) > 100 {
		mc.data.CommandHistory = mc.data.CommandHistory[1:]
	}

	mc.data.LastUpdate = time.Now()
	mc.data.SessionDuration = time.Since(mc.data.SessionStart)

	// Save metrics asynchronously
	go mc.saveMetrics()
}

// RecordPluginLoad records when a plugin is loaded
func (mc *MetricsCollector) RecordPluginLoad(pluginName string) {
	if !mc.enabled {
		return
	}

	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	if mc.data.PluginUsage[pluginName] == nil {
		mc.data.PluginUsage[pluginName] = &PluginMetrics{
			FirstUsed: time.Now(),
		}
	}

	metrics := mc.data.PluginUsage[pluginName]
	metrics.LoadCount++
	metrics.LastUsed = time.Now()

	mc.data.LastUpdate = time.Now()
	go mc.saveMetrics()
}

// RecordPluginExecution records plugin command execution
func (mc *MetricsCollector) RecordPluginExecution(pluginName string, success bool, duration time.Duration) {
	if !mc.enabled {
		return
	}

	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	if mc.data.PluginUsage[pluginName] == nil {
		mc.data.PluginUsage[pluginName] = &PluginMetrics{
			FirstUsed: time.Now(),
		}
	}

	metrics := mc.data.PluginUsage[pluginName]
	metrics.ExecuteCount++
	metrics.TotalTime += duration
	if !success {
		metrics.ErrorCount++
	}
	metrics.LastUsed = time.Now()

	mc.data.LastUpdate = time.Now()
	go mc.saveMetrics()
}

// GetMetrics returns a copy of current metrics
func (mc *MetricsCollector) GetMetrics() MetricsData {
	if !mc.enabled {
		return MetricsData{}
	}

	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	// Update session duration
	mc.data.SessionDuration = time.Since(mc.data.SessionStart)

	// Return a copy to prevent external modification
	data := *mc.data
	data.CommandHistory = make([]CommandMetric, len(mc.data.CommandHistory))
	copy(data.CommandHistory, mc.data.CommandHistory)
	
	data.CommandFrequency = make(map[string]int64)
	for k, v := range mc.data.CommandFrequency {
		data.CommandFrequency[k] = v
	}

	data.PluginUsage = make(map[string]*PluginMetrics)
	for k, v := range mc.data.PluginUsage {
		pluginMetricsCopy := *v
		data.PluginUsage[k] = &pluginMetricsCopy
	}

	return data
}

// GetSummary returns a formatted summary of metrics
func (mc *MetricsCollector) GetSummary() string {
	if !mc.enabled {
		return "Metrics collection is disabled"
	}

	metrics := mc.GetMetrics()
	
	summary := fmt.Sprintf("Usage Metrics Summary\n")
	summary += fmt.Sprintf("====================\n")
	summary += fmt.Sprintf("Session Start: %s\n", metrics.SessionStart.Format("2006-01-02 15:04:05"))
	summary += fmt.Sprintf("Session Duration: %v\n", metrics.SessionDuration.Round(time.Second))
	summary += fmt.Sprintf("Total Commands: %d\n", metrics.CommandCount)
	summary += fmt.Sprintf("Errors: %d\n", metrics.ErrorCount)
	summary += fmt.Sprintf("Success Rate: %.1f%%\n", float64(metrics.CommandCount-metrics.ErrorCount)/float64(metrics.CommandCount)*100)
	
	summary += fmt.Sprintf("\nTop Commands:\n")
	count := 0
	for cmd, freq := range metrics.CommandFrequency {
		if count >= 5 {
			break
		}
		summary += fmt.Sprintf("  %s: %d times\n", cmd, freq)
		count++
	}

	summary += fmt.Sprintf("\nPlugins Used: %d\n", len(metrics.PluginUsage))
	for name, usage := range metrics.PluginUsage {
		summary += fmt.Sprintf("  %s: loaded %d times, executed %d times\n", 
			name, usage.LoadCount, usage.ExecuteCount)
	}

	return summary
}

// ExportMetrics exports metrics to a file in a specific format
func (mc *MetricsCollector) ExportMetrics(filePath string, format string) error {
	if !mc.enabled {
		return fmt.Errorf("metrics collection is disabled")
	}

	metrics := mc.GetMetrics()

	switch format {
	case "json":
		return mc.exportJSON(filePath, metrics)
	case "csv":
		return mc.exportCSV(filePath, metrics)
	default:
		return fmt.Errorf("unsupported format: %s", format)
	}
}

// ResetMetrics resets all collected metrics
func (mc *MetricsCollector) ResetMetrics() error {
	if !mc.enabled {
		return nil
	}

	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	mc.data = &MetricsData{
		SessionStart:     time.Now(),
		CommandHistory:   make([]CommandMetric, 0),
		CommandFrequency: make(map[string]int64),
		PluginUsage:      make(map[string]*PluginMetrics),
	}

	return mc.saveMetrics()
}

// Private methods

func (mc *MetricsCollector) loadMetrics() error {
	data, err := os.ReadFile(mc.metricsFile)
	if err != nil {
		if os.IsNotExist(err) {
			return nil // No existing metrics file
		}
		return err
	}

	return json.Unmarshal(data, mc.data)
}

func (mc *MetricsCollector) saveMetrics() error {
	mc.mutex.RLock()
	data := *mc.data
	mc.mutex.RUnlock()

	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(mc.metricsFile, jsonData, 0644)
}

func (mc *MetricsCollector) exportJSON(filePath string, metrics MetricsData) error {
	jsonData, err := json.MarshalIndent(metrics, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(filePath, jsonData, 0644)
}

func (mc *MetricsCollector) exportCSV(filePath string, metrics MetricsData) error {
	// Simple CSV export of command history
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// Write CSV header
	fmt.Fprintf(file, "Timestamp,Command,Success,Duration(ms),Error\n")

	// Write command history
	for _, cmd := range metrics.CommandHistory {
		fmt.Fprintf(file, "%s,%s,%t,%d,%s\n",
			cmd.Timestamp.Format("2006-01-02 15:04:05"),
			cmd.Command,
			cmd.Success,
			cmd.Duration.Milliseconds(),
			cmd.Error)
	}

	return nil
}
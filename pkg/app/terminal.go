// Package app provides terminal UI integration
package app

import (
	"fmt"
	"strings"

	tea "github.com/charmbracelet/bubbletea"
)

// TerminalIntegration provides integration between the terminal UI and application coordinator
type TerminalIntegration struct {
	coordinator *AppCoordinator
	
	// Command state
	commandMode     bool
	commandInput    string
	commandHistory  []string
	historyIndex    int
	
	// Output buffer
	outputBuffer    []string
	maxOutputLines  int
	
	// UI state
	showHelp        bool
	showStatus      bool
}

// NewTerminalIntegration creates a new terminal integration
func NewTerminalIntegration(coordinator *AppCoordinator) *TerminalIntegration {
	ti := &TerminalIntegration{
		coordinator:    coordinator,
		commandMode:    false,
		commandInput:   "",
		commandHistory: make([]string, 0),
		historyIndex:   -1,
		outputBuffer:   make([]string, 0),
		maxOutputLines: 100,
		showHelp:       false,
		showStatus:     false,
	}
	
	// Register UI callbacks
	coordinator.RegisterUICallback("plugin.loaded", ti.handlePluginLoaded)
	coordinator.RegisterUICallback("plugin.unloaded", ti.handlePluginUnloaded)
	coordinator.RegisterUICallback("config.changed", ti.handleConfigChanged)
	coordinator.RegisterUICallback("config.reloaded", ti.handleConfigReloaded)
	
	return ti
}

// TerminalCommand represents a terminal command message
type TerminalCommand struct {
	Command string
}

// TerminalOutput represents output to be displayed
type TerminalOutput struct {
	Text   string
	Source string
}

// Update handles terminal integration updates
func (ti *TerminalIntegration) Update(msg tea.Msg) tea.Cmd {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		return ti.handleKeyMsg(msg)
	case TerminalCommand:
		return ti.executeCommand(msg.Command)
	}
	
	return nil
}

// handleKeyMsg handles keyboard input for command mode
func (ti *TerminalIntegration) handleKeyMsg(msg tea.KeyMsg) tea.Cmd {
	switch msg.String() {
	case ":":
		// Enter command mode
		if !ti.commandMode {
			ti.commandMode = true
			ti.commandInput = ""
			return nil
		}
		fallthrough
	case "escape":
		// Exit command mode
		if ti.commandMode {
			ti.commandMode = false
			ti.commandInput = ""
			ti.historyIndex = -1
			return nil
		}
		return nil
		
	case "enter":
		if ti.commandMode && ti.commandInput != "" {
			cmd := ti.commandInput
			ti.commandHistory = append(ti.commandHistory, cmd)
			ti.commandMode = false
			ti.commandInput = ""
			ti.historyIndex = -1
			
			return func() tea.Msg {
				return TerminalCommand{Command: cmd}
			}
		}
		return nil
		
	case "backspace":
		if ti.commandMode && len(ti.commandInput) > 0 {
			ti.commandInput = ti.commandInput[:len(ti.commandInput)-1]
		}
		return nil
		
	case "up":
		if ti.commandMode && len(ti.commandHistory) > 0 {
			if ti.historyIndex == -1 {
				ti.historyIndex = len(ti.commandHistory) - 1
			} else if ti.historyIndex > 0 {
				ti.historyIndex--
			}
			if ti.historyIndex >= 0 && ti.historyIndex < len(ti.commandHistory) {
				ti.commandInput = ti.commandHistory[ti.historyIndex]
			}
		}
		return nil
		
	case "down":
		if ti.commandMode && ti.historyIndex != -1 {
			ti.historyIndex++
			if ti.historyIndex >= len(ti.commandHistory) {
				ti.historyIndex = -1
				ti.commandInput = ""
			} else {
				ti.commandInput = ti.commandHistory[ti.historyIndex]
			}
		}
		return nil
		
	case "f1":
		ti.showHelp = !ti.showHelp
		if ti.showHelp {
			return ti.executeCommand("system.help")
		}
		return nil
		
	case "f2":
		ti.showStatus = !ti.showStatus
		if ti.showStatus {
			return ti.executeCommand("system.status")
		}
		return nil
		
	default:
		// Add character to command input
		if ti.commandMode && len(msg.String()) == 1 {
			ti.commandInput += msg.String()
		}
		return nil
	}
}

// executeCommand executes a command through the coordinator
func (ti *TerminalIntegration) executeCommand(cmdStr string) tea.Cmd {
	return func() tea.Msg {
		// Parse command
		parts := strings.Fields(cmdStr)
		if len(parts) == 0 {
			return TerminalOutput{
				Text:   "Empty command",
				Source: "terminal",
			}
		}
		
		cmd := Command{
			Name:   parts[0],
			Args:   parts[1:],
			Flags:  make(map[string]interface{}),
			Source: "terminal",
		}
		
		// Execute command
		result, err := ti.coordinator.ExecuteCommand(cmd)
		if err != nil {
			return TerminalOutput{
				Text:   fmt.Sprintf("Command error: %v", err),
				Source: "terminal",
			}
		}
		
		if !result.Success {
			return TerminalOutput{
				Text:   fmt.Sprintf("Command failed: %s", result.Error),
				Source: "terminal",
			}
		}
		
		return TerminalOutput{
			Text:   result.Output,
			Source: "terminal",
		}
	}
}

// AddOutput adds text to the output buffer
func (ti *TerminalIntegration) AddOutput(text, source string) {
	// Split text into lines
	lines := strings.Split(text, "\n")
	
	for _, line := range lines {
		if line != "" {
			ti.outputBuffer = append(ti.outputBuffer, fmt.Sprintf("[%s] %s", source, line))
		}
	}
	
	// Limit buffer size
	if len(ti.outputBuffer) > ti.maxOutputLines {
		ti.outputBuffer = ti.outputBuffer[len(ti.outputBuffer)-ti.maxOutputLines:]
	}
}

// GetOutput returns the current output buffer
func (ti *TerminalIntegration) GetOutput() []string {
	return ti.outputBuffer
}

// GetCommandLine returns the current command line for display
func (ti *TerminalIntegration) GetCommandLine() string {
	if ti.commandMode {
		return ":" + ti.commandInput
	}
	return ""
}

// IsCommandMode returns whether we're in command mode
func (ti *TerminalIntegration) IsCommandMode() bool {
	return ti.commandMode
}

// UI callback handlers

func (ti *TerminalIntegration) handlePluginLoaded(event UIEvent) error {
	name, _ := event.Data["name"].(string)
	path, _ := event.Data["path"].(string)
	
	ti.AddOutput(fmt.Sprintf("Plugin loaded: %s from %s", name, path), "system")
	return nil
}

func (ti *TerminalIntegration) handlePluginUnloaded(event UIEvent) error {
	name, _ := event.Data["name"].(string)
	
	ti.AddOutput(fmt.Sprintf("Plugin unloaded: %s", name), "system")
	return nil
}

func (ti *TerminalIntegration) handleConfigChanged(event UIEvent) error {
	key, _ := event.Data["key"].(string)
	value := event.Data["value"]
	
	ti.AddOutput(fmt.Sprintf("Config changed: %s = %v", key, value), "config")
	return nil
}

func (ti *TerminalIntegration) handleConfigReloaded(event UIEvent) error {
	ti.AddOutput("Configuration reloaded", "config")
	return nil
}

// Helper functions for the main UI

// ShouldShowHelp returns whether to show help overlay
func (ti *TerminalIntegration) ShouldShowHelp() bool {
	return ti.showHelp
}

// ShouldShowStatus returns whether to show status overlay
func (ti *TerminalIntegration) ShouldShowStatus() bool {
	return ti.showStatus
}

// GetHelpText returns help text for display
func (ti *TerminalIntegration) GetHelpText() string {
	return `Terminal Commands:
  :                 Enter command mode
  escape            Exit command mode
  enter             Execute command
  up/down           Navigate command history
  F1                Toggle this help
  F2                Toggle system status
  
Available Commands:
  plugin.list       List loaded plugins
  plugin.load       Load a plugin
  plugin.unload     Unload a plugin
  plugin.info       Show plugin information
  config.get        Get configuration value
  config.set        Set configuration value
  system.status     Show system status
  system.help       Show available commands`
}

// GetStatusText returns status text for display
func (ti *TerminalIntegration) GetStatusText() string {
	appConfig := ti.coordinator.GetAppConfig()
	plugins := ti.coordinator.GetPluginManager().ListPlugins()
	
	return fmt.Sprintf(`System Status:
  Plugins: %d loaded
  Hot-reload: %t
  Sandbox: %t
  File access: %t
  Network access: %t
  
Recent Output:
%s`, 
		len(plugins),
		appConfig.EnableHotReload,
		appConfig.Security.SandboxMode,
		appConfig.Security.AllowFileAccess,
		appConfig.Security.AllowNetworkAccess,
		ti.getRecentOutput(5))
}

// getRecentOutput returns the last N lines from the output buffer
func (ti *TerminalIntegration) getRecentOutput(n int) string {
	if len(ti.outputBuffer) == 0 {
		return "  (no output)"
	}
	
	start := len(ti.outputBuffer) - n
	if start < 0 {
		start = 0
	}
	
	lines := ti.outputBuffer[start:]
	return "  " + strings.Join(lines, "\n  ")
}
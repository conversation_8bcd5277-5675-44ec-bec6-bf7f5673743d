package app_test

import (
	"testing"

	"eionz.com/demo/pkg/app"
)

// TestBasicStructures tests basic data structures without file I/O
func TestBasicStructures(t *testing.T) {
	// Test Command structure
	cmd := app.Command{
		Name:   "test.command",
		Args:   []string{"arg1", "arg2"},
		Flags:  map[string]interface{}{"flag1": true},
		Source: "test",
	}

	if cmd.Name != "test.command" {
		t.<PERSON><PERSON>("Expected command name 'test.command', got %s", cmd.Name)
	}

	if len(cmd.Args) != 2 {
		t.<PERSON><PERSON><PERSON>("Expected 2 args, got %d", len(cmd.Args))
	}

	// Test CommandResult structure
	result := app.CommandResult{
		Success: true,
		Output:  "test output",
		Data:    map[string]interface{}{"key": "value"},
	}

	if !result.Success {
		t.Error("Expected success to be true")
	}

	if result.Output != "test output" {
		t.<PERSON>("Expected output 'test output', got %s", result.Output)
	}

	if result.Data["key"] != "value" {
		t.<PERSON>("Expected data key 'value', got %v", result.Data["key"])
	}
}

// TestAppConfigStructure tests the app configuration structure
func TestAppConfigStructure(t *testing.T) {
	config := app.AppConfig{
		PluginDir:       "./plugins",
		ConfigDir:       "./config",
		EnableHotReload: true,
		Security: app.SecurityConfig{
			AllowFileAccess:    true,
			AllowNetworkAccess: false,
			TrustedPlugins:     []string{"plugin1"},
			SandboxMode:        true,
		},
		UI: app.UIConfig{
			Theme:           "dark",
			ShowLineNumbers: true,
			WordWrap:        false,
			TabSize:         4,
		},
		Plugins: map[string]string{
			"demo": "./plugins/demo.so",
		},
	}

	if config.PluginDir != "./plugins" {
		t.Errorf("Expected plugin dir './plugins', got %s", config.PluginDir)
	}

	if !config.EnableHotReload {
		t.Error("Expected hot reload to be enabled")
	}

	if !config.Security.AllowFileAccess {
		t.Error("Expected file access to be allowed")
	}

	if config.Security.AllowNetworkAccess {
		t.Error("Expected network access to be denied")
	}

	if config.UI.Theme != "dark" {
		t.Errorf("Expected theme 'dark', got %s", config.UI.Theme)
	}

	if config.UI.TabSize != 4 {
		t.Errorf("Expected tab size 4, got %d", config.UI.TabSize)
	}

	if len(config.Plugins) != 1 {
		t.Errorf("Expected 1 plugin, got %d", len(config.Plugins))
	}
}

// TestMetricsCollectorDisabled tests that disabled collector works correctly
func TestMetricsCollectorDisabled(t *testing.T) {
	collector, err := app.NewMetricsCollector("", false) // Empty dir since disabled
	if err != nil {
		t.Fatalf("Failed to create disabled metrics collector: %v", err)
	}

	// Recording should be no-op when disabled
	collector.RecordCommand("test.command", []string{}, true, 0, "")
	collector.RecordPluginLoad("test_plugin")

	metrics := collector.GetMetrics()
	if metrics.CommandCount != 0 {
		t.Errorf("Disabled collector should not record commands, got %d", metrics.CommandCount)
	}

	summary := collector.GetSummary()
	if summary != "Metrics collection is disabled" {
		t.Errorf("Expected disabled message, got: %s", summary)
	}

	err = collector.ExportMetrics("test.json", "json")
	if err == nil {
		t.Error("Export should fail when metrics are disabled")
	}
}
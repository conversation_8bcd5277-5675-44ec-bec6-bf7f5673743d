package container

import (
	"testing"
)

type TestService struct {
	Value string
}

type TestInterface interface {
	GetValue() string
}

func (ts *TestService) GetValue() string {
	return ts.Value
}

type DependentService struct {
	Dependency *TestService
}

func TestContainer_RegisterAndResolve(t *testing.T) {
	container := NewContainer()

	// Register a singleton service
	err := container.RegisterSingleton("testService", func() (interface{}, error) {
		return &TestService{Value: "test"}, nil
	})
	if err != nil {
		t.Fatalf("Failed to register service: %v", err)
	}

	// Resolve the service
	instance, err := container.Resolve("testService")
	if err != nil {
		t.Fatalf("Failed to resolve service: %v", err)
	}

	service, ok := instance.(*TestService)
	if !ok {
		t.Fatalf("Expected *TestService, got %T", instance)
	}

	if service.Value != "test" {
		t.<PERSON>("Expected 'test', got %s", service.Value)
	}
}

func TestContainer_SingletonScope(t *testing.T) {
	container := NewContainer()

	err := container.RegisterSingleton("testService", func() (interface{}, error) {
		return &TestService{Value: "singleton"}, nil
	})
	if err != nil {
		t.Fatalf("Failed to register service: %v", err)
	}

	// Resolve twice
	instance1, err := container.Resolve("testService")
	if err != nil {
		t.Fatalf("Failed to resolve service: %v", err)
	}

	instance2, err := container.Resolve("testService")
	if err != nil {
		t.Fatalf("Failed to resolve service: %v", err)
	}

	// Should be the same instance
	if instance1 != instance2 {
		t.Error("Singleton instances should be the same")
	}
}

func TestContainer_TransientScope(t *testing.T) {
	container := NewContainer()

	err := container.RegisterTransient("testService", func() (interface{}, error) {
		return &TestService{Value: "transient"}, nil
	})
	if err != nil {
		t.Fatalf("Failed to register service: %v", err)
	}

	// Resolve twice
	instance1, err := container.Resolve("testService")
	if err != nil {
		t.Fatalf("Failed to resolve service: %v", err)
	}

	instance2, err := container.Resolve("testService")
	if err != nil {
		t.Fatalf("Failed to resolve service: %v", err)
	}

	// Should be different instances
	if instance1 == instance2 {
		t.Error("Transient instances should be different")
	}
}

func TestContainer_ScopedScope(t *testing.T) {
	container := NewContainer()

	err := container.RegisterScoped("testService", func() (interface{}, error) {
		return &TestService{Value: "scoped"}, nil
	})
	if err != nil {
		t.Fatalf("Failed to register service: %v", err)
	}

	// Resolve twice in same scope
	instance1, err := container.Resolve("testService")
	if err != nil {
		t.Fatalf("Failed to resolve service: %v", err)
	}

	instance2, err := container.Resolve("testService")
	if err != nil {
		t.Fatalf("Failed to resolve service: %v", err)
	}

	// Should be the same instance in same scope
	if instance1 != instance2 {
		t.Error("Scoped instances should be the same within scope")
	}

	// Create new scope
	newScope := container.CreateScope()
	instance3, err := newScope.Resolve("testService")
	if err != nil {
		t.Fatalf("Failed to resolve service in new scope: %v", err)
	}

	// Should be different instance in different scope
	if instance1 == instance3 {
		t.Error("Scoped instances should be different across scopes")
	}
}

func TestContainer_RegisterInstance(t *testing.T) {
	container := NewContainer()
	testInstance := &TestService{Value: "instance"}

	err := container.RegisterInstance("testService", testInstance)
	if err != nil {
		t.Fatalf("Failed to register instance: %v", err)
	}

	resolved, err := container.Resolve("testService")
	if err != nil {
		t.Fatalf("Failed to resolve instance: %v", err)
	}

	if resolved != testInstance {
		t.Error("Resolved instance should be the same as registered instance")
	}
}

func TestContainer_ResolveAs(t *testing.T) {
	container := NewContainer()

	err := container.RegisterSingleton("testService", func() (interface{}, error) {
		return &TestService{Value: "resolve-as"}, nil
	})
	if err != nil {
		t.Fatalf("Failed to register service: %v", err)
	}

	var service *TestService
	err = container.ResolveAs("testService", &service)
	if err != nil {
		t.Fatalf("Failed to resolve as: %v", err)
	}

	if service == nil {
		t.Fatal("Service should not be nil")
	}

	if service.Value != "resolve-as" {
		t.Errorf("Expected 'resolve-as', got %s", service.Value)
	}
}

func TestContainer_ListServices(t *testing.T) {
	container := NewContainer()

	err := container.RegisterSingleton("service1", func() (interface{}, error) {
		return &TestService{}, nil
	})
	if err != nil {
		t.Fatalf("Failed to register service1: %v", err)
	}

	err = container.RegisterTransient("service2", func() (interface{}, error) {
		return &TestService{}, nil
	})
	if err != nil {
		t.Fatalf("Failed to register service2: %v", err)
	}

	services := container.ListServices()
	if len(services) != 2 {
		t.Errorf("Expected 2 services, got %d", len(services))
	}

	serviceMap := make(map[string]bool)
	for _, service := range services {
		serviceMap[service] = true
	}

	if !serviceMap["service1"] || !serviceMap["service2"] {
		t.Error("Expected services not found in list")
	}
}

func TestContainer_IsRegistered(t *testing.T) {
	container := NewContainer()

	if container.IsRegistered("nonexistent") {
		t.Error("Should not report nonexistent service as registered")
	}

	err := container.RegisterSingleton("testService", func() (interface{}, error) {
		return &TestService{}, nil
	})
	if err != nil {
		t.Fatalf("Failed to register service: %v", err)
	}

	if !container.IsRegistered("testService") {
		t.Error("Should report registered service as registered")
	}
}

func TestContainer_Unregister(t *testing.T) {
	container := NewContainer()

	err := container.RegisterSingleton("testService", func() (interface{}, error) {
		return &TestService{}, nil
	})
	if err != nil {
		t.Fatalf("Failed to register service: %v", err)
	}

	container.Unregister("testService")

	if container.IsRegistered("testService") {
		t.Error("Service should be unregistered")
	}

	_, err = container.Resolve("testService")
	if err == nil {
		t.Error("Should fail to resolve unregistered service")
	}
}

func TestContainer_Clear(t *testing.T) {
	container := NewContainer()

	err := container.RegisterSingleton("service1", func() (interface{}, error) {
		return &TestService{}, nil
	})
	if err != nil {
		t.Fatalf("Failed to register service1: %v", err)
	}

	err = container.RegisterTransient("service2", func() (interface{}, error) {
		return &TestService{}, nil
	})
	if err != nil {
		t.Fatalf("Failed to register service2: %v", err)
	}

	container.Clear()

	services := container.ListServices()
	if len(services) != 0 {
		t.Errorf("Expected 0 services after clear, got %d", len(services))
	}
}
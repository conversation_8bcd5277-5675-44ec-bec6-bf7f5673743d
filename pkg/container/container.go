package container

import (
	"fmt"
	"reflect"
	"sync"
)

// Factory represents a function that creates an instance
type Factory func() (interface{}, error)

// Scope defines the lifecycle of a service
type Scope int

const (
	ScopeSingleton Scope = iota // Created once and reused
	ScopeTransient              // Created every time
	ScopeScoped                 // Created once per scope
)

// ServiceDefinition holds service registration information
type ServiceDefinition struct {
	Factory    Factory
	Scope      Scope
	Instance   interface{}
	Type       reflect.Type
	Interfaces []reflect.Type
}

// Container manages dependency injection
type Container struct {
	services map[string]*ServiceDefinition
	mutex    sync.RWMutex
	scoped   map[string]interface{} // Scoped instances
}

// NewContainer creates a new dependency injection container
func NewContainer() *Container {
	return &Container{
		services: make(map[string]*ServiceDefinition),
		scoped:   make(map[string]interface{}),
	}
}

// Register registers a service with the container
func (c *Container) Register(name string, factory Factory, scope Scope) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Test the factory to get type information
	instance, err := factory()
	if err != nil {
		return fmt.Errorf("factory test failed for %s: %w", name, err)
	}

	serviceType := reflect.TypeOf(instance)
	interfaces := c.getImplementedInterfaces(serviceType)

	c.services[name] = &ServiceDefinition{
		Factory:    factory,
		Scope:      scope,
		Type:       serviceType,
		Interfaces: interfaces,
	}

	return nil
}

// RegisterSingleton registers a singleton service
func (c *Container) RegisterSingleton(name string, factory Factory) error {
	return c.Register(name, factory, ScopeSingleton)
}

// RegisterTransient registers a transient service
func (c *Container) RegisterTransient(name string, factory Factory) error {
	return c.Register(name, factory, ScopeTransient)
}

// RegisterScoped registers a scoped service
func (c *Container) RegisterScoped(name string, factory Factory) error {
	return c.Register(name, factory, ScopeScoped)
}

// RegisterInstance registers an existing instance as a singleton
func (c *Container) RegisterInstance(name string, instance interface{}) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	serviceType := reflect.TypeOf(instance)
	interfaces := c.getImplementedInterfaces(serviceType)

	c.services[name] = &ServiceDefinition{
		Factory:    func() (interface{}, error) { return instance, nil },
		Scope:      ScopeSingleton,
		Instance:   instance,
		Type:       serviceType,
		Interfaces: interfaces,
	}

	return nil
}

// Resolve resolves a service by name
func (c *Container) Resolve(name string) (interface{}, error) {
	c.mutex.RLock()
	service, exists := c.services[name]
	c.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("service %s not registered", name)
	}

	return c.getInstance(name, service)
}

// ResolveAs resolves a service and assigns it to the target pointer
func (c *Container) ResolveAs(name string, target interface{}) error {
	instance, err := c.Resolve(name)
	if err != nil {
		return err
	}

	targetValue := reflect.ValueOf(target)
	if targetValue.Kind() != reflect.Ptr {
		return fmt.Errorf("target must be a pointer")
	}

	targetValue.Elem().Set(reflect.ValueOf(instance))
	return nil
}

// ResolveByType resolves a service by its type or interface
func (c *Container) ResolveByType(targetType reflect.Type) (interface{}, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	for name, service := range c.services {
		// Check if service type matches
		if service.Type == targetType {
			c.mutex.RUnlock()
			instance, err := c.getInstance(name, service)
			c.mutex.RLock()
			return instance, err
		}

		// Check if service implements the interface
		for _, iface := range service.Interfaces {
			if iface == targetType {
				c.mutex.RUnlock()
				instance, err := c.getInstance(name, service)
				c.mutex.RLock()
				return instance, err
			}
		}
	}

	return nil, fmt.Errorf("no service found for type %s", targetType)
}

// CreateScope creates a new scoped container
func (c *Container) CreateScope() *Container {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	scopedContainer := &Container{
		services: c.services, // Share service definitions
		scoped:   make(map[string]interface{}),
	}

	return scopedContainer
}

// ListServices returns all registered service names
func (c *Container) ListServices() []string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	services := make([]string, 0, len(c.services))
	for name := range c.services {
		services = append(services, name)
	}

	return services
}

// IsRegistered checks if a service is registered
func (c *Container) IsRegistered(name string) bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	_, exists := c.services[name]
	return exists
}

// Unregister removes a service registration
func (c *Container) Unregister(name string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	delete(c.services, name)
	delete(c.scoped, name)
}

// Clear removes all service registrations
func (c *Container) Clear() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.services = make(map[string]*ServiceDefinition)
	c.scoped = make(map[string]interface{})
}

// getInstance gets or creates an instance based on scope
func (c *Container) getInstance(name string, service *ServiceDefinition) (interface{}, error) {
	switch service.Scope {
	case ScopeSingleton:
		if service.Instance != nil {
			return service.Instance, nil
		}

		c.mutex.Lock()
		defer c.mutex.Unlock()

		// Double-check pattern
		if service.Instance != nil {
			return service.Instance, nil
		}

		instance, err := service.Factory()
		if err != nil {
			return nil, fmt.Errorf("failed to create singleton %s: %w", name, err)
		}

		service.Instance = instance
		return instance, nil

	case ScopeScoped:
		c.mutex.RLock()
		instance, exists := c.scoped[name]
		c.mutex.RUnlock()

		if exists {
			return instance, nil
		}

		c.mutex.Lock()
		defer c.mutex.Unlock()

		// Double-check pattern
		if instance, exists := c.scoped[name]; exists {
			return instance, nil
		}

		instance, err := service.Factory()
		if err != nil {
			return nil, fmt.Errorf("failed to create scoped %s: %w", name, err)
		}

		c.scoped[name] = instance
		return instance, nil

	case ScopeTransient:
		return service.Factory()

	default:
		return nil, fmt.Errorf("unknown scope for service %s", name)
	}
}

// getImplementedInterfaces returns all interfaces implemented by a type
func (c *Container) getImplementedInterfaces(t reflect.Type) []reflect.Type {
	var interfaces []reflect.Type

	// Handle pointer types
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	// Get all methods of the type
	numMethods := t.NumMethod()
	if numMethods == 0 {
		return interfaces
	}

	// This is a simplified interface detection
	// In a real implementation, you might want to register known interfaces
	// or use a more sophisticated reflection mechanism

	return interfaces
}
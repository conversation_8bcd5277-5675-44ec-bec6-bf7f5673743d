package completion

import (
	"sort"
	"strings"
)

// CompletionProvider defines an interface for providing completions
type CompletionProvider interface {
	GetCompletions(input string, cursorPos int) []Completion
	GetName() string
}

// Completion represents a single completion suggestion
type Completion struct {
	Text        string // The text to insert
	Display     string // What to show in the completion list (may include description)
	Description string // Detailed description
	Type        CompletionType
	Score       int // Higher score = better match
}

// CompletionType categorizes different types of completions
type CompletionType int

const (
	CompletionTypeCommand CompletionType = iota
	CompletionTypeFile
	CompletionTypeDirectory
	CompletionTypeKeyword
	CompletionTypeVariable
	CompletionTypeFunction
	CompletionTypePlugin
	CompletionTypeOption
)

func (ct CompletionType) String() string {
	switch ct {
	case CompletionTypeCommand:
		return "command"
	case CompletionTypeFile:
		return "file"
	case CompletionTypeDirectory:
		return "directory"
	case CompletionTypeKeyword:
		return "keyword"
	case CompletionTypeVariable:
		return "variable"
	case CompletionTypeFunction:
		return "function"
	case CompletionTypePlugin:
		return "plugin"
	case CompletionTypeOption:
		return "option"
	default:
		return "unknown"
	}
}

// CompletionEngine manages multiple completion providers
type CompletionEngine struct {
	providers []CompletionProvider
	maxItems  int
}

// NewCompletionEngine creates a new completion engine
func NewCompletionEngine() *CompletionEngine {
	return &CompletionEngine{
		providers: make([]CompletionProvider, 0),
		maxItems:  50, // Default maximum completions to show
	}
}

// AddProvider adds a completion provider to the engine
func (ce *CompletionEngine) AddProvider(provider CompletionProvider) {
	ce.providers = append(ce.providers, provider)
}

// RemoveProvider removes a completion provider by name
func (ce *CompletionEngine) RemoveProvider(name string) {
	for i, provider := range ce.providers {
		if provider.GetName() == name {
			ce.providers = append(ce.providers[:i], ce.providers[i+1:]...)
			break
		}
	}
}

// SetMaxItems sets the maximum number of completions to return
func (ce *CompletionEngine) SetMaxItems(max int) {
	ce.maxItems = max
}

// GetCompletions returns completions for the given input
func (ce *CompletionEngine) GetCompletions(input string, cursorPos int) []Completion {
	var allCompletions []Completion
	
	// Collect completions from all providers
	for _, provider := range ce.providers {
		completions := provider.GetCompletions(input, cursorPos)
		allCompletions = append(allCompletions, completions...)
	}
	
	// Remove duplicates and sort by score
	completions := ce.deduplicateAndSort(allCompletions)
	
	// Limit to maxItems
	if len(completions) > ce.maxItems {
		completions = completions[:ce.maxItems]
	}
	
	return completions
}

// deduplicateAndSort removes duplicate completions and sorts by score
func (ce *CompletionEngine) deduplicateAndSort(completions []Completion) []Completion {
	seen := make(map[string]bool)
	var unique []Completion
	
	for _, completion := range completions {
		key := completion.Text + ":" + completion.Type.String()
		if !seen[key] {
			seen[key] = true
			unique = append(unique, completion)
		}
	}
	
	// Sort by score (descending) and then by text (ascending)
	sort.Slice(unique, func(i, j int) bool {
		if unique[i].Score == unique[j].Score {
			return unique[i].Text < unique[j].Text
		}
		return unique[i].Score > unique[j].Score
	})
	
	return unique
}

// CommandCompletionProvider provides completions for application commands
type CommandCompletionProvider struct {
	commands map[string]CommandInfo
}

// CommandInfo holds information about a command
type CommandInfo struct {
	Name        string
	Description string
	Usage       string
	Options     []string
}

// NewCommandCompletionProvider creates a new command completion provider
func NewCommandCompletionProvider() *CommandCompletionProvider {
	provider := &CommandCompletionProvider{
		commands: make(map[string]CommandInfo),
	}
	
	// Register default commands
	provider.registerDefaultCommands()
	
	return provider
}

// registerDefaultCommands registers built-in application commands
func (ccp *CommandCompletionProvider) registerDefaultCommands() {
	ccp.commands["help"] = CommandInfo{
		Name:        "help",
		Description: "Show help information",
		Usage:       "help [command]",
		Options:     []string{},
	}
	
	ccp.commands["quit"] = CommandInfo{
		Name:        "quit",
		Description: "Exit the application",
		Usage:       "quit",
		Options:     []string{},
	}
	
	ccp.commands["exit"] = CommandInfo{
		Name:        "exit",
		Description: "Exit the application",
		Usage:       "exit",
		Options:     []string{},
	}
	
	ccp.commands["load"] = CommandInfo{
		Name:        "load",
		Description: "Load content from a file",
		Usage:       "load <file>",
		Options:     []string{},
	}
	
	ccp.commands["reload"] = CommandInfo{
		Name:        "reload",
		Description: "Reload the current content",
		Usage:       "reload",
		Options:     []string{},
	}
	
	ccp.commands["theme"] = CommandInfo{
		Name:        "theme",
		Description: "Change syntax highlighting theme",
		Usage:       "theme <theme_name>",
		Options:     []string{"dark", "light", "default", "monochrome"},
	}
	
	ccp.commands["syntax"] = CommandInfo{
		Name:        "syntax",
		Description: "Control syntax highlighting",
		Usage:       "syntax <on|off|language>",
		Options:     []string{"on", "off", "go", "javascript", "python", "json", "yaml"},
	}
	
	ccp.commands["search"] = CommandInfo{
		Name:        "search",
		Description: "Search within content",
		Usage:       "search <pattern>",
		Options:     []string{},
	}
	
	ccp.commands["goto"] = CommandInfo{
		Name:        "goto",
		Description: "Go to specific line",
		Usage:       "goto <line_number>",
		Options:     []string{},
	}
}

// GetName returns the provider name
func (ccp *CommandCompletionProvider) GetName() string {
	return "commands"
}

// RegisterCommand adds a new command to the completion provider
func (ccp *CommandCompletionProvider) RegisterCommand(info CommandInfo) {
	ccp.commands[info.Name] = info
}

// UnregisterCommand removes a command from the completion provider
func (ccp *CommandCompletionProvider) UnregisterCommand(name string) {
	delete(ccp.commands, name)
}

// GetCompletions returns command completions for the given input
func (ccp *CommandCompletionProvider) GetCompletions(input string, cursorPos int) []Completion {
	var completions []Completion
	
	// Remove leading "/" if present for processing
	processInput := input
	if strings.HasPrefix(processInput, "/") {
		processInput = processInput[1:]
		cursorPos-- // Adjust cursor position
	}
	
	// Extract the current word being typed
	words := strings.Fields(processInput[:cursorPos])
	if len(words) == 0 {
		// Show all commands if no input (or just "/")
		for _, cmd := range ccp.commands {
			completions = append(completions, Completion{
				Text:        "/" + cmd.Name, // Include "/" in the completion
				Display:     cmd.Name + " - " + cmd.Description,
				Description: cmd.Usage,
				Type:        CompletionTypeCommand,
				Score:       100,
			})
		}
		return completions
	}
	
	currentWord := ""
	if cursorPos == len(processInput) || processInput[cursorPos-1] != ' ' {
		currentWord = words[len(words)-1]
	}
	
	if len(words) == 1 && currentWord != "" {
		// Completing command name
		for _, cmd := range ccp.commands {
			if strings.HasPrefix(cmd.Name, currentWord) {
				score := calculateScore(cmd.Name, currentWord)
				completions = append(completions, Completion{
					Text:        "/" + cmd.Name, // Include "/" in the completion
					Display:     cmd.Name + " - " + cmd.Description,
					Description: cmd.Usage,
					Type:        CompletionTypeCommand,
					Score:       score,
				})
			}
		}
	} else if len(words) >= 1 {
		// Completing command options/arguments
		commandName := words[0]
		if cmd, exists := ccp.commands[commandName]; exists {
			for _, option := range cmd.Options {
				if currentWord == "" || strings.HasPrefix(option, currentWord) {
					score := calculateScore(option, currentWord)
					completions = append(completions, Completion{
						Text:        option, // Just the option, not the full command
						Display:     option,
						Description: "Option for " + commandName,
						Type:        CompletionTypeOption,
						Score:       score,
					})
				}
			}
		}
	}
	
	return completions
}

// FileCompletionProvider provides file and directory completions
type FileCompletionProvider struct {
	basePath string
}

// NewFileCompletionProvider creates a new file completion provider
func NewFileCompletionProvider(basePath string) *FileCompletionProvider {
	if basePath == "" {
		basePath = "."
	}
	return &FileCompletionProvider{
		basePath: basePath,
	}
}

// GetName returns the provider name
func (fcp *FileCompletionProvider) GetName() string {
	return "files"
}

// SetBasePath sets the base path for file completions
func (fcp *FileCompletionProvider) SetBasePath(path string) {
	fcp.basePath = path
}

// GetCompletions returns file completions for the given input
func (fcp *FileCompletionProvider) GetCompletions(input string, cursorPos int) []Completion {
	var completions []Completion
	
	// Extract the current path being typed
	words := strings.Fields(input[:cursorPos])
	if len(words) == 0 {
		return completions
	}
	
	// Only provide file completions for certain commands
	if len(words) >= 1 {
		command := words[0]
		if !isFileCommand(command) {
			return completions
		}
	}
	
	currentPath := ""
	if len(words) > 1 {
		currentPath = words[len(words)-1]
	}
	
	// TODO: Implement actual file system traversal
	// For now, return some example completions
	exampleFiles := []string{
		"test-content.txt",
		"sample.txt",
		"README.md",
		"main.go",
		"config.json",
	}
	
	for _, file := range exampleFiles {
		if currentPath == "" || strings.HasPrefix(file, currentPath) {
			score := calculateScore(file, currentPath)
			completions = append(completions, Completion{
				Text:        file,
				Display:     file,
				Description: "File",
				Type:        CompletionTypeFile,
				Score:       score,
			})
		}
	}
	
	return completions
}

// KeywordCompletionProvider provides keyword completions for syntax highlighting
type KeywordCompletionProvider struct {
	keywords map[string][]string
}

// NewKeywordCompletionProvider creates a new keyword completion provider
func NewKeywordCompletionProvider() *KeywordCompletionProvider {
	provider := &KeywordCompletionProvider{
		keywords: make(map[string][]string),
	}
	
	// Register language keywords
	provider.keywords["go"] = []string{
		"package", "import", "func", "var", "const", "type", "struct", "interface",
		"if", "else", "for", "range", "switch", "case", "default", "break", "continue",
		"return", "go", "defer", "select", "chan", "map", "slice",
	}
	
	provider.keywords["javascript"] = []string{
		"function", "var", "let", "const", "if", "else", "for", "while", "do",
		"switch", "case", "default", "break", "continue", "return", "try", "catch",
		"finally", "throw", "class", "extends", "import", "export", "async", "await",
	}
	
	provider.keywords["python"] = []string{
		"def", "class", "if", "elif", "else", "for", "while", "try", "except",
		"finally", "with", "import", "from", "as", "return", "yield", "break",
		"continue", "pass", "raise", "lambda", "and", "or", "not", "in", "is",
	}
	
	return provider
}

// GetName returns the provider name
func (kcp *KeywordCompletionProvider) GetName() string {
	return "keywords"
}

// GetCompletions returns keyword completions for the given input
func (kcp *KeywordCompletionProvider) GetCompletions(input string, cursorPos int) []Completion {
	var completions []Completion
	
	// Extract context to determine language
	words := strings.Fields(input[:cursorPos])
	if len(words) == 0 {
		return completions
	}
	
	// Check if this is a syntax command
	if len(words) >= 2 && words[0] == "syntax" {
		language := ""
		currentWord := ""
		
		if len(words) >= 3 {
			language = words[1]
			currentWord = words[len(words)-1]
		} else if len(words) == 2 {
			currentWord = words[1]
		}
		
		// If language is specified, provide keywords for that language
		if language != "" && language != "on" && language != "off" {
			if keywords, exists := kcp.keywords[language]; exists {
				for _, keyword := range keywords {
					if currentWord == "" || strings.HasPrefix(keyword, currentWord) {
						score := calculateScore(keyword, currentWord)
						completions = append(completions, Completion{
							Text:        keyword,
							Display:     keyword,
							Description: language + " keyword",
							Type:        CompletionTypeKeyword,
							Score:       score,
						})
					}
				}
			}
		}
	}
	
	return completions
}

// Helper functions

// calculateScore calculates a matching score between a candidate and input
func calculateScore(candidate, input string) int {
	if input == "" {
		return 50 // Base score for empty input
	}
	
	if candidate == input {
		return 100 // Perfect match
	}
	
	if strings.HasPrefix(candidate, input) {
		// Prefix match - score based on how much of the candidate is matched
		return 80 + (len(input)*20)/len(candidate)
	}
	
	if strings.Contains(candidate, input) {
		// Substring match
		return 60
	}
	
	// Fuzzy match - check if all characters of input appear in order in candidate
	if fuzzyMatch(candidate, input) {
		return 40
	}
	
	return 0 // No match
}

// fuzzyMatch checks if all characters in input appear in order in candidate
func fuzzyMatch(candidate, input string) bool {
	candidateRunes := []rune(strings.ToLower(candidate))
	inputRunes := []rune(strings.ToLower(input))
	
	i := 0
	for _, c := range candidateRunes {
		if i < len(inputRunes) && c == inputRunes[i] {
			i++
		}
	}
	
	return i == len(inputRunes)
}

// isFileCommand checks if a command typically takes file arguments
func isFileCommand(command string) bool {
	fileCommands := []string{"load", "open", "read", "write", "save", "edit"}
	for _, cmd := range fileCommands {
		if command == cmd {
			return true
		}
	}
	return false
}

// GetAvailableLanguages returns list of supported syntax highlighting languages
func GetAvailableLanguages() []string {
	return []string{
		"go", "javascript", "python", "json", "yaml", "markdown",
		"sql", "xml", "html", "css", "shell", "java", "c", "cpp",
		"text",
	}
}
package completion

import (
	"strings"

	"github.com/charmbracelet/bubbles/key"
	"github.com/charmbracelet/bubbles/textinput"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// CompletionInput provides a text input with auto-completion capabilities
type CompletionInput struct {
	textInput         textinput.Model
	completionEngine  *CompletionEngine
	completions       []Completion
	selectedIndex     int
	showCompletions   bool
	maxCompletions    int
	completionHeight  int
	active            bool
	prompt            string
	styles            CompletionStyles
}

// CompletionStyles defines the styling for the completion input
type CompletionStyles struct {
	Input           lipgloss.Style
	Prompt          lipgloss.Style
	Completion      lipgloss.Style
	SelectedCompletion lipgloss.Style
	CompletionBox   lipgloss.Style
	CompletionType  lipgloss.Style
	Description     lipgloss.Style
}

// DefaultCompletionStyles returns default styles for the completion input
func DefaultCompletionStyles() CompletionStyles {
	return CompletionStyles{
		Input: lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("240")).
			Padding(0, 1),
		Prompt: lipgloss.NewStyle().
			Foreground(lipgloss.Color("205")).
			Bold(true),
		Completion: lipgloss.NewStyle().
			Padding(0, 1).
			Foreground(lipgloss.Color("252")),
		SelectedCompletion: lipgloss.NewStyle().
			Padding(0, 1).
			Background(lipgloss.Color("62")).
			Foreground(lipgloss.Color("255")).
			Bold(true),
		CompletionBox: lipgloss.NewStyle(),
		CompletionType: lipgloss.NewStyle().
			Foreground(lipgloss.Color("99")).
			Italic(true),
		Description: lipgloss.NewStyle().
			Foreground(lipgloss.Color("243")).
			Italic(true),
	}
}

// NewCompletionInput creates a new completion input
func NewCompletionInput(prompt string) *CompletionInput {
	ti := textinput.New()
	ti.Focus()
	ti.CharLimit = 256
	ti.Width = 50
	
	engine := NewCompletionEngine()
	engine.AddProvider(NewCommandCompletionProvider())
	engine.AddProvider(NewFileCompletionProvider("."))
	engine.AddProvider(NewKeywordCompletionProvider())
	
	return &CompletionInput{
		textInput:         ti,
		completionEngine:  engine,
		completions:       []Completion{},
		selectedIndex:     0,
		showCompletions:   false,
		maxCompletions:    10,
		completionHeight:  8,
		active:            true,
		prompt:            prompt,
		styles:            DefaultCompletionStyles(),
	}
}

// SetStyles sets custom styles for the completion input
func (ci *CompletionInput) SetStyles(styles CompletionStyles) {
	ci.styles = styles
}

// SetWidth sets the width of the input field
func (ci *CompletionInput) SetWidth(width int) {
	ci.textInput.Width = width
}

// SetPrompt sets the prompt text
func (ci *CompletionInput) SetPrompt(prompt string) {
	ci.prompt = prompt
}

// SetActive sets whether the input is active (focused)
func (ci *CompletionInput) SetActive(active bool) {
	ci.active = active
	if active {
		ci.textInput.Focus()
	} else {
		ci.textInput.Blur()
	}
}

// IsActive returns whether the input is active
func (ci *CompletionInput) IsActive() bool {
	return ci.active
}

// Value returns the current input value
func (ci *CompletionInput) Value() string {
	return ci.textInput.Value()
}

// SetValue sets the input value
func (ci *CompletionInput) SetValue(value string) {
	ci.textInput.SetValue(value)
	ci.updateCompletions()
}

// Clear clears the input
func (ci *CompletionInput) Clear() {
	ci.textInput.SetValue("")
	ci.completions = []Completion{}
	ci.showCompletions = false
	ci.selectedIndex = 0
}

// GetCompletionEngine returns the completion engine for adding providers
func (ci *CompletionInput) GetCompletionEngine() *CompletionEngine {
	return ci.completionEngine
}

// Update handles input events and updates the completion state
func (ci *CompletionInput) Update(msg tea.Msg) tea.Cmd {
	if !ci.active {
		return nil
	}
	
	var cmd tea.Cmd
	
	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch {
		case key.Matches(msg, key.NewBinding(key.WithKeys("tab"))):
			// Tab: accept selected completion or show completions
			value := ci.textInput.Value()
			// Only show completions if input starts with "/"
			if !strings.HasPrefix(value, "/") {
				return nil
			}
			
			if ci.showCompletions && len(ci.completions) > 0 {
				completion := ci.completions[ci.selectedIndex]
				ci.acceptCompletion(completion)
				return nil
			} else {
				ci.updateCompletions()
				ci.showCompletions = true
				return nil
			}
		
		case key.Matches(msg, key.NewBinding(key.WithKeys("shift+tab"))):
			// Shift+Tab: hide completions
			ci.showCompletions = false
			return nil
		
		case key.Matches(msg, key.NewBinding(key.WithKeys("up"))):
			// Up arrow: select previous completion
			if ci.showCompletions && len(ci.completions) > 0 {
				ci.selectedIndex--
				if ci.selectedIndex < 0 {
					ci.selectedIndex = len(ci.completions) - 1
				}
				return nil
			}
		
		case key.Matches(msg, key.NewBinding(key.WithKeys("down"))):
			// Down arrow: select next completion
			if ci.showCompletions && len(ci.completions) > 0 {
				ci.selectedIndex++
				if ci.selectedIndex >= len(ci.completions) {
					ci.selectedIndex = 0
				}
				return nil
			}
		
		case key.Matches(msg, key.NewBinding(key.WithKeys("enter"))):
			// Enter: accept selected completion if shown, otherwise execute command
			if ci.showCompletions && len(ci.completions) > 0 {
				completion := ci.completions[ci.selectedIndex]
				ci.acceptCompletion(completion)
				return nil
			}
			// Command execution would be handled by parent component
			
		case key.Matches(msg, key.NewBinding(key.WithKeys("esc"))):
			// Escape: hide completions
			ci.showCompletions = false
			return nil
		
		default:
			// Regular character input
			prevValue := ci.textInput.Value()
			ci.textInput, cmd = ci.textInput.Update(msg)
			
			// Update completions if text changed
			if ci.textInput.Value() != prevValue {
				ci.updateCompletions()
				if len(ci.completions) > 0 {
					ci.showCompletions = true
					ci.selectedIndex = 0
				} else {
					ci.showCompletions = false
				}
			}
		}
	
	default:
		ci.textInput, cmd = ci.textInput.Update(msg)
	}
	
	return cmd
}

// acceptCompletion inserts the selected completion into the input
func (ci *CompletionInput) acceptCompletion(completion Completion) {
	value := ci.textInput.Value()
	cursorPos := ci.textInput.Position()
	
	// Special handling for command options vs command names
	if completion.Type == CompletionTypeOption {
		// For options, we want to append the option to the existing command
		// Find the last space and replace everything after it
		lastSpaceIndex := strings.LastIndex(value[:cursorPos], " ")
		if lastSpaceIndex != -1 {
			// Replace from after the last space to cursor position
			newValue := value[:lastSpaceIndex+1] + completion.Display
			if cursorPos < len(value) {
				newValue += value[cursorPos:]
			}
			ci.textInput.SetValue(newValue)
			ci.textInput.SetCursor(lastSpaceIndex + 1 + len(completion.Display))
		} else {
			// No space found, just replace the entire input
			ci.textInput.SetValue(completion.Text)
			ci.textInput.SetCursor(len(completion.Text))
		}
	} else {
		// For command names, use the original logic
		// Find the start of the current word
		words := strings.FieldsFunc(value[:cursorPos], func(r rune) bool {
			return r == ' ' || r == '\t'
		})
		
		var wordStart int
		if len(words) > 0 {
			// Find where the last word starts
			lastWord := words[len(words)-1]
			wordStart = strings.LastIndex(value[:cursorPos], lastWord)
		} else {
			wordStart = cursorPos
		}
		
		// Replace the current word with the completion
		newValue := value[:wordStart] + completion.Text
		if cursorPos < len(value) {
			newValue += value[cursorPos:]
		}
		
		ci.textInput.SetValue(newValue)
		ci.textInput.SetCursor(wordStart + len(completion.Text))
	}
	
	// Hide completions after accepting one
	ci.showCompletions = false
	ci.completions = []Completion{}
}

// updateCompletions refreshes the completion list based on current input
func (ci *CompletionInput) updateCompletions() {
	value := ci.textInput.Value()
	cursorPos := ci.textInput.Position()
	
	// Only show completions if the input starts with "/"
	if !strings.HasPrefix(value, "/") {
		ci.completions = []Completion{}
		ci.showCompletions = false
		ci.selectedIndex = 0
		return
	}
	
	ci.completions = ci.completionEngine.GetCompletions(value, cursorPos)
	
	// Limit completions to maxCompletions
	if len(ci.completions) > ci.maxCompletions {
		ci.completions = ci.completions[:ci.maxCompletions]
	}
	
	// Reset selection if no completions
	if len(ci.completions) == 0 {
		ci.selectedIndex = 0
	} else if ci.selectedIndex >= len(ci.completions) {
		ci.selectedIndex = len(ci.completions) - 1
	}
}

// View renders the completion input
func (ci *CompletionInput) View() string {
	var sections []string
	
	// Render completions first (above input) with proper alignment
	if ci.showCompletions && len(ci.completions) > 0 {
		completionBox := ci.renderCompletions()
		
		// Add 1 character padding to align with input text
		completionLines := strings.Split(completionBox, "\n")
		for i, line := range completionLines {
			completionLines[i] = " " + line
		}
		completionBox = strings.Join(completionLines, "\n")
		
		sections = append(sections, completionBox)
	}
	
	// Render prompt and input (below completions)
	prompt := ci.styles.Prompt.Render(ci.prompt)
	input := ci.styles.Input.Render(ci.textInput.View())
	
	if len(prompt) > 0 {
		inputLine := lipgloss.JoinHorizontal(lipgloss.Center, prompt, " ", input)
		sections = append(sections, inputLine)
	} else {
		sections = append(sections, input)
	}
	
	return lipgloss.JoinVertical(lipgloss.Left, sections...)
}

// renderCompletions renders the completion popup
func (ci *CompletionInput) renderCompletions() string {
	var lines []string
	
	for i, completion := range ci.completions {
		var style lipgloss.Style
		if i == ci.selectedIndex {
			style = ci.styles.SelectedCompletion
		} else {
			style = ci.styles.Completion
		}
		
		// Format completion line
		typeText := ci.styles.CompletionType.Render("[" + completion.Type.String() + "]")
		line := style.Render(completion.Display)
		
		// Add type indicator and description if there's space
		if len(completion.Description) > 0 && len(line) < 60 {
			desc := ci.styles.Description.Render(" - " + completion.Description)
			line = lipgloss.JoinHorizontal(lipgloss.Left, line, desc)
		}
		
		line = lipgloss.JoinHorizontal(lipgloss.Left, typeText, " ", line)
		lines = append(lines, line)
	}
	
	content := strings.Join(lines, "\n")
	return ci.styles.CompletionBox.Render(content)
}

// GetHeight returns the total height of the completion input including popup
func (ci *CompletionInput) GetHeight() int {
	height := 1 // Input line
	
	if ci.showCompletions && len(ci.completions) > 0 {
		completionLines := len(ci.completions)
		if completionLines > ci.maxCompletions {
			completionLines = ci.maxCompletions
		}
		height += completionLines + 2 // +2 for border
	}
	
	return height
}

// GetWidth returns the width of the completion input
func (ci *CompletionInput) GetWidth() int {
	return ci.textInput.Width + len(ci.prompt) + 4 // +4 for spacing and borders
}
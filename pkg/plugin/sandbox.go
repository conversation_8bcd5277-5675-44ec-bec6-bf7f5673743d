package plugin

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

// SimpleSandbox provides a basic implementation of the Sandbox interface
type SimpleSandbox struct {
	id         string
	pluginID   PluginID
	config     SandboxConfig
	active     int32 // atomic
	mu         sync.RWMutex
	startTime  time.Time
	lastAccess time.Time
	
	// Resource tracking
	resourceUsage ResourceUsage
	executions    int64
	
	// Security context
	permissions map[string]bool
	
	// Cleanup functions
	cleanup []func() error
}

// NewSimpleSandbox creates a new sandbox with the given configuration
func NewSimpleSandbox(pluginID PluginID, config SandboxConfig) *SimpleSandbox {
	sandboxID := fmt.Sprintf("sandbox-%s-%d", pluginID.String(), time.Now().UnixNano())
	
	// Initialize permissions map
	permissions := make(map[string]bool)
	for _, perm := range config.Permissions {
		permissions[perm] = true
	}
	
	return &SimpleSandbox{
		id:          sandboxID,
		pluginID:    pluginID,
		config:      config,
		active:      1, // Start as active
		startTime:   time.Now(),
		lastAccess:  time.Now(),
		permissions: permissions,
		cleanup:     make([]func() error, 0),
		resourceUsage: ResourceUsage{
			LastUpdated: time.Now(),
		},
	}
}

func (s *SimpleSandbox) ID() string {
	return s.id
}

func (s *SimpleSandbox) PluginID() PluginID {
	return s.pluginID
}

func (s *SimpleSandbox) Execute(ctx context.Context, fn func() error) error {
	if !s.IsActive() {
		return fmt.Errorf("sandbox %s is not active", s.id)
	}
	
	// Update access time
	s.mu.Lock()
	s.lastAccess = time.Now()
	s.mu.Unlock()
	
	// Increment execution counter
	atomic.AddInt64(&s.executions, 1)
	
	// Create execution context with timeout
	execCtx := ctx
	if s.config.Timeout > 0 {
		var cancel context.CancelFunc
		execCtx, cancel = context.WithTimeout(ctx, s.config.Timeout)
		defer cancel()
	}
	
	// Track execution start time for resource monitoring
	startTime := time.Now()
	
	// Execute the function with resource monitoring
	done := make(chan error, 1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				done <- fmt.Errorf("plugin execution panicked: %v", r)
			}
		}()
		
		// Update resource usage before execution
		s.updateResourceUsage()
		
		// Execute the function
		done <- fn()
	}()
	
	select {
	case err := <-done:
		// Update execution time
		executionTime := time.Since(startTime)
		s.mu.Lock()
		s.resourceUsage.ExecutionTime += executionTime
		s.resourceUsage.LastUpdated = time.Now()
		s.mu.Unlock()
		
		// Check resource limits after execution
		if err := s.checkResourceLimits(); err != nil {
			return fmt.Errorf("resource limit exceeded: %w", err)
		}
		
		return err
		
	case <-execCtx.Done():
		return fmt.Errorf("execution timeout or cancelled: %w", execCtx.Err())
	}
}

func (s *SimpleSandbox) IsActive() bool {
	return atomic.LoadInt32(&s.active) == 1
}

func (s *SimpleSandbox) GetResourceUsage() ResourceUsage {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	s.updateResourceUsageLocked()
	return s.resourceUsage
}

func (s *SimpleSandbox) Destroy() error {
	if !atomic.CompareAndSwapInt32(&s.active, 1, 0) {
		return fmt.Errorf("sandbox %s is already destroyed", s.id)
	}
	
	s.mu.Lock()
	defer s.mu.Unlock()
	
	// Run cleanup functions
	var errors []error
	for _, cleanup := range s.cleanup {
		if err := cleanup(); err != nil {
			errors = append(errors, err)
		}
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("cleanup errors: %v", errors)
	}
	
	return nil
}

// AddCleanup adds a cleanup function to be called when the sandbox is destroyed
func (s *SimpleSandbox) AddCleanup(fn func() error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	s.cleanup = append(s.cleanup, fn)
}

// HasPermission checks if the sandbox has a specific permission
func (s *SimpleSandbox) HasPermission(permission string) bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	return s.permissions[permission]
}

// GetExecutionCount returns the number of executions performed in this sandbox
func (s *SimpleSandbox) GetExecutionCount() int64 {
	return atomic.LoadInt64(&s.executions)
}

// GetUptime returns how long the sandbox has been active
func (s *SimpleSandbox) GetUptime() time.Duration {
	return time.Since(s.startTime)
}

// GetLastAccess returns the last access time
func (s *SimpleSandbox) GetLastAccess() time.Time {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	return s.lastAccess
}

// updateResourceUsage updates the current resource usage (must be called with lock held)
func (s *SimpleSandbox) updateResourceUsage() {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	s.updateResourceUsageLocked()
}

func (s *SimpleSandbox) updateResourceUsageLocked() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	// Simple approximation - in production, you'd want more sophisticated tracking
	s.resourceUsage.Memory = int64(m.Alloc)
	s.resourceUsage.LastUpdated = time.Now()
	
	// CPU usage would require more complex tracking
	// File handles and network connections would require OS-specific monitoring
}

// checkResourceLimits verifies that resource usage is within configured limits
func (s *SimpleSandbox) checkResourceLimits() error {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	limits := s.config.ResourceLimits
	usage := s.resourceUsage
	
	if limits.MaxMemory > 0 && usage.Memory > limits.MaxMemory {
		return fmt.Errorf("memory limit exceeded: %d > %d", usage.Memory, limits.MaxMemory)
	}
	
	if limits.MaxExecutionTime > 0 && usage.ExecutionTime > limits.MaxExecutionTime {
		return fmt.Errorf("execution time limit exceeded: %v > %v", usage.ExecutionTime, limits.MaxExecutionTime)
	}
	
	return nil
}

// InMemoryPluginSandbox provides an in-memory implementation of PluginSandbox
type InMemoryPluginSandbox struct {
	sandboxes map[string]*SimpleSandbox
	mu        sync.RWMutex
}

// NewInMemoryPluginSandbox creates a new in-memory plugin sandbox manager
func NewInMemoryPluginSandbox() *InMemoryPluginSandbox {
	return &InMemoryPluginSandbox{
		sandboxes: make(map[string]*SimpleSandbox),
	}
}

func (ips *InMemoryPluginSandbox) CreateSandbox(pluginID PluginID, config SandboxConfig) (Sandbox, error) {
	sandbox := NewSimpleSandbox(pluginID, config)
	
	ips.mu.Lock()
	defer ips.mu.Unlock()
	
	ips.sandboxes[sandbox.ID()] = sandbox
	
	return sandbox, nil
}

func (ips *InMemoryPluginSandbox) DestroySandbox(sandboxID string) error {
	ips.mu.Lock()
	defer ips.mu.Unlock()
	
	sandbox, exists := ips.sandboxes[sandboxID]
	if !exists {
		return fmt.Errorf("sandbox %s not found", sandboxID)
	}
	
	err := sandbox.Destroy()
	delete(ips.sandboxes, sandboxID)
	
	return err
}

func (ips *InMemoryPluginSandbox) SetResourceLimits(sandboxID string, limits ResourceLimits) error {
	ips.mu.RLock()
	defer ips.mu.RUnlock()
	
	sandbox, exists := ips.sandboxes[sandboxID]
	if !exists {
		return fmt.Errorf("sandbox %s not found", sandboxID)
	}
	
	sandbox.mu.Lock()
	sandbox.config.ResourceLimits = limits
	sandbox.mu.Unlock()
	
	return nil
}

func (ips *InMemoryPluginSandbox) GetResourceUsage(sandboxID string) (ResourceUsage, error) {
	ips.mu.RLock()
	defer ips.mu.RUnlock()
	
	sandbox, exists := ips.sandboxes[sandboxID]
	if !exists {
		return ResourceUsage{}, fmt.Errorf("sandbox %s not found", sandboxID)
	}
	
	return sandbox.GetResourceUsage(), nil
}

func (ips *InMemoryPluginSandbox) SetPermissions(sandboxID string, permissions []string) error {
	ips.mu.RLock()
	defer ips.mu.RUnlock()
	
	sandbox, exists := ips.sandboxes[sandboxID]
	if !exists {
		return fmt.Errorf("sandbox %s not found", sandboxID)
	}
	
	sandbox.mu.Lock()
	defer sandbox.mu.Unlock()
	
	// Update permissions map
	sandbox.permissions = make(map[string]bool)
	for _, perm := range permissions {
		sandbox.permissions[perm] = true
	}
	
	// Update config
	sandbox.config.Permissions = permissions
	
	return nil
}

func (ips *InMemoryPluginSandbox) ValidateAccess(sandboxID string, resource string, operation string) error {
	ips.mu.RLock()
	defer ips.mu.RUnlock()
	
	sandbox, exists := ips.sandboxes[sandboxID]
	if !exists {
		return fmt.Errorf("sandbox %s not found", sandboxID)
	}
	
	if !sandbox.IsActive() {
		return fmt.Errorf("sandbox %s is not active", sandboxID)
	}
	
	// Check specific permission
	permission := fmt.Sprintf("%s:%s", resource, operation)
	if !sandbox.HasPermission(permission) {
		// Check wildcard permissions
		wildcardPermission := fmt.Sprintf("%s:*", resource)
		if !sandbox.HasPermission(wildcardPermission) && !sandbox.HasPermission("*") {
			return fmt.Errorf("access denied: missing permission %s", permission)
		}
	}
	
	return nil
}

// GetSandbox returns a sandbox by ID (for testing/monitoring)
func (ips *InMemoryPluginSandbox) GetSandbox(sandboxID string) (*SimpleSandbox, bool) {
	ips.mu.RLock()
	defer ips.mu.RUnlock()
	
	sandbox, exists := ips.sandboxes[sandboxID]
	return sandbox, exists
}

// ListSandboxes returns all active sandboxes
func (ips *InMemoryPluginSandbox) ListSandboxes() map[string]*SimpleSandbox {
	ips.mu.RLock()
	defer ips.mu.RUnlock()
	
	result := make(map[string]*SimpleSandbox)
	for id, sandbox := range ips.sandboxes {
		if sandbox.IsActive() {
			result[id] = sandbox
		}
	}
	
	return result
}

// CleanupInactiveSandboxes removes destroyed sandboxes from the registry
func (ips *InMemoryPluginSandbox) CleanupInactiveSandboxes() int {
	ips.mu.Lock()
	defer ips.mu.Unlock()
	
	var removed int
	for id, sandbox := range ips.sandboxes {
		if !sandbox.IsActive() {
			delete(ips.sandboxes, id)
			removed++
		}
	}
	
	return removed
}
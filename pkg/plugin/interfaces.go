// Package plugin defines the core plugin system interfaces and contracts
package plugin

import (
	"context"
	"fmt"
	"time"

	"eionz.com/demo/pkg/domain"
)

// PluginID represents a unique plugin identifier
type PluginID struct {
	value string
}

func NewPluginID(value string) PluginID {
	return PluginID{value: value}
}

func (id PluginID) String() string {
	return id.value
}

func (id PluginID) Equals(other PluginID) bool {
	return id.value == other.value
}

// PluginVersion represents a semantic version
type PluginVersion struct {
	Major int
	Minor int
	Patch int
	Pre   string
}

func NewPluginVersion(major, minor, patch int) PluginVersion {
	return PluginVersion{
		Major: major,
		Minor: minor,
		Patch: patch,
	}
}

func (v PluginVersion) String() string {
	version := fmt.Sprintf("%d.%d.%d", v.Major, v.Minor, v.Patch)
	if v.Pre != "" {
		version += "-" + v.Pre
	}
	return version
}

func (v PluginVersion) IsCompatibleWith(other PluginVersion) bool {
	// Simple compatibility check - same major version
	return v.Major == other.Major
}

// PluginMetadata holds information about a plugin
type PluginMetadata struct {
	ID           PluginID
	Name         string
	Description  string
	Version      PluginVersion
	Author       string
	License      string
	Homepage     string
	Repository   string
	Keywords     []string
	Dependencies []PluginDependency
	Capabilities []string
	MinPlatform  PluginVersion
	CreatedAt    time.Time
}

// PluginDependency represents a plugin dependency
type PluginDependency struct {
	PluginID       PluginID
	Version        PluginVersion
	Optional       bool
	VersionRange   string // e.g., "^1.0.0", ">=1.0.0 <2.0.0"
}

// PluginState represents the current state of a plugin
type PluginState int

const (
	PluginStateUnloaded PluginState = iota
	PluginStateLoading
	PluginStateLoaded
	PluginStateInitializing
	PluginStateActive
	PluginStateDeactivating
	PluginStateError
	PluginStateStopped
)

func (s PluginState) String() string {
	switch s {
	case PluginStateUnloaded:
		return "unloaded"
	case PluginStateLoading:
		return "loading"
	case PluginStateLoaded:
		return "loaded"
	case PluginStateInitializing:
		return "initializing"
	case PluginStateActive:
		return "active"
	case PluginStateDeactivating:
		return "deactivating"
	case PluginStateError:
		return "error"
	case PluginStateStopped:
		return "stopped"
	default:
		return "unknown"
	}
}

// PluginContext provides context and services to plugins
type PluginContext interface {
	// Service access
	GetService(name string) (interface{}, error)
	GetLogger() Logger
	GetEventBus() EventBus
	GetConfiguration() map[string]interface{}
	
	// Plugin communication
	SendMessage(targetPlugin PluginID, message interface{}) error
	BroadcastMessage(message interface{}) error
	
	// Resource management
	GetDataDirectory() string
	GetTempDirectory() string
	GetConfigDirectory() string
	
	// Security context
	GetPermissions() []string
	HasPermission(permission string) bool
	
	// Lifecycle
	GetContext() context.Context
	IsShuttingDown() bool
}

// Logger interface for plugin logging
type Logger interface {
	Debug(msg string, fields ...interface{})
	Info(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Fatal(msg string, fields ...interface{})
}

// EventBus interface for plugin event handling
type EventBus interface {
	Publish(event domain.DomainEvent) error
	Subscribe(handler EventHandler) error
	Unsubscribe(handler EventHandler) error
}

// EventHandler interface for plugin event handling
type EventHandler interface {
	Handle(ctx context.Context, event domain.DomainEvent) error
	CanHandle(eventType string) bool
	HandlerName() string
}

// Plugin is the main interface that all plugins must implement
type Plugin interface {
	// Metadata
	ID() PluginID
	Metadata() PluginMetadata
	
	// Lifecycle
	Initialize(ctx PluginContext) error
	Start() error
	Stop() error
	Cleanup() error
	
	// Health and status
	IsHealthy() bool
	GetStatus() PluginStatus
	GetLastError() error
	
	// Configuration
	GetConfigSchema() ConfigSchema
	ValidateConfig(config map[string]interface{}) error
	UpdateConfig(config map[string]interface{}) error
	
	// Features
	GetCapabilities() []string
	HasCapability(capability string) bool
}

// PluginStatus holds current plugin status information
type PluginStatus struct {
	State       PluginState
	Message     string
	LastUpdate  time.Time
	Uptime      time.Duration
	MemoryUsage int64
	ErrorCount  int
	Metadata    map[string]interface{}
}

// ConfigSchema defines the configuration schema for a plugin
type ConfigSchema struct {
	Properties map[string]PropertySchema
	Required   []string
}

// PropertySchema defines a single configuration property
type PropertySchema struct {
	Type        string      // "string", "number", "boolean", "array", "object"
	Description string
	Default     interface{}
	Enum        []interface{}
	Minimum     *float64
	Maximum     *float64
	Pattern     string
	Items       *PropertySchema // For arrays
	Properties  map[string]PropertySchema // For objects
}

// Specialized plugin interfaces

// ContentPlugin handles content loading and transformation
type ContentPlugin interface {
	Plugin
	
	// Content handling
	GetSupportedSources() []domain.SourceType
	CanHandleSource(source domain.ContentSource) bool
	LoadContent(ctx context.Context, source domain.ContentSource) (*domain.Content, error)
	
	// Content transformation
	GetSupportedTransformations() []string
	CanTransform(content *domain.Content, transformation string) bool
	TransformContent(ctx context.Context, content *domain.Content, transformation string, options map[string]interface{}) (*domain.Content, error)
}

// UIPlugin provides user interface components
type UIPlugin interface {
	Plugin
	
	// Component management
	GetComponents() []ComponentDefinition
	CreateComponent(componentType string, config map[string]interface{}) (UIComponent, error)
	
	// Event handling
	GetEventHandlers() []UIEventHandler
	
	// Styling
	GetStyles() []StyleDefinition
	GetThemes() []ThemeDefinition
}

// ComponentDefinition defines a UI component
type ComponentDefinition struct {
	Type        string
	Name        string
	Description string
	ConfigSchema ConfigSchema
	Events      []string
	Slots       []string
}

// UIComponent represents a UI component instance
type UIComponent interface {
	ID() string
	Type() string
	Render(ctx context.Context) (string, error)
	HandleEvent(event UIEvent) error
	UpdateConfig(config map[string]interface{}) error
	GetState() map[string]interface{}
	SetState(state map[string]interface{}) error
}

// UIEvent represents a UI event
type UIEvent struct {
	Type      string
	Source    string
	Target    string
	Data      map[string]interface{}
	Timestamp time.Time
}

// UIEventHandler handles UI events
type UIEventHandler interface {
	CanHandle(eventType string) bool
	Handle(ctx context.Context, event UIEvent) error
}

// StyleDefinition defines CSS styles
type StyleDefinition struct {
	Name     string
	Selector string
	Rules    map[string]string
}

// ThemeDefinition defines a complete theme
type ThemeDefinition struct {
	Name        string
	Description string
	Colors      map[string]string
	Fonts       map[string]string
	Sizes       map[string]string
	Styles      []StyleDefinition
}

// CommandPlugin provides command-line commands
type CommandPlugin interface {
	Plugin
	
	// Command management
	GetCommands() []CommandDefinition
	ExecuteCommand(ctx context.Context, command string, args []string, options map[string]interface{}) (CommandResult, error)
	
	// Auto-completion
	GetCompletions(ctx context.Context, command string, args []string, position int) ([]Completion, error)
}

// CommandDefinition defines a command
type CommandDefinition struct {
	Name        string
	Description string
	Usage       string
	Arguments   []ArgumentDefinition
	Options     []OptionDefinition
	Examples    []string
}

// ArgumentDefinition defines a command argument
type ArgumentDefinition struct {
	Name        string
	Description string
	Type        string
	Required    bool
	Multiple    bool
}

// OptionDefinition defines a command option
type OptionDefinition struct {
	Name        string
	Short       string
	Description string
	Type        string
	Default     interface{}
	Required    bool
}

// CommandResult represents the result of command execution
type CommandResult struct {
	Success   bool
	Output    string
	Error     string
	ExitCode  int
	Duration  time.Duration
	Metadata  map[string]interface{}
}

// Completion represents an auto-completion suggestion
type Completion struct {
	Text        string
	Description string
	Type        string // "command", "argument", "option", "file", "directory"
}

// DataPlugin provides data access and storage
type DataPlugin interface {
	Plugin
	
	// Data sources
	GetSupportedDataSources() []string
	CanHandleDataSource(source string) bool
	ConnectToDataSource(ctx context.Context, source string, config map[string]interface{}) (DataConnection, error)
	
	// Query capabilities
	GetSupportedQueryLanguages() []string
	ExecuteQuery(ctx context.Context, connection DataConnection, query string, params map[string]interface{}) (QueryResult, error)
}

// DataConnection represents a connection to a data source
type DataConnection interface {
	ID() string
	Source() string
	IsConnected() bool
	Close() error
	GetSchema() (Schema, error)
	BeginTransaction() (Transaction, error)
}

// Schema represents a data schema
type Schema struct {
	Tables  []TableSchema
	Views   []ViewSchema
	Indexes []IndexSchema
}

// TableSchema represents a table schema
type TableSchema struct {
	Name    string
	Columns []ColumnSchema
}

// ColumnSchema represents a column schema
type ColumnSchema struct {
	Name     string
	Type     string
	Nullable bool
	Default  interface{}
}

// ViewSchema represents a view schema
type ViewSchema struct {
	Name       string
	Definition string
	Columns    []ColumnSchema
}

// IndexSchema represents an index schema
type IndexSchema struct {
	Name    string
	Table   string
	Columns []string
	Unique  bool
}

// Transaction represents a database transaction
type Transaction interface {
	Commit() error
	Rollback() error
	IsActive() bool
}

// QueryResult represents the result of a query
type QueryResult struct {
	Columns []string
	Rows    [][]interface{}
	Count   int64
	Duration time.Duration
	Metadata map[string]interface{}
}

// SecurityPlugin provides security features
type SecurityPlugin interface {
	Plugin
	
	// Authentication
	GetAuthenticationMethods() []string
	Authenticate(ctx context.Context, method string, credentials map[string]interface{}) (AuthResult, error)
	
	// Authorization
	GetPermissions(ctx context.Context, user string) ([]string, error)
	CheckPermission(ctx context.Context, user string, permission string) (bool, error)
	
	// Encryption
	Encrypt(ctx context.Context, data []byte, options map[string]interface{}) ([]byte, error)
	Decrypt(ctx context.Context, data []byte, options map[string]interface{}) ([]byte, error)
}

// AuthResult represents authentication result
type AuthResult struct {
	Success     bool
	User        UserInfo
	Token       string
	ExpiresAt   time.Time
	Permissions []string
	Metadata    map[string]interface{}
}

// UserInfo represents user information
type UserInfo struct {
	ID       string
	Username string
	Email    string
	Name     string
	Groups   []string
	Metadata map[string]interface{}
}
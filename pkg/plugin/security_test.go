package plugin

import (
	"testing"
	"time"
)

func TestPermissionSet(t *testing.T) {
	ps := NewPermissionSet()
	
	// Test adding permissions
	permission := &Permission{
		ID:          "test_permission",
		Name:        "Test Permission",
		Description: "A test permission",
		Category:    "test",
		Level:       SecurityLevelBasic,
		Dangerous:   false,
	}
	
	ps.Add(permission)
	
	// Test has permission
	if !ps.Has("test_permission") {
		t.<PERSON>r("Permission set should contain the added permission")
	}
	
	// Test get permission
	retrieved, exists := ps.Get("test_permission")
	if !exists {
		t.Error("Permission should exist")
	}
	
	if retrieved.ID != permission.ID {
		t.Errorf("Expected permission ID %s, got %s", permission.ID, retrieved.ID)
	}
	
	// Test list permissions
	permissions := ps.List()
	if len(permissions) != 1 {
		t.<PERSON><PERSON>("Expected 1 permission, got %d", len(permissions))
	}
	
	// Test remove permission
	ps.Remove("test_permission")
	if ps.Has("test_permission") {
		t.<PERSON><PERSON>("Permission should be removed")
	}
}

func TestPermissionSetByCategory(t *testing.T) {
	ps := NewPermissionSet()
	
	// Add permissions with different categories
	ps.Add(&Permission{ID: "fs1", Category: "filesystem"})
	ps.Add(&Permission{ID: "fs2", Category: "filesystem"})
	ps.Add(&Permission{ID: "net1", Category: "network"})
	
	// Test list by category
	fsPermissions := ps.ListByCategory("filesystem")
	if len(fsPermissions) != 2 {
		t.Errorf("Expected 2 filesystem permissions, got %d", len(fsPermissions))
	}
	
	netPermissions := ps.ListByCategory("network")
	if len(netPermissions) != 1 {
		t.Errorf("Expected 1 network permission, got %d", len(netPermissions))
	}
}

func TestPermissionSetClone(t *testing.T) {
	ps := NewPermissionSet()
	permission := &Permission{ID: "test", Name: "Test"}
	ps.Add(permission)
	
	// Clone permission set
	cloned := ps.Clone()
	
	// Verify clone has the permission
	if !cloned.Has("test") {
		t.Error("Cloned permission set should contain the permission")
	}
	
	// Modify original
	ps.Remove("test")
	
	// Verify clone is unaffected
	if !cloned.Has("test") {
		t.Error("Cloned permission set should be independent of original")
	}
}

func TestSecurityContext(t *testing.T) {
	ctx := NewSecurityContext("user123", "session456")
	
	// Test initial state
	if !ctx.IsValid() {
		t.Error("New security context should be valid")
	}
	
	if ctx.UserID != "user123" {
		t.Errorf("Expected user ID user123, got %s", ctx.UserID)
	}
	
	if ctx.SessionID != "session456" {
		t.Errorf("Expected session ID session456, got %s", ctx.SessionID)
	}
	
	// Test permission management
	permission := &Permission{
		ID:    "test_permission",
		Level: SecurityLevelBasic,
	}
	
	err := ctx.AddPermission(permission)
	if err != nil {
		t.Errorf("Failed to add permission: %v", err)
	}
	
	if !ctx.HasPermission("test_permission") {
		t.Error("Security context should have the added permission")
	}
	
	// Test permission level restriction
	highLevelPermission := &Permission{
		ID:    "high_level",
		Level: SecurityLevelMaximum,
	}
	
	err = ctx.AddPermission(highLevelPermission)
	if err == nil {
		t.Error("Should not be able to add permission with higher level than security context")
	}
	
	// Test metadata
	ctx.SetMetadata("key", "value")
	value, exists := ctx.GetMetadata("key")
	if !exists {
		t.Error("Metadata should exist")
	}
	
	if value != "value" {
		t.Errorf("Expected metadata value 'value', got %v", value)
	}
}

func TestSecurityContextExpiration(t *testing.T) {
	ctx := NewSecurityContext("user", "session")
	
	// Set expiration to past
	ctx.ExpiresAt = time.Now().Add(-1 * time.Hour)
	
	if ctx.IsValid() {
		t.Error("Expired security context should not be valid")
	}
	
	// Test extension
	ctx.Extend(2 * time.Hour)
	
	if !ctx.IsValid() {
		t.Error("Extended security context should be valid")
	}
}

func TestSecurityContextClone(t *testing.T) {
	ctx := NewSecurityContext("user", "session")
	ctx.SetMetadata("key", "value")
	ctx.AddPermission(&Permission{ID: "test", Level: SecurityLevelBasic})
	
	// Clone context
	cloned := ctx.Clone()
	
	// Verify clone properties
	if cloned.UserID != ctx.UserID {
		t.Error("Cloned context should have same user ID")
	}
	
	if !cloned.HasPermission("test") {
		t.Error("Cloned context should have same permissions")
	}
	
	value, exists := cloned.GetMetadata("key")
	if !exists || value != "value" {
		t.Error("Cloned context should have same metadata")
	}
	
	// Modify original
	ctx.SetMetadata("key", "modified")
	
	// Verify clone is unaffected
	value, _ = cloned.GetMetadata("key")
	if value != "value" {
		t.Error("Cloned context should be independent of original")
	}
}

func TestSecurePluginContext(t *testing.T) {
	secCtx := NewSecurityContext("user", "session")
	pluginCtx := NewSecurePluginContext("plugin1", "Test Plugin", "1.0.0", secCtx)
	
	// Test initial state
	if !pluginCtx.IsValid() {
		t.Error("New plugin context should be valid")
	}
	
	if pluginCtx.PluginID != "plugin1" {
		t.Errorf("Expected plugin ID plugin1, got %s", pluginCtx.PluginID)
	}
	
	// Test config management
	pluginCtx.SetConfig("key", "value")
	value, exists := pluginCtx.GetConfig("key")
	if !exists {
		t.Error("Config should exist")
	}
	
	if value != "value" {
		t.Errorf("Expected config value 'value', got %v", value)
	}
	
	// Test state management
	pluginCtx.SetState("state_key", "state_value")
	stateValue, exists := pluginCtx.GetState("state_key")
	if !exists {
		t.Error("State should exist")
	}
	
	if stateValue != "state_value" {
		t.Errorf("Expected state value 'state_value', got %v", stateValue)
	}
	
	// Test clear state
	pluginCtx.ClearState()
	_, exists = pluginCtx.GetState("state_key")
	if exists {
		t.Error("State should be cleared")
	}
}

func TestSecurePluginContextActivity(t *testing.T) {
	secCtx := NewSecurityContext("user", "session")
	pluginCtx := NewSecurePluginContext("plugin1", "Test Plugin", "1.0.0", secCtx)
	
	initialActivity := pluginCtx.GetLastActivity()
	
	// Wait a bit and update activity
	time.Sleep(10 * time.Millisecond)
	pluginCtx.UpdateActivity()
	
	newActivity := pluginCtx.GetLastActivity()
	if !newActivity.After(initialActivity) {
		t.Error("Activity should be updated")
	}
}

func TestSecurePluginContextWithTimeout(t *testing.T) {
	secCtx := NewSecurityContext("user", "session")
	pluginCtx := NewSecurePluginContext("plugin1", "Test Plugin", "1.0.0", secCtx)
	
	// Test with timeout
	_, cancel := pluginCtx.WithTimeout(100 * time.Millisecond)
	defer cancel()
	
	// Verify context has deadline
	deadline, ok := pluginCtx.ExecutionCtx.Deadline()
	if !ok {
		t.Error("Context should have deadline")
	}
	
	if time.Until(deadline) > 100*time.Millisecond {
		t.Error("Deadline should be within timeout duration")
	}
}

func TestSecurePluginContextInvalidSecurity(t *testing.T) {
	// Test with nil security context
	pluginCtx := NewSecurePluginContext("plugin1", "Test Plugin", "1.0.0", nil)
	
	if pluginCtx.IsValid() {
		t.Error("Plugin context with nil security context should not be valid")
	}
	
	// Test with invalid security context
	secCtx := NewSecurityContext("", "")
	pluginCtx = NewSecurePluginContext("plugin1", "Test Plugin", "1.0.0", secCtx)
	
	if pluginCtx.IsValid() {
		t.Error("Plugin context with invalid security context should not be valid")
	}
}

func TestSecurityManager(t *testing.T) {
	sm := NewSecurityManager()
	
	// Test security context creation
	secCtx := sm.CreateSecurityContext("user123", "session456")
	if secCtx == nil {
		t.Error("Security context should be created")
	}
	
	// Test retrieving security context
	retrieved := sm.GetSecurityContext("user123", "session456")
	if retrieved != secCtx {
		t.Error("Should retrieve the same security context")
	}
	
	// Test plugin context creation
	pluginCtx := sm.CreatePluginContext("plugin1", "Test Plugin", "1.0.0", secCtx)
	if pluginCtx == nil {
		t.Error("Plugin context should be created")
	}
	
	// Test retrieving plugin context
	retrievedPlugin := sm.GetPluginContext("plugin1")
	if retrievedPlugin != pluginCtx {
		t.Error("Should retrieve the same plugin context")
	}
	
	// Test validation
	err := sm.ValidatePluginExecution("plugin1", []string{})
	if err != nil {
		t.Errorf("Validation should succeed: %v", err)
	}
	
	// Test validation with required permission
	err = sm.ValidatePluginExecution("plugin1", []string{"nonexistent_permission"})
	if err == nil {
		t.Error("Validation should fail for missing permission")
	}
}

func TestSecurityManagerDefaultPermissions(t *testing.T) {
	sm := NewSecurityManager()
	
	// Add default permission
	defaultPerm := &Permission{
		ID:    "default_test",
		Level: SecurityLevelBasic,
	}
	sm.AddDefaultPermission(defaultPerm)
	
	// Create security context
	secCtx := sm.CreateSecurityContext("user", "session")
	
	// Should have default permission
	if !secCtx.HasPermission("default_test") {
		t.Error("Security context should have default permission")
	}
	
	// Remove default permission
	sm.RemoveDefaultPermission("default_test")
	
	// Create new context
	secCtx2 := sm.CreateSecurityContext("user2", "session2")
	
	// Should not have the removed default permission
	if secCtx2.HasPermission("default_test") {
		t.Error("New security context should not have removed default permission")
	}
}

func TestSecurityManagerCleanup(t *testing.T) {
	sm := NewSecurityManager()
	
	// Create contexts
	secCtx := sm.CreateSecurityContext("user", "session")
	_ = sm.CreatePluginContext("plugin1", "Test Plugin", "1.0.0", secCtx)
	
	// Expire security context
	secCtx.ExpiresAt = time.Now().Add(-1 * time.Hour)
	
	// Run cleanup
	sm.CleanupExpiredContexts()
	
	// Contexts should be removed
	retrievedSec := sm.GetSecurityContext("user", "session")
	if retrievedSec != nil {
		t.Error("Expired security context should be removed")
	}
	
	retrievedPlugin := sm.GetPluginContext("plugin1")
	if retrievedPlugin != nil {
		t.Error("Plugin context with expired security context should be removed")
	}
}

func TestSecurityManagerStats(t *testing.T) {
	sm := NewSecurityManager()
	
	// Add default permission
	sm.AddDefaultPermission(&Permission{ID: "test", Level: SecurityLevelBasic})
	
	// Create contexts
	secCtx := sm.CreateSecurityContext("user", "session")
	sm.CreatePluginContext("plugin1", "Test Plugin", "1.0.0", secCtx)
	
	// Get stats
	stats := sm.GetStats()
	
	securityContexts, ok := stats["active_security_contexts"].(int)
	if !ok || securityContexts != 1 {
		t.Errorf("Expected 1 active security context, got %v", stats["active_security_contexts"])
	}
	
	pluginContexts, ok := stats["active_plugin_contexts"].(int)
	if !ok || pluginContexts != 1 {
		t.Errorf("Expected 1 active plugin context, got %v", stats["active_plugin_contexts"])
	}
	
	defaultPerms, ok := stats["default_permissions"].(int)
	if !ok || defaultPerms != 1 {
		t.Errorf("Expected 1 default permission, got %v", stats["default_permissions"])
	}
}

func TestPredefinedPermissions(t *testing.T) {
	// Test that predefined permissions are properly configured
	permissions := []*Permission{
		PermissionReadFiles,
		PermissionWriteFiles,
		PermissionNetworkAccess,
		PermissionExecuteCommands,
		PermissionAccessDatabase,
		PermissionModifyUI,
	}
	
	for _, perm := range permissions {
		if perm.ID == "" {
			t.Error("Permission ID should not be empty")
		}
		
		if perm.Name == "" {
			t.Error("Permission name should not be empty")
		}
		
		if perm.Category == "" {
			t.Error("Permission category should not be empty")
		}
	}
	
	// Test dangerous permissions
	if !PermissionWriteFiles.Dangerous {
		t.Error("Write files permission should be marked as dangerous")
	}
	
	if !PermissionExecuteCommands.Dangerous {
		t.Error("Execute commands permission should be marked as dangerous")
	}
}

func TestDefaultPermissionSets(t *testing.T) {
	// Test default permissions
	defaultPerms := DefaultPermissions()
	if !defaultPerms.Has("read_files") {
		t.Error("Default permissions should include read files")
	}
	
	if defaultPerms.Has("execute_commands") {
		t.Error("Default permissions should not include execute commands")
	}
	
	// Test strict permissions
	strictPerms := StrictPermissions()
	if !strictPerms.Has("write_files") {
		t.Error("Strict permissions should include write files")
	}
	
	if strictPerms.Has("execute_commands") {
		t.Error("Strict permissions should not include execute commands")
	}
	
	// Test maximum permissions
	maxPerms := MaximumPermissions()
	if !maxPerms.Has("execute_commands") {
		t.Error("Maximum permissions should include execute commands")
	}
}

func TestSecurePluginContextClone(t *testing.T) {
	secCtx := NewSecurityContext("user", "session")
	pluginCtx := NewSecurePluginContext("plugin1", "Test Plugin", "1.0.0", secCtx)
	
	// Set some config and state
	pluginCtx.SetConfig("config_key", "config_value")
	pluginCtx.SetState("state_key", "state_value")
	
	// Clone context
	cloned := pluginCtx.Clone()
	
	// Verify clone properties
	if cloned.PluginID != pluginCtx.PluginID {
		t.Error("Cloned context should have same plugin ID")
	}
	
	// Verify config is cloned
	value, exists := cloned.GetConfig("config_key")
	if !exists || value != "config_value" {
		t.Error("Cloned context should have same config")
	}
	
	// Verify state is cloned
	stateValue, exists := cloned.GetState("state_key")
	if !exists || stateValue != "state_value" {
		t.Error("Cloned context should have same state")
	}
	
	// Modify original
	pluginCtx.SetConfig("config_key", "modified")
	
	// Verify clone is unaffected
	value, _ = cloned.GetConfig("config_key")
	if value != "config_value" {
		t.Error("Cloned context should be independent of original")
	}
}
// Package plugin provides security and context management for plugins
package plugin

import (
	"context"
	"fmt"
	"sync"
	"time"

	"eionz.com/demo/pkg/domain"
)

// SecurityLevel defines the security level for plugin execution
type SecurityLevel int

const (
	SecurityLevelMinimal SecurityLevel = iota
	SecurityLevelBasic
	SecurityLevelStrict
	SecurityLevelMaximum
)

// Permission represents a specific permission that can be granted to a plugin
type Permission struct {
	ID          string
	Name        string
	Description string
	Category    string
	Level       SecurityLevel
	Dangerous   bool
}

// PermissionSet manages a collection of permissions
type PermissionSet struct {
	permissions map[string]*Permission
	mutex       sync.RWMutex
}

func NewPermissionSet() *PermissionSet {
	return &PermissionSet{
		permissions: make(map[string]*Permission),
	}
}

func (ps *PermissionSet) Add(permission *Permission) {
	ps.mutex.Lock()
	defer ps.mutex.Unlock()
	ps.permissions[permission.ID] = permission
}

func (ps *PermissionSet) Remove(permissionID string) {
	ps.mutex.Lock()
	defer ps.mutex.Unlock()
	delete(ps.permissions, permissionID)
}

func (ps *PermissionSet) Has(permissionID string) bool {
	ps.mutex.RLock()
	defer ps.mutex.RUnlock()
	_, exists := ps.permissions[permissionID]
	return exists
}

func (ps *PermissionSet) Get(permissionID string) (*Permission, bool) {
	ps.mutex.RLock()
	defer ps.mutex.RUnlock()
	permission, exists := ps.permissions[permissionID]
	return permission, exists
}

func (ps *PermissionSet) List() []*Permission {
	ps.mutex.RLock()
	defer ps.mutex.RUnlock()
	
	permissions := make([]*Permission, 0, len(ps.permissions))
	for _, permission := range ps.permissions {
		permissions = append(permissions, permission)
	}
	
	return permissions
}

func (ps *PermissionSet) ListByCategory(category string) []*Permission {
	ps.mutex.RLock()
	defer ps.mutex.RUnlock()
	
	var permissions []*Permission
	for _, permission := range ps.permissions {
		if permission.Category == category {
			permissions = append(permissions, permission)
		}
	}
	
	return permissions
}

func (ps *PermissionSet) Clone() *PermissionSet {
	ps.mutex.RLock()
	defer ps.mutex.RUnlock()
	
	newSet := NewPermissionSet()
	for id, permission := range ps.permissions {
		newSet.permissions[id] = &Permission{
			ID:          permission.ID,
			Name:        permission.Name,
			Description: permission.Description,
			Category:    permission.Category,
			Level:       permission.Level,
			Dangerous:   permission.Dangerous,
		}
	}
	
	return newSet
}

// SecurityContext provides security context for plugin execution
type SecurityContext struct {
	UserID         string
	SessionID      string
	PermissionSet  *PermissionSet
	SecurityLevel  SecurityLevel
	IPAddress      string
	UserAgent      string
	RequestID      string
	CreatedAt      time.Time
	ExpiresAt      time.Time
	Metadata       map[string]interface{}
	mutex          sync.RWMutex
}

func NewSecurityContext(userID, sessionID string) *SecurityContext {
	return &SecurityContext{
		UserID:        userID,
		SessionID:     sessionID,
		PermissionSet: NewPermissionSet(),
		SecurityLevel: SecurityLevelBasic,
		CreatedAt:     time.Now(),
		ExpiresAt:     time.Now().Add(24 * time.Hour),
		Metadata:      make(map[string]interface{}),
	}
}

func (sc *SecurityContext) IsValid() bool {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()
	
	if sc.UserID == "" || sc.SessionID == "" {
		return false
	}
	
	if time.Now().After(sc.ExpiresAt) {
		return false
	}
	
	return true
}

func (sc *SecurityContext) HasPermission(permissionID string) bool {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()
	
	return sc.PermissionSet.Has(permissionID)
}

func (sc *SecurityContext) AddPermission(permission *Permission) error {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()
	
	if permission.Level > sc.SecurityLevel {
		return fmt.Errorf("permission level %d exceeds security context level %d", permission.Level, sc.SecurityLevel)
	}
	
	sc.PermissionSet.Add(permission)
	return nil
}

func (sc *SecurityContext) RemovePermission(permissionID string) {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()
	
	sc.PermissionSet.Remove(permissionID)
}

func (sc *SecurityContext) SetSecurityLevel(level SecurityLevel) {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()
	
	sc.SecurityLevel = level
}

func (sc *SecurityContext) SetMetadata(key string, value interface{}) {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()
	
	sc.Metadata[key] = value
}

func (sc *SecurityContext) GetMetadata(key string) (interface{}, bool) {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()
	
	value, exists := sc.Metadata[key]
	return value, exists
}

func (sc *SecurityContext) Extend(duration time.Duration) {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()
	
	sc.ExpiresAt = sc.ExpiresAt.Add(duration)
}

func (sc *SecurityContext) Clone() *SecurityContext {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()
	
	newContext := &SecurityContext{
		UserID:        sc.UserID,
		SessionID:     sc.SessionID,
		PermissionSet: sc.PermissionSet.Clone(),
		SecurityLevel: sc.SecurityLevel,
		IPAddress:     sc.IPAddress,
		UserAgent:     sc.UserAgent,
		RequestID:     sc.RequestID,
		CreatedAt:     sc.CreatedAt,
		ExpiresAt:     sc.ExpiresAt,
		Metadata:      make(map[string]interface{}),
	}
	
	// Copy metadata
	for k, v := range sc.Metadata {
		newContext.Metadata[k] = v
	}
	
	return newContext
}

// SecurePluginContext provides execution context for plugins with security features
type SecurePluginContext struct {
	PluginID       string
	PluginName     string
	Version        string
	SecurityCtx    *SecurityContext
	ExecutionCtx   context.Context
	Logger         interface{} // Use interface{} for now, will be typed properly later
	Config         map[string]interface{}
	State          map[string]interface{}
	CreatedAt      time.Time
	LastActivity   time.Time
	mutex          sync.RWMutex
}

func NewSecurePluginContext(pluginID, pluginName, version string, securityCtx *SecurityContext) *SecurePluginContext {
	return &SecurePluginContext{
		PluginID:     pluginID,
		PluginName:   pluginName,
		Version:      version,
		SecurityCtx:  securityCtx,
		ExecutionCtx: context.Background(),
		Config:       make(map[string]interface{}),
		State:        make(map[string]interface{}),
		CreatedAt:    time.Now(),
		LastActivity: time.Now(),
	}
}

func (pc *SecurePluginContext) IsValid() bool {
	pc.mutex.RLock()
	defer pc.mutex.RUnlock()
	
	if pc.PluginID == "" || pc.PluginName == "" {
		return false
	}
	
	if pc.SecurityCtx == nil || !pc.SecurityCtx.IsValid() {
		return false
	}
	
	return true
}

func (pc *SecurePluginContext) SetLogger(logger interface{}) {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()
	
	pc.Logger = logger
}

func (pc *SecurePluginContext) SetConfig(key string, value interface{}) {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()
	
	pc.Config[key] = value
	pc.LastActivity = time.Now()
}

func (pc *SecurePluginContext) GetConfig(key string) (interface{}, bool) {
	pc.mutex.RLock()
	defer pc.mutex.RUnlock()
	
	value, exists := pc.Config[key]
	return value, exists
}

func (pc *SecurePluginContext) SetState(key string, value interface{}) {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()
	
	pc.State[key] = value
	pc.LastActivity = time.Now()
}

func (pc *SecurePluginContext) GetState(key string) (interface{}, bool) {
	pc.mutex.RLock()
	defer pc.mutex.RUnlock()
	
	value, exists := pc.State[key]
	return value, exists
}

func (pc *SecurePluginContext) ClearState() {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()
	
	pc.State = make(map[string]interface{})
	pc.LastActivity = time.Now()
}

func (pc *SecurePluginContext) UpdateActivity() {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()
	
	pc.LastActivity = time.Now()
}

func (pc *SecurePluginContext) GetLastActivity() time.Time {
	pc.mutex.RLock()
	defer pc.mutex.RUnlock()
	
	return pc.LastActivity
}

func (pc *SecurePluginContext) WithContext(ctx context.Context) *SecurePluginContext {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()
	
	pc.ExecutionCtx = ctx
	return pc
}

func (pc *SecurePluginContext) WithTimeout(timeout time.Duration) (*SecurePluginContext, context.CancelFunc) {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()
	
	ctx, cancel := context.WithTimeout(pc.ExecutionCtx, timeout)
	pc.ExecutionCtx = ctx
	return pc, cancel
}

func (pc *SecurePluginContext) Clone() *SecurePluginContext {
	pc.mutex.RLock()
	defer pc.mutex.RUnlock()
	
	newContext := &SecurePluginContext{
		PluginID:     pc.PluginID,
		PluginName:   pc.PluginName,
		Version:      pc.Version,
		SecurityCtx:  pc.SecurityCtx.Clone(),
		ExecutionCtx: pc.ExecutionCtx,
		Logger:       pc.Logger,
		Config:       make(map[string]interface{}),
		State:        make(map[string]interface{}),
		CreatedAt:    pc.CreatedAt,
		LastActivity: pc.LastActivity,
	}
	
	// Copy config
	for k, v := range pc.Config {
		newContext.Config[k] = v
	}
	
	// Copy state
	for k, v := range pc.State {
		newContext.State[k] = v
	}
	
	return newContext
}

// PluginContext interface implementation

func (pc *SecurePluginContext) GetService(name string) (interface{}, error) {
	// Implementation would depend on available services
	return nil, fmt.Errorf("service not found: %s", name)
}

func (pc *SecurePluginContext) GetLogger() Logger {
	// Return a basic logger interface
	return &basicLogger{}
}

func (pc *SecurePluginContext) GetEventBus() EventBus {
	// Return a basic event bus interface
	return &basicEventBus{}
}

func (pc *SecurePluginContext) GetConfiguration() map[string]interface{} {
	pc.mutex.RLock()
	defer pc.mutex.RUnlock()
	
	// Return a copy of the configuration
	result := make(map[string]interface{})
	for k, v := range pc.Config {
		result[k] = v
	}
	return result
}

func (pc *SecurePluginContext) SendMessage(targetPlugin PluginID, message interface{}) error {
	// For now, return not implemented
	return fmt.Errorf("message sending not implemented")
}

func (pc *SecurePluginContext) BroadcastMessage(message interface{}) error {
	// For now, return not implemented
	return fmt.Errorf("message broadcasting not implemented")
}

func (pc *SecurePluginContext) GetConfigDirectory() string {
	// Return a default config directory for plugins
	return "./config/plugins"
}

func (pc *SecurePluginContext) GetDataDirectory() string {
	// Return a default data directory for plugins
	return "./data/plugins"
}

func (pc *SecurePluginContext) GetTempDirectory() string {
	// Return a default temp directory for plugins
	return "./tmp/plugins"
}

func (pc *SecurePluginContext) GetPermissions() []string {
	if pc.SecurityCtx == nil {
		return []string{}
	}
	
	permissions := make([]string, 0)
	for perm := range pc.SecurityCtx.PermissionSet.permissions {
		permissions = append(permissions, perm)
	}
	return permissions
}

func (pc *SecurePluginContext) HasPermission(permission string) bool {
	if pc.SecurityCtx == nil || pc.SecurityCtx.PermissionSet == nil {
		return false
	}
	// Check if permission exists in the permission map
	_, exists := pc.SecurityCtx.PermissionSet.permissions[permission]
	return exists
}

func (pc *SecurePluginContext) GetContext() context.Context {
	pc.mutex.RLock()
	defer pc.mutex.RUnlock()
	
	if pc.ExecutionCtx == nil {
		return context.Background()
	}
	return pc.ExecutionCtx
}

func (pc *SecurePluginContext) IsShuttingDown() bool {
	// For now, return false - this would be set during shutdown
	return false
}

// Basic implementations for interfaces

type basicLogger struct{}

func (l *basicLogger) Debug(msg string, fields ...interface{}) {}
func (l *basicLogger) Info(msg string, fields ...interface{})  {}
func (l *basicLogger) Warn(msg string, fields ...interface{})  {}
func (l *basicLogger) Error(msg string, fields ...interface{}) {}
func (l *basicLogger) Fatal(msg string, fields ...interface{}) {}

type basicEventBus struct{}

func (eb *basicEventBus) Publish(event domain.DomainEvent) error {
	return fmt.Errorf("event publishing not implemented")
}

func (eb *basicEventBus) Subscribe(handler EventHandler) error {
	return fmt.Errorf("event subscription not implemented")
}

func (eb *basicEventBus) Unsubscribe(handler EventHandler) error {
	return fmt.Errorf("event unsubscription not implemented")
}

// SecurityManager manages plugin security contexts and permissions
type SecurityManager struct {
	contexts           map[string]*SecurityContext
	pluginContexts     map[string]*SecurePluginContext
	defaultPermissions *PermissionSet
	mutex              sync.RWMutex
}

func NewSecurityManager() *SecurityManager {
	return &SecurityManager{
		contexts:           make(map[string]*SecurityContext),
		pluginContexts:     make(map[string]*SecurePluginContext),
		defaultPermissions: NewPermissionSet(),
	}
}

func (sm *SecurityManager) CreateSecurityContext(userID, sessionID string) *SecurityContext {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	ctx := NewSecurityContext(userID, sessionID)
	
	// Add default permissions
	for _, permission := range sm.defaultPermissions.List() {
		ctx.AddPermission(permission)
	}
	
	contextID := fmt.Sprintf("%s:%s", userID, sessionID)
	sm.contexts[contextID] = ctx
	
	return ctx
}

func (sm *SecurityManager) GetSecurityContext(userID, sessionID string) *SecurityContext {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	contextID := fmt.Sprintf("%s:%s", userID, sessionID)
	return sm.contexts[contextID]
}

func (sm *SecurityManager) RemoveSecurityContext(userID, sessionID string) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	contextID := fmt.Sprintf("%s:%s", userID, sessionID)
	delete(sm.contexts, contextID)
}

func (sm *SecurityManager) CreatePluginContext(pluginID, pluginName, version string, securityCtx *SecurityContext) *SecurePluginContext {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	ctx := NewSecurePluginContext(pluginID, pluginName, version, securityCtx)
	sm.pluginContexts[pluginID] = ctx
	
	return ctx
}

func (sm *SecurityManager) GetPluginContext(pluginID string) *SecurePluginContext {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	return sm.pluginContexts[pluginID]
}

func (sm *SecurityManager) RemovePluginContext(pluginID string) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	delete(sm.pluginContexts, pluginID)
}

func (sm *SecurityManager) AddDefaultPermission(permission *Permission) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	sm.defaultPermissions.Add(permission)
}

func (sm *SecurityManager) RemoveDefaultPermission(permissionID string) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	sm.defaultPermissions.Remove(permissionID)
}

func (sm *SecurityManager) ValidatePluginExecution(pluginID string, requiredPermissions []string) error {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	ctx, exists := sm.pluginContexts[pluginID]
	if !exists {
		return fmt.Errorf("plugin context not found for plugin %s", pluginID)
	}
	
	if !ctx.IsValid() {
		return fmt.Errorf("plugin context is not valid")
	}
	
	// Check required permissions
	for _, permissionID := range requiredPermissions {
		if !ctx.SecurityCtx.HasPermission(permissionID) {
			return fmt.Errorf("plugin %s does not have required permission: %s", pluginID, permissionID)
		}
	}
	
	return nil
}

func (sm *SecurityManager) CleanupExpiredContexts() {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	// Clean up expired security contexts
	for contextID, ctx := range sm.contexts {
		if !ctx.IsValid() {
			delete(sm.contexts, contextID)
		}
	}
	
	// Clean up plugin contexts with invalid security contexts
	for pluginID, ctx := range sm.pluginContexts {
		if !ctx.IsValid() {
			delete(sm.pluginContexts, pluginID)
		}
	}
}

func (sm *SecurityManager) GetStats() map[string]interface{} {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	return map[string]interface{}{
		"active_security_contexts": len(sm.contexts),
		"active_plugin_contexts":   len(sm.pluginContexts),
		"default_permissions":      len(sm.defaultPermissions.permissions),
	}
}

// Predefined permissions
var (
	PermissionReadFiles = &Permission{
		ID:          "read_files",
		Name:        "Read Files",
		Description: "Allow reading files from the file system",
		Category:    "filesystem",
		Level:       SecurityLevelBasic,
		Dangerous:   false,
	}
	
	PermissionWriteFiles = &Permission{
		ID:          "write_files",
		Name:        "Write Files",
		Description: "Allow writing files to the file system",
		Category:    "filesystem",
		Level:       SecurityLevelStrict,
		Dangerous:   true,
	}
	
	PermissionNetworkAccess = &Permission{
		ID:          "network_access",
		Name:        "Network Access",
		Description: "Allow network communication",
		Category:    "network",
		Level:       SecurityLevelBasic,
		Dangerous:   false,
	}
	
	PermissionExecuteCommands = &Permission{
		ID:          "execute_commands",
		Name:        "Execute Commands",
		Description: "Allow executing system commands",
		Category:    "system",
		Level:       SecurityLevelMaximum,
		Dangerous:   true,
	}
	
	PermissionAccessDatabase = &Permission{
		ID:          "access_database",
		Name:        "Access Database",
		Description: "Allow accessing database resources",
		Category:    "database",
		Level:       SecurityLevelStrict,
		Dangerous:   false,
	}
	
	PermissionModifyUI = &Permission{
		ID:          "modify_ui",
		Name:        "Modify UI",
		Description: "Allow modifying user interface elements",
		Category:    "ui",
		Level:       SecurityLevelBasic,
		Dangerous:   false,
	}
)

// DefaultPermissions returns a set of default permissions for new contexts
func DefaultPermissions() *PermissionSet {
	ps := NewPermissionSet()
	ps.Add(PermissionReadFiles)
	ps.Add(PermissionNetworkAccess)
	ps.Add(PermissionModifyUI)
	return ps
}

// StrictPermissions returns a set of strict permissions for high-security contexts
func StrictPermissions() *PermissionSet {
	ps := NewPermissionSet()
	ps.Add(PermissionReadFiles)
	ps.Add(PermissionWriteFiles)
	ps.Add(PermissionNetworkAccess)
	ps.Add(PermissionAccessDatabase)
	ps.Add(PermissionModifyUI)
	return ps
}

// MaximumPermissions returns all available permissions
func MaximumPermissions() *PermissionSet {
	ps := NewPermissionSet()
	ps.Add(PermissionReadFiles)
	ps.Add(PermissionWriteFiles)
	ps.Add(PermissionNetworkAccess)
	ps.Add(PermissionExecuteCommands)
	ps.Add(PermissionAccessDatabase)
	ps.Add(PermissionModifyUI)
	return ps
}
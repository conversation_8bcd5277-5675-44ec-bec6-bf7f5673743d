package plugin

import (
	"fmt"
	"sync"
	"time"
)

// SimplePluginManager provides a basic implementation of PluginManager
type SimplePluginManager struct {
	plugins map[PluginID]Plugin
	mutex   sync.RWMutex
}

// NewSimplePluginManager creates a new simple plugin manager
func NewSimplePluginManager() *SimplePluginManager {
	return &SimplePluginManager{
		plugins: make(map[PluginID]Plugin),
	}
}

func (pm *SimplePluginManager) RegisterPlugin(id PluginID, plugin Plugin) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	
	if _, exists := pm.plugins[id]; exists {
		return fmt.Errorf("plugin with ID %s already registered", id.String())
	}
	
	pm.plugins[id] = plugin
	return nil
}

func (pm *SimplePluginManager) UnregisterPlugin(id PluginID) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	
	if _, exists := pm.plugins[id]; !exists {
		return fmt.Errorf("plugin with ID %s not found", id.String())
	}
	
	delete(pm.plugins, id)
	return nil
}

func (pm *SimplePluginManager) GetPlugin(id PluginID) (Plugin, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	
	plugin, exists := pm.plugins[id]
	if !exists {
		return nil, fmt.Errorf("plugin with ID %s not found", id.String())
	}
	
	return plugin, nil
}

func (pm *SimplePluginManager) ListPlugins() []Plugin {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	
	plugins := make([]Plugin, 0, len(pm.plugins))
	for _, plugin := range pm.plugins {
		plugins = append(plugins, plugin)
	}
	
	return plugins
}

func (pm *SimplePluginManager) StartPlugin(id PluginID) error {
	pm.mutex.RLock()
	plugin, exists := pm.plugins[id]
	pm.mutex.RUnlock()
	
	if !exists {
		return fmt.Errorf("plugin with ID %s not found", id.String())
	}
	
	return plugin.Start()
}

func (pm *SimplePluginManager) StopPlugin(id PluginID) error {
	pm.mutex.RLock()
	plugin, exists := pm.plugins[id]
	pm.mutex.RUnlock()
	
	if !exists {
		return fmt.Errorf("plugin with ID %s not found", id.String())
	}
	
	return plugin.Stop()
}

func (pm *SimplePluginManager) GetPluginStatus(id PluginID) (PluginStatus, error) {
	pm.mutex.RLock()
	plugin, exists := pm.plugins[id]
	pm.mutex.RUnlock()
	
	if !exists {
		return PluginStatus{State: PluginStateUnloaded}, fmt.Errorf("plugin with ID %s not found", id.String())
	}
	
	return plugin.GetStatus(), nil
}

func (pm *SimplePluginManager) GetPluginMetadata(id PluginID) (map[string]interface{}, error) {
	pm.mutex.RLock()
	plugin, exists := pm.plugins[id]
	pm.mutex.RUnlock()
	
	if !exists {
		return nil, fmt.Errorf("plugin with ID %s not found", id.String())
	}
	
	metadata := plugin.Metadata()
	return map[string]interface{}{
		"id":           metadata.ID.String(),
		"name":         metadata.Name,
		"description":  metadata.Description,
		"version":      metadata.Version.String(),
		"author":       metadata.Author,
		"license":      metadata.License,
		"homepage":     metadata.Homepage,
		"repository":   metadata.Repository,
		"keywords":     metadata.Keywords,
	}, nil
}

func (pm *SimplePluginManager) SetPluginConfig(id PluginID, config map[string]interface{}) error {
	pm.mutex.RLock()
	plugin, exists := pm.plugins[id]
	pm.mutex.RUnlock()
	
	if !exists {
		return fmt.Errorf("plugin with ID %s not found", id.String())
	}
	
	return plugin.UpdateConfig(config)
}

func (pm *SimplePluginManager) GetPluginConfig(id PluginID) (map[string]interface{}, error) {
	pm.mutex.RLock()
	_, exists := pm.plugins[id]
	pm.mutex.RUnlock()
	
	if !exists {
		return nil, fmt.Errorf("plugin with ID %s not found", id.String())
	}
	
	// For now, return empty config since Plugin interface doesn't have GetConfig method
	// In a real implementation, this would store and return plugin configurations
	return map[string]interface{}{}, nil
}

// Additional PluginManager interface methods

func (pm *SimplePluginManager) Start() error {
	// Simple manager doesn't need startup
	return nil
}

func (pm *SimplePluginManager) Stop() error {
	// Simple manager doesn't need shutdown
	return nil
}

func (pm *SimplePluginManager) IsRunning() bool {
	// Simple manager is always running
	return true
}

func (pm *SimplePluginManager) InstallPlugin(source PluginSource) error {
	return fmt.Errorf("plugin installation not implemented")
}

func (pm *SimplePluginManager) UninstallPlugin(id PluginID) error {
	return pm.UnregisterPlugin(id)
}

func (pm *SimplePluginManager) EnablePlugin(id PluginID) error {
	return pm.StartPlugin(id)
}

func (pm *SimplePluginManager) DisablePlugin(id PluginID) error {
	return pm.StopPlugin(id)
}

func (pm *SimplePluginManager) ReloadPlugin(id PluginID) error {
	if err := pm.StopPlugin(id); err != nil {
		return err
	}
	return pm.StartPlugin(id)
}

func (pm *SimplePluginManager) UpdatePlugin(id PluginID, source PluginSource) error {
	return fmt.Errorf("plugin updates not implemented")
}

func (pm *SimplePluginManager) CheckPluginHealth(id PluginID) (HealthStatus, error) {
	pm.mutex.RLock()
	plugin, exists := pm.plugins[id]
	pm.mutex.RUnlock()
	
	if !exists {
		return HealthStatus{}, fmt.Errorf("plugin with ID %s not found", id.String())
	}
	
	healthy := plugin.IsHealthy()
	return HealthStatus{
		Healthy:      healthy,
		Message:      "Plugin health check completed",
		LastCheck:    time.Now(),
		CheckCount:   1,
		FailureCount: 0,
		Details:      map[string]interface{}{"status": "checked"},
	}, nil
}

func (pm *SimplePluginManager) UnloadAll() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	
	for id := range pm.plugins {
		if err := pm.StopPlugin(id); err != nil {
			return fmt.Errorf("failed to stop plugin %s: %w", id.String(), err)
		}
	}
	
	pm.plugins = make(map[PluginID]Plugin)
	return nil
}

func (pm *SimplePluginManager) UpdatePluginConfig(id PluginID, config map[string]interface{}) error {
	return pm.SetPluginConfig(id, config)
}

func (pm *SimplePluginManager) GetSystemHealth() (SystemHealth, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	
	totalPlugins := len(pm.plugins)
	activePlugins := 0
	failedPlugins := 0
	
	for _, plugin := range pm.plugins {
		if plugin.IsHealthy() {
			activePlugins++
		} else {
			failedPlugins++
		}
	}
	
	return SystemHealth{
		Healthy:       failedPlugins == 0,
		TotalPlugins:  totalPlugins,
		ActivePlugins: activePlugins,
		FailedPlugins: failedPlugins,
		ResourceUsage: ResourceUsage{},
		LastCheck:     time.Now(),
		Issues:        []HealthIssue{},
	}, nil
}

func (pm *SimplePluginManager) OnPluginStateChanged(callback func(pluginID PluginID, oldState, newState PluginState)) {
	// Event callbacks not implemented for simple manager
}

func (pm *SimplePluginManager) OnPluginError(callback func(pluginID PluginID, err error)) {
	// Event callbacks not implemented for simple manager
}
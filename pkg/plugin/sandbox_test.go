package plugin

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"
)

func TestSimpleSandbox_Creation(t *testing.T) {
	pluginID := NewPluginID("test-plugin")
	config := SandboxConfig{
		ResourceLimits: ResourceLimits{
			MaxMemory:        100 * 1024 * 1024, // 100MB
			MaxExecutionTime: 5 * time.Second,
		},
		Permissions:   []string{"file:read", "network:connect"},
		NetworkAccess: true,
		Timeout:       10 * time.Second,
	}

	sandbox := NewSimpleSandbox(pluginID, config)

	if sandbox.ID() == "" {
		t.Error("Sandbox ID should not be empty")
	}

	if sandbox.PluginID() != pluginID {
		t.Errorf("Expected plugin ID %s, got %s", pluginID, sandbox.PluginID())
	}

	if !sandbox.IsActive() {
		t.Error("Sandbox should be active after creation")
	}

	if !sandbox.HasPermission("file:read") {
		t.<PERSON>rror("Sandbox should have file:read permission")
	}

	if sandbox.HasPermission("file:write") {
		t.Error("Sandbox should not have file:write permission")
	}
}

func TestSimpleSandbox_Execute(t *testing.T) {
	pluginID := NewPluginID("test-plugin")
	config := SandboxConfig{
		Timeout: 1 * time.Second,
	}

	sandbox := NewSimpleSandbox(pluginID, config)
	defer sandbox.Destroy()

	// Test successful execution
	executed := false
	err := sandbox.Execute(context.Background(), func() error {
		executed = true
		return nil
	})
	if err != nil {
		t.Fatalf("Execution should succeed: %v", err)
	}

	if !executed {
		t.Error("Function should have been executed")
	}

	if sandbox.GetExecutionCount() != 1 {
		t.Errorf("Expected 1 execution, got %d", sandbox.GetExecutionCount())
	}
}

func TestSimpleSandbox_ExecuteWithError(t *testing.T) {
	pluginID := NewPluginID("test-plugin")
	config := SandboxConfig{
		Timeout: 1 * time.Second,
	}

	sandbox := NewSimpleSandbox(pluginID, config)
	defer sandbox.Destroy()

	// Test execution with error
	expectedError := "test error"
	err := sandbox.Execute(context.Background(), func() error {
		return fmt.Errorf("%s", expectedError)
	})

	if err == nil {
		t.Fatal("Execution should fail")
	}

	if err.Error() != expectedError {
		t.Errorf("Expected error '%s', got '%s'", expectedError, err.Error())
	}
}

func TestSimpleSandbox_ExecuteTimeout(t *testing.T) {
	pluginID := NewPluginID("test-plugin")
	config := SandboxConfig{
		Timeout: 100 * time.Millisecond,
	}

	sandbox := NewSimpleSandbox(pluginID, config)
	defer sandbox.Destroy()

	// Test execution timeout
	err := sandbox.Execute(context.Background(), func() error {
		time.Sleep(200 * time.Millisecond) // Sleep longer than timeout
		return nil
	})

	if err == nil {
		t.Fatal("Execution should timeout")
	}

	if !strings.Contains(err.Error(), "timeout") {
		t.Errorf("Error should mention timeout, got: %s", err.Error())
	}
}

func TestSimpleSandbox_ExecutePanic(t *testing.T) {
	pluginID := NewPluginID("test-plugin")
	config := SandboxConfig{}

	sandbox := NewSimpleSandbox(pluginID, config)
	defer sandbox.Destroy()

	// Test execution with panic
	err := sandbox.Execute(context.Background(), func() error {
		panic("test panic")
	})

	if err == nil {
		t.Fatal("Execution should handle panic")
	}

	if !strings.Contains(err.Error(), "panicked") {
		t.Errorf("Error should mention panic, got: %s", err.Error())
	}
}

func TestSimpleSandbox_ResourceUsage(t *testing.T) {
	pluginID := NewPluginID("test-plugin")
	config := SandboxConfig{}

	sandbox := NewSimpleSandbox(pluginID, config)
	defer sandbox.Destroy()

	// Execute something to update resource usage
	sandbox.Execute(context.Background(), func() error {
		return nil
	})

	usage := sandbox.GetResourceUsage()

	if usage.LastUpdated.IsZero() {
		t.Error("Resource usage should have last updated time")
	}

	if usage.Memory < 0 {
		t.Error("Memory usage should not be negative")
	}
}

func TestSimpleSandbox_Destroy(t *testing.T) {
	pluginID := NewPluginID("test-plugin")
	config := SandboxConfig{}

	sandbox := NewSimpleSandbox(pluginID, config)

	if !sandbox.IsActive() {
		t.Error("Sandbox should be active before destroy")
	}

	err := sandbox.Destroy()
	if err != nil {
		t.Fatalf("Destroy should succeed: %v", err)
	}

	if sandbox.IsActive() {
		t.Error("Sandbox should not be active after destroy")
	}

	// Destroying again should fail
	err = sandbox.Destroy()
	if err == nil {
		t.Error("Destroying already destroyed sandbox should fail")
	}

	// Execution should fail after destroy
	err = sandbox.Execute(context.Background(), func() error {
		return nil
	})
	if err == nil {
		t.Error("Execution should fail on destroyed sandbox")
	}
}

func TestSimpleSandbox_Cleanup(t *testing.T) {
	pluginID := NewPluginID("test-plugin")
	config := SandboxConfig{}

	sandbox := NewSimpleSandbox(pluginID, config)

	cleanupCalled := false
	sandbox.AddCleanup(func() error {
		cleanupCalled = true
		return nil
	})

	err := sandbox.Destroy()
	if err != nil {
		t.Fatalf("Destroy should succeed: %v", err)
	}

	if !cleanupCalled {
		t.Error("Cleanup function should have been called")
	}
}

func TestInMemoryPluginSandbox_CreateDestroy(t *testing.T) {
	manager := NewInMemoryPluginSandbox()
	pluginID := NewPluginID("test-plugin")
	config := SandboxConfig{}

	// Create sandbox
	sandbox, err := manager.CreateSandbox(pluginID, config)
	if err != nil {
		t.Fatalf("Create sandbox should succeed: %v", err)
	}

	sandboxID := sandbox.ID()

	// Verify sandbox exists
	_, exists := manager.GetSandbox(sandboxID)
	if !exists {
		t.Error("Sandbox should exist in manager")
	}

	// Destroy sandbox
	err = manager.DestroySandbox(sandboxID)
	if err != nil {
		t.Fatalf("Destroy sandbox should succeed: %v", err)
	}

	// Verify sandbox is removed
	sandboxes := manager.ListSandboxes()
	if len(sandboxes) != 0 {
		t.Errorf("Expected 0 active sandboxes, got %d", len(sandboxes))
	}
}

func TestInMemoryPluginSandbox_ResourceLimits(t *testing.T) {
	manager := NewInMemoryPluginSandbox()
	pluginID := NewPluginID("test-plugin")
	config := SandboxConfig{}

	sandbox, err := manager.CreateSandbox(pluginID, config)
	if err != nil {
		t.Fatalf("Create sandbox should succeed: %v", err)
	}
	defer manager.DestroySandbox(sandbox.ID())

	// Set resource limits
	limits := ResourceLimits{
		MaxMemory:        50 * 1024 * 1024, // 50MB
		MaxExecutionTime: 2 * time.Second,
	}

	err = manager.SetResourceLimits(sandbox.ID(), limits)
	if err != nil {
		t.Fatalf("Set resource limits should succeed: %v", err)
	}

	// Get resource usage
	usage, err := manager.GetResourceUsage(sandbox.ID())
	if err != nil {
		t.Fatalf("Get resource usage should succeed: %v", err)
	}

	if usage.LastUpdated.IsZero() {
		t.Error("Resource usage should have last updated time")
	}
}

func TestInMemoryPluginSandbox_Permissions(t *testing.T) {
	manager := NewInMemoryPluginSandbox()
	pluginID := NewPluginID("test-plugin")
	config := SandboxConfig{
		Permissions: []string{"file:read"},
	}

	sandbox, err := manager.CreateSandbox(pluginID, config)
	if err != nil {
		t.Fatalf("Create sandbox should succeed: %v", err)
	}
	defer manager.DestroySandbox(sandbox.ID())

	// Test initial permission
	err = manager.ValidateAccess(sandbox.ID(), "file", "read")
	if err != nil {
		t.Errorf("Should have file:read permission: %v", err)
	}

	err = manager.ValidateAccess(sandbox.ID(), "file", "write")
	if err == nil {
		t.Error("Should not have file:write permission")
	}

	// Update permissions
	newPermissions := []string{"file:read", "file:write", "network:connect"}
	err = manager.SetPermissions(sandbox.ID(), newPermissions)
	if err != nil {
		t.Fatalf("Set permissions should succeed: %v", err)
	}

	// Test updated permissions
	err = manager.ValidateAccess(sandbox.ID(), "file", "write")
	if err != nil {
		t.Errorf("Should have file:write permission after update: %v", err)
	}

	err = manager.ValidateAccess(sandbox.ID(), "network", "connect")
	if err != nil {
		t.Errorf("Should have network:connect permission: %v", err)
	}
}

func TestInMemoryPluginSandbox_WildcardPermissions(t *testing.T) {
	manager := NewInMemoryPluginSandbox()
	pluginID := NewPluginID("test-plugin")
	config := SandboxConfig{
		Permissions: []string{"file:*"},
	}

	sandbox, err := manager.CreateSandbox(pluginID, config)
	if err != nil {
		t.Fatalf("Create sandbox should succeed: %v", err)
	}
	defer manager.DestroySandbox(sandbox.ID())

	// Test wildcard permission
	err = manager.ValidateAccess(sandbox.ID(), "file", "read")
	if err != nil {
		t.Errorf("Should have file:read permission via wildcard: %v", err)
	}

	err = manager.ValidateAccess(sandbox.ID(), "file", "write")
	if err != nil {
		t.Errorf("Should have file:write permission via wildcard: %v", err)
	}

	err = manager.ValidateAccess(sandbox.ID(), "network", "connect")
	if err == nil {
		t.Error("Should not have network:connect permission")
	}
}

func TestInMemoryPluginSandbox_CleanupInactive(t *testing.T) {
	manager := NewInMemoryPluginSandbox()
	pluginID := NewPluginID("test-plugin")
	config := SandboxConfig{}

	// Create multiple sandboxes
	sandbox1, _ := manager.CreateSandbox(pluginID, config)
	sandbox2, _ := manager.CreateSandbox(pluginID, config)
	// sandbox3, _ := manager.CreateSandbox(pluginID, config)

	// Destroy some sandboxes
	sandbox1.Destroy()
	sandbox2.Destroy()

	// Initially, all sandboxes are in the registry
	allSandboxes := manager.ListSandboxes()
	if len(allSandboxes) != 1 { // Only sandbox3 should be active
		t.Errorf("Expected 1 active sandbox, got %d", len(allSandboxes))
	}

	// Cleanup inactive sandboxes
	removed := manager.CleanupInactiveSandboxes()
	if removed != 2 {
		t.Errorf("Expected 2 sandboxes removed, got %d", removed)
	}

	// Verify cleanup
	activeSandboxes := manager.ListSandboxes()
	if len(activeSandboxes) != 1 {
		t.Errorf("Expected 1 active sandbox after cleanup, got %d", len(activeSandboxes))
	}
}

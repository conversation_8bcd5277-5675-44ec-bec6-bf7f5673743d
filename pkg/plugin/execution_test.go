package plugin

import (
	"context"
	"errors"
	"testing"
	"time"
)

// Mock implementations for testing

type MockPluginManager struct {
	plugins map[PluginID]Plugin
	running bool
}

func NewMockPluginManager() *MockPluginManager {
	return &MockPluginManager{
		plugins: make(map[PluginID]Plugin),
		running: false,
	}
}

// Lifecycle methods
func (m *MockPluginManager) Start() error {
	m.running = true
	return nil
}

func (m *MockPluginManager) Stop() error {
	m.running = false
	return nil
}

func (m *MockPluginManager) IsRunning() bool {
	return m.running
}

// Plugin management methods
func (m *MockPluginManager) RegisterPlugin(pluginID PluginID, plugin Plugin) error {
	m.plugins[pluginID] = plugin
	return nil
}

func (m *MockPluginManager) UnregisterPlugin(pluginID PluginID) error {
	delete(m.plugins, pluginID)
	return nil
}

func (m *MockPluginManager) InstallPlugin(source PluginSource) error {
	return nil
}

func (m *MockPluginManager) UninstallPlugin(pluginID PluginID) error {
	delete(m.plugins, pluginID)
	return nil
}

func (m *MockPluginManager) EnablePlugin(pluginID PluginID) error {
	return nil
}

func (m *MockPluginManager) DisablePlugin(pluginID PluginID) error {
	return nil
}

// Plugin operations
func (m *MockPluginManager) GetPlugin(pluginID PluginID) (Plugin, error) {
	plugin, exists := m.plugins[pluginID]
	if !exists {
		return nil, errors.New("plugin not found")
	}
	return plugin, nil
}

func (m *MockPluginManager) ListPlugins() []Plugin {
	var plugins []Plugin
	for _, plugin := range m.plugins {
		plugins = append(plugins, plugin)
	}
	return plugins
}

func (m *MockPluginManager) GetPluginStatus(pluginID PluginID) (PluginStatus, error) {
	if _, exists := m.plugins[pluginID]; !exists {
		return PluginStatus{}, errors.New("plugin not found")
	}
	return PluginStatus{State: PluginStateActive}, nil
}

// Hot swapping
func (m *MockPluginManager) ReloadPlugin(pluginID PluginID) error {
	return nil
}

func (m *MockPluginManager) UpdatePlugin(pluginID PluginID, source PluginSource) error {
	return nil
}

// Bulk operations
func (m *MockPluginManager) UnloadAll() error {
	m.plugins = make(map[PluginID]Plugin)
	return nil
}

// Health monitoring
func (m *MockPluginManager) CheckPluginHealth(pluginID PluginID) (HealthStatus, error) {
	if _, exists := m.plugins[pluginID]; !exists {
		return HealthStatus{}, errors.New("plugin not found")
	}
	return HealthStatus{Healthy: true}, nil
}

func (m *MockPluginManager) GetSystemHealth() (SystemHealth, error) {
	return SystemHealth{Healthy: true}, nil
}

// Configuration 
func (m *MockPluginManager) UpdatePluginConfig(pluginID PluginID, config map[string]interface{}) error {
	return nil
}

func (m *MockPluginManager) GetPluginConfig(pluginID PluginID) (map[string]interface{}, error) {
	return make(map[string]interface{}), nil
}

// Events
func (m *MockPluginManager) OnPluginStateChanged(callback func(pluginID PluginID, oldState, newState PluginState)) {
}

func (m *MockPluginManager) OnPluginError(callback func(pluginID PluginID, err error)) {
}

func (m *MockPluginManager) AddMockPlugin(pluginID PluginID, plugin Plugin) {
	m.plugins[pluginID] = plugin
}

type MockExecutionPlugin struct {
	metadata PluginMetadata
}

func (m *MockExecutionPlugin) Execute(method string, input interface{}) (interface{}, error) {
	if method == "test_method" {
		return "test_result", nil
	}
	if method == "error_method" {
		return nil, errors.New("test error")
	}
	return nil, errors.New("method not found")
}

func (m *MockExecutionPlugin) Metadata() PluginMetadata {
	return m.metadata
}

// Add missing methods to satisfy Plugin interface
func (m *MockExecutionPlugin) ID() PluginID {
	return m.metadata.ID
}

func (m *MockExecutionPlugin) Initialize(ctx PluginContext) error {
	return nil
}

func (m *MockExecutionPlugin) Start() error {
	return nil
}

func (m *MockExecutionPlugin) Stop() error {
	return nil
}

func (m *MockExecutionPlugin) Cleanup() error {
	return nil
}

func (m *MockExecutionPlugin) IsHealthy() bool {
	return true
}

func (m *MockExecutionPlugin) GetStatus() PluginStatus {
	return PluginStatus{State: PluginStateActive}
}

func (m *MockExecutionPlugin) GetLastError() error {
	return nil
}

func (m *MockExecutionPlugin) GetConfigSchema() ConfigSchema {
	return ConfigSchema{}
}

func (m *MockExecutionPlugin) ValidateConfig(config map[string]interface{}) error {
	return nil
}

func (m *MockExecutionPlugin) UpdateConfig(config map[string]interface{}) error {
	return nil
}

func (m *MockExecutionPlugin) GetCapabilities() []string {
	return []string{}
}

func (m *MockExecutionPlugin) HasCapability(capability string) bool {
	return false
}

type MockSandbox struct {
	sandboxes map[string]*MockSandboxInstance
}

func NewMockSandbox() *MockSandbox {
	return &MockSandbox{
		sandboxes: make(map[string]*MockSandboxInstance),
	}
}

// PluginSandbox interface implementation
func (m *MockSandbox) CreateSandbox(pluginID PluginID, config SandboxConfig) (Sandbox, error) {
	sandboxID := pluginID.String() + "_sandbox"
	sandbox := &MockSandboxInstance{
		id:       sandboxID,
		pluginID: pluginID,
		active:   true,
	}
	m.sandboxes[sandboxID] = sandbox
	return sandbox, nil
}

func (m *MockSandbox) DestroySandbox(sandboxID string) error {
	delete(m.sandboxes, sandboxID)
	return nil
}

func (m *MockSandbox) SetResourceLimits(sandboxID string, limits ResourceLimits) error {
	return nil
}

func (m *MockSandbox) GetResourceUsage(sandboxID string) (ResourceUsage, error) {
	return ResourceUsage{}, nil
}

func (m *MockSandbox) SetPermissions(sandboxID string, permissions []string) error {
	return nil
}

func (m *MockSandbox) ValidateAccess(sandboxID string, resource string, operation string) error {
	return nil
}

type MockSandboxInstance struct {
	id       string
	pluginID PluginID
	active   bool
}

func (m *MockSandboxInstance) ID() string {
	return m.id
}

func (m *MockSandboxInstance) PluginID() PluginID {
	return m.pluginID
}

func (m *MockSandboxInstance) Execute(ctx context.Context, fn func() error) error {
	return fn()
}

func (m *MockSandboxInstance) IsActive() bool {
	return m.active
}

func (m *MockSandboxInstance) GetResourceUsage() ResourceUsage {
	return ResourceUsage{}
}

func (m *MockSandboxInstance) Destroy() error {
	m.active = false
	return nil
}

func TestDefaultSecurePluginExecutor_Execute(t *testing.T) {
	// Setup
	pluginManager := NewMockPluginManager()
	securityManager := NewSecurityManager()
	sandbox := NewMockSandbox()
	
	executor := NewDefaultSecurePluginExecutor(pluginManager, securityManager, sandbox)
	
	// Create security context
	secCtx := securityManager.CreateSecurityContext("user123", "session456")
	secCtx.AddPermission(PermissionReadFiles)
	
	// Create plugin
	pluginID := NewPluginID("test-plugin")
	mockPlugin := &MockExecutionPlugin{
		metadata: PluginMetadata{ID: pluginID, Name: "Test Plugin"},
	}
	pluginManager.AddMockPlugin(pluginID, mockPlugin)
	
	// Create plugin context
	pluginCtx := securityManager.CreatePluginContext("test-plugin", "Test Plugin", "1.0.0", secCtx)
	
	// Test successful execution
	options := ExecutionOptions{
		Timeout:       time.Second * 5,
		MaxMemory:     1024 * 1024,
		MaxCPUTime:    time.Second,
		RequiredPerms: []string{},
		Input:         map[string]interface{}{"method": "test_method", "data": "test input"},
	}
	
	result, err := executor.ExecuteWithContext(pluginCtx, "test_method", options)
	if err != nil {
		t.Fatalf("Execution should succeed: %v", err)
	}
	
	if !result.Success {
		t.Error("Result should be successful")
	}
	
	if result.Result != "test_result" {
		t.Errorf("Expected result 'test_result', got %v", result.Result)
	}
}

func TestDefaultSecurePluginExecutor_ExecuteWithMissingPermission(t *testing.T) {
	// Setup
	pluginManager := NewMockPluginManager()
	securityManager := NewSecurityManager()
	sandbox := NewMockSandbox()
	
	executor := NewDefaultSecurePluginExecutor(pluginManager, securityManager, sandbox)
	
	// Create security context without required permission
	secCtx := securityManager.CreateSecurityContext("user123", "session456")
	
	// Create plugin
	pluginID := NewPluginID("test-plugin")
	mockPlugin := &MockExecutionPlugin{
		metadata: PluginMetadata{ID: pluginID, Name: "Test Plugin"},
	}
	pluginManager.AddMockPlugin(pluginID, mockPlugin)
	
	// Create plugin context
	pluginCtx := securityManager.CreatePluginContext("test-plugin", "Test Plugin", "1.0.0", secCtx)
	
	// Test execution with missing permission
	options := ExecutionOptions{
		Timeout:       time.Second * 5,
		MaxMemory:     1024 * 1024,
		MaxCPUTime:    time.Second,
		RequiredPerms: []string{"write_files"}, // Plugin doesn't have this permission
		Input:         map[string]interface{}{"method": "test_method", "data": "test input"},
	}
	
	_, err := executor.ExecuteWithContext(pluginCtx, "test_method", options)
	if err == nil {
		t.Error("Execution should fail due to missing permission")
	}
}

func TestDefaultSecurePluginExecutor_ExecuteWithContext(t *testing.T) {
	// Setup
	pluginManager := NewMockPluginManager()
	securityManager := NewSecurityManager()
	sandbox := NewMockSandbox()
	
	executor := NewDefaultSecurePluginExecutor(pluginManager, securityManager, sandbox)
	
	// Create security context
	secCtx := securityManager.CreateSecurityContext("user123", "session456")
	
	// Create plugin
	pluginID := NewPluginID("test-plugin")
	mockPlugin := &MockExecutionPlugin{
		metadata: PluginMetadata{ID: pluginID, Name: "Test Plugin"},
	}
	pluginManager.AddMockPlugin(pluginID, mockPlugin)
	
	// Create plugin context
	pluginCtx := securityManager.CreatePluginContext("test-plugin", "Test Plugin", "1.0.0", secCtx)
	
	// Test execution with context
	options := ExecutionOptions{
		Timeout:    time.Second * 5,
		MaxMemory:  1024 * 1024,
		MaxCPUTime: time.Second,
		Input:      map[string]interface{}{"method": "test_method", "data": "test input"},
	}
	
	result, err := executor.ExecuteWithContext(pluginCtx, "test_method", options)
	if err != nil {
		t.Fatalf("Execution should succeed: %v", err)
	}
	
	if !result.Success {
		t.Error("Result should be successful")
	}
}

func TestDefaultSecurePluginExecutor_PreValidate(t *testing.T) {
	// Setup
	pluginManager := NewMockPluginManager()
	securityManager := NewSecurityManager()
	sandbox := NewMockSandbox()
	
	executor := NewDefaultSecurePluginExecutor(pluginManager, securityManager, sandbox)
	
	// Create plugin
	pluginID := NewPluginID("test-plugin")
	mockPlugin := &MockExecutionPlugin{
		metadata: PluginMetadata{ID: pluginID, Name: "Test Plugin"},
	}
	pluginManager.AddMockPlugin(pluginID, mockPlugin)
	
	// Test valid options
	options := ExecutionOptions{
		Timeout:    time.Second * 5,
		MaxMemory:  1024 * 1024,
		MaxCPUTime: time.Second,
	}
	
	err := executor.PreValidate("test-plugin", "test_method", options)
	if err != nil {
		t.Errorf("Validation should succeed: %v", err)
	}
	
	// Test invalid timeout
	invalidOptions := ExecutionOptions{
		Timeout:    0, // Invalid
		MaxMemory:  1024 * 1024,
		MaxCPUTime: time.Second,
	}
	
	err = executor.PreValidate("test-plugin", "test_method", invalidOptions)
	if err == nil {
		t.Error("Validation should fail for invalid timeout")
	}
}

func TestDefaultSecurePluginExecutor_GetExecutionStats(t *testing.T) {
	// Setup
	pluginManager := NewMockPluginManager()
	securityManager := NewSecurityManager()
	sandbox := &MockSandbox{}
	
	executor := NewDefaultSecurePluginExecutor(pluginManager, securityManager, sandbox)
	
	// Create security context
	secCtx := securityManager.CreateSecurityContext("user123", "session456")
	
	// Create plugin
	pluginID := NewPluginID("test-plugin")
	mockPlugin := &MockExecutionPlugin{
		metadata: PluginMetadata{ID: pluginID, Name: "Test Plugin"},
	}
	pluginManager.AddMockPlugin(pluginID, mockPlugin)
	
	// Create plugin context
	pluginCtx := securityManager.CreatePluginContext("test-plugin", "Test Plugin", "1.0.0", secCtx)
	
	// Execute plugin to generate stats
	options := ExecutionOptions{
		Timeout:    time.Second * 5,
		MaxMemory:  1024 * 1024,
		MaxCPUTime: time.Second,
		Input:      "test input",
	}
	
	executor.ExecuteWithContext(pluginCtx, "test_method", options)
	
	// Get stats
	stats, err := executor.GetExecutionStats("test-plugin")
	if err != nil {
		t.Fatalf("Should get execution stats: %v", err)
	}
	
	if stats.TotalExecutions != 1 {
		t.Errorf("Expected 1 total execution, got %d", stats.TotalExecutions)
	}
	
	if stats.SuccessfulExecutions != 1 {
		t.Errorf("Expected 1 successful execution, got %d", stats.SuccessfulExecutions)
	}
	
	if stats.FailedExecutions != 0 {
		t.Errorf("Expected 0 failed executions, got %d", stats.FailedExecutions)
	}
}

func TestPluginExecutionAuditor(t *testing.T) {
	auditor := NewPluginExecutionAuditor()
	
	// Create mock data
	secCtx := NewSecurityContext("user123", "session456")
	secCtx.AddPermission(PermissionReadFiles)
	
	pluginCtx := NewSecurePluginContext("test-plugin", "Test Plugin", "1.0.0", secCtx)
	
	result := &ExecutionResult{
		PluginID:      "test-plugin",
		Success:       true,
		ExecutionTime: time.Millisecond * 100,
		MemoryUsed:    1024,
		CPUTime:       time.Millisecond * 50,
		ExecutedAt:    time.Now(),
	}
	
	// Test auditing
	auditor.AuditExecution(pluginCtx, "test_method", result)
	
	// Test getting audit log
	log := auditor.GetAuditLog(10)
	if len(log) != 1 {
		t.Errorf("Expected 1 audit entry, got %d", len(log))
	}
	
	entry := log[0]
	if entry.PluginID != "test-plugin" {
		t.Errorf("Expected plugin ID 'test-plugin', got %s", entry.PluginID)
	}
	
	if entry.Method != "test_method" {
		t.Errorf("Expected method 'test_method', got %s", entry.Method)
	}
	
	if entry.UserID != "user123" {
		t.Errorf("Expected user ID 'user123', got %s", entry.UserID)
	}
	
	if !entry.Success {
		t.Error("Entry should be marked as successful")
	}
}

func TestPluginExecutionAuditor_GetAuditLogForPlugin(t *testing.T) {
	auditor := NewPluginExecutionAuditor()
	
	// Create mock data for different plugins
	secCtx := NewSecurityContext("user123", "session456")
	
	plugin1Ctx := NewSecurePluginContext("plugin1", "Plugin 1", "1.0.0", secCtx)
	plugin2Ctx := NewSecurePluginContext("plugin2", "Plugin 2", "1.0.0", secCtx)
	
	result1 := &ExecutionResult{
		PluginID:   "plugin1",
		Success:    true,
		ExecutedAt: time.Now(),
	}
	
	result2 := &ExecutionResult{
		PluginID:   "plugin2",
		Success:    true,
		ExecutedAt: time.Now(),
	}
	
	// Audit executions
	auditor.AuditExecution(plugin1Ctx, "method1", result1)
	auditor.AuditExecution(plugin2Ctx, "method2", result2)
	auditor.AuditExecution(plugin1Ctx, "method3", result1)
	
	// Test getting audit log for specific plugin
	plugin1Log := auditor.GetAuditLogForPlugin("plugin1", 10)
	if len(plugin1Log) != 2 {
		t.Errorf("Expected 2 audit entries for plugin1, got %d", len(plugin1Log))
	}
	
	plugin2Log := auditor.GetAuditLogForPlugin("plugin2", 10)
	if len(plugin2Log) != 1 {
		t.Errorf("Expected 1 audit entry for plugin2, got %d", len(plugin2Log))
	}
}

func TestPluginExecutionAuditor_GetAuditLogForUser(t *testing.T) {
	auditor := NewPluginExecutionAuditor()
	
	// Create mock data for different users
	secCtx1 := NewSecurityContext("user1", "session1")
	secCtx2 := NewSecurityContext("user2", "session2")
	
	plugin1Ctx := NewSecurePluginContext("plugin1", "Plugin 1", "1.0.0", secCtx1)
	plugin2Ctx := NewSecurePluginContext("plugin2", "Plugin 2", "1.0.0", secCtx2)
	
	result := &ExecutionResult{
		PluginID:   "plugin1",
		Success:    true,
		ExecutedAt: time.Now(),
	}
	
	// Audit executions
	auditor.AuditExecution(plugin1Ctx, "method1", result)
	auditor.AuditExecution(plugin2Ctx, "method2", result)
	auditor.AuditExecution(plugin1Ctx, "method3", result)
	
	// Test getting audit log for specific user
	user1Log := auditor.GetAuditLogForUser("user1", 10)
	if len(user1Log) != 2 {
		t.Errorf("Expected 2 audit entries for user1, got %d", len(user1Log))
	}
	
	user2Log := auditor.GetAuditLogForUser("user2", 10)
	if len(user2Log) != 1 {
		t.Errorf("Expected 1 audit entry for user2, got %d", len(user2Log))
	}
}

func TestPluginExecutionAuditor_GetStats(t *testing.T) {
	auditor := NewPluginExecutionAuditor()
	
	// Create mock data
	secCtx := NewSecurityContext("user123", "session456")
	pluginCtx := NewSecurePluginContext("test-plugin", "Test Plugin", "1.0.0", secCtx)
	
	successResult := &ExecutionResult{
		PluginID:   "test-plugin",
		Success:    true,
		ExecutedAt: time.Now(),
	}
	
	failResult := &ExecutionResult{
		PluginID:   "test-plugin",
		Success:    false,
		Error:      errors.New("test error"),
		ExecutedAt: time.Now(),
	}
	
	// Audit executions
	auditor.AuditExecution(pluginCtx, "method1", successResult)
	auditor.AuditExecution(pluginCtx, "method2", failResult)
	auditor.AuditExecution(pluginCtx, "method3", successResult)
	
	// Get stats
	stats := auditor.GetStats()
	
	totalExecutions, ok := stats["total_executions"].(int)
	if !ok || totalExecutions != 3 {
		t.Errorf("Expected 3 total executions, got %v", stats["total_executions"])
	}
	
	successfulExecutions, ok := stats["successful_executions"].(int)
	if !ok || successfulExecutions != 2 {
		t.Errorf("Expected 2 successful executions, got %v", stats["successful_executions"])
	}
	
	failedExecutions, ok := stats["failed_executions"].(int)
	if !ok || failedExecutions != 1 {
		t.Errorf("Expected 1 failed execution, got %v", stats["failed_executions"])
	}
	
	errorRate, ok := stats["error_rate"].(float64)
	if !ok || errorRate != 1.0/3.0 {
		t.Errorf("Expected error rate 0.333, got %v", stats["error_rate"])
	}
}

func TestPluginExecutionAuditor_ClearAuditLog(t *testing.T) {
	auditor := NewPluginExecutionAuditor()
	
	// Create mock data
	secCtx := NewSecurityContext("user123", "session456")
	pluginCtx := NewSecurePluginContext("test-plugin", "Test Plugin", "1.0.0", secCtx)
	
	result := &ExecutionResult{
		PluginID:   "test-plugin",
		Success:    true,
		ExecutedAt: time.Now(),
	}
	
	// Audit execution
	auditor.AuditExecution(pluginCtx, "method1", result)
	
	// Verify log has entries
	log := auditor.GetAuditLog(10)
	if len(log) != 1 {
		t.Errorf("Expected 1 audit entry before clear, got %d", len(log))
	}
	
	// Clear log
	auditor.ClearAuditLog()
	
	// Verify log is empty
	log = auditor.GetAuditLog(10)
	if len(log) != 0 {
		t.Errorf("Expected 0 audit entries after clear, got %d", len(log))
	}
}

func TestExecutionOptions_Validation(t *testing.T) {
	// Test network access validation
	options := ExecutionOptions{
		Timeout:         time.Second * 5,
		MaxMemory:       1024 * 1024,
		MaxCPUTime:      time.Second,
		AllowNetwork:    true,
		AllowFileSystem: false,
	}
	
	pluginManager := NewMockPluginManager()
	securityManager := NewSecurityManager()
	
	// Create context without network permission
	secCtx := NewSecurityContext("user", "session")
	pluginCtx := securityManager.CreatePluginContext("plugin", "Plugin", "1.0.0", secCtx)
	
	securityManager.pluginContexts["plugin"] = pluginCtx
	sandbox := NewMockSandbox()
	
	executor := NewDefaultSecurePluginExecutor(pluginManager, securityManager, sandbox)
	
	// Should fail validation due to missing network permission
	err := executor.preExecutionValidation(pluginCtx, "test", options)
	if err == nil {
		t.Error("Should fail validation for network access without permission")
	}
	
	// Add network permission and try again
	secCtx.AddPermission(PermissionNetworkAccess)
	err = executor.preExecutionValidation(pluginCtx, "test", options)
	if err != nil && err.Error() == "plugin does not have network access permission" {
		t.Errorf("Should pass validation with network permission: %v", err)
	}
}
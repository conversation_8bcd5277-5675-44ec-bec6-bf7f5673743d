package plugin

import (
	"fmt"
	"sync"
)

// SimpleSandboxManager provides a basic implementation of PluginSandbox
type SimpleSandboxManager struct {
	sandboxes map[string]*SimpleSandbox
	mutex     sync.RWMutex
}

// NewSimpleSandboxManager creates a new simple sandbox manager
func NewSimpleSandboxManager() *SimpleSandboxManager {
	return &SimpleSandboxManager{
		sandboxes: make(map[string]*SimpleSandbox),
	}
}

func (sm *SimpleSandboxManager) CreateSandbox(pluginID PluginID, config SandboxConfig) (Sandbox, error) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	sandboxID := fmt.Sprintf("sandbox-%s", pluginID.String())
	
	if _, exists := sm.sandboxes[sandboxID]; exists {
		return nil, fmt.Errorf("sandbox for plugin %s already exists", pluginID.String())
	}
	
	sandbox := NewSimpleSandbox(pluginID, config)
	sm.sandboxes[sandboxID] = sandbox
	
	return sandbox, nil
}

func (sm *SimpleSandboxManager) DestroySandbox(sandboxID string) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	sandbox, exists := sm.sandboxes[sandboxID]
	if !exists {
		return fmt.Errorf("sandbox %s not found", sandboxID)
	}
	
	if err := sandbox.Destroy(); err != nil {
		return fmt.Errorf("failed to destroy sandbox %s: %w", sandboxID, err)
	}
	
	delete(sm.sandboxes, sandboxID)
	return nil
}

func (sm *SimpleSandboxManager) SetResourceLimits(sandboxID string, limits ResourceLimits) error {
	sm.mutex.RLock()
	sandbox, exists := sm.sandboxes[sandboxID]
	sm.mutex.RUnlock()
	
	if !exists {
		return fmt.Errorf("sandbox %s not found", sandboxID)
	}
	
	// For SimpleSandbox, resource limits are not implemented yet
	_ = limits
	_ = sandbox
	return nil
}

func (sm *SimpleSandboxManager) GetResourceUsage(sandboxID string) (ResourceUsage, error) {
	sm.mutex.RLock()
	_, exists := sm.sandboxes[sandboxID]
	sm.mutex.RUnlock()
	
	if !exists {
		return ResourceUsage{}, fmt.Errorf("sandbox %s not found", sandboxID)
	}
	
	// Return empty resource usage for now
	return ResourceUsage{}, nil
}

func (sm *SimpleSandboxManager) SetPermissions(sandboxID string, permissions []string) error {
	sm.mutex.RLock()
	_, exists := sm.sandboxes[sandboxID]
	sm.mutex.RUnlock()
	
	if !exists {
		return fmt.Errorf("sandbox %s not found", sandboxID)
	}
	
	// For SimpleSandbox, permissions are not implemented yet
	_ = permissions
	return nil
}

func (sm *SimpleSandboxManager) GetPermissions(sandboxID string) ([]string, error) {
	sm.mutex.RLock()
	_, exists := sm.sandboxes[sandboxID]
	sm.mutex.RUnlock()
	
	if !exists {
		return nil, fmt.Errorf("sandbox %s not found", sandboxID)
	}
	
	// Return empty permissions for now
	return []string{}, nil
}

func (sm *SimpleSandboxManager) ListSandboxes() []string {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	sandboxes := make([]string, 0, len(sm.sandboxes))
	for id := range sm.sandboxes {
		sandboxes = append(sandboxes, id)
	}
	
	return sandboxes
}

func (sm *SimpleSandboxManager) GetSandboxInfo(sandboxID string) (map[string]interface{}, error) {
	sm.mutex.RLock()
	sandbox, exists := sm.sandboxes[sandboxID]
	sm.mutex.RUnlock()
	
	if !exists {
		return nil, fmt.Errorf("sandbox %s not found", sandboxID)
	}
	
	return map[string]interface{}{
		"id":     sandbox.ID(),
		"active": sandbox.IsActive(),
	}, nil
}

func (sm *SimpleSandboxManager) ValidateAccess(sandboxID string, resource string, operation string) error {
	sm.mutex.RLock()
	_, exists := sm.sandboxes[sandboxID]
	sm.mutex.RUnlock()
	
	if !exists {
		return fmt.Errorf("sandbox %s not found", sandboxID)
	}
	
	// For SimpleSandbox, allow all access for now
	// In a real implementation, this would check permissions
	return nil
}

// SimplePluginRegistry provides a basic implementation of PluginRegistry
type SimplePluginRegistry struct {
	plugins map[PluginID]PluginMetadata
	mutex   sync.RWMutex
}

// NewSimplePluginRegistry creates a new simple plugin registry
func NewSimplePluginRegistry() *SimplePluginRegistry {
	return &SimplePluginRegistry{
		plugins: make(map[PluginID]PluginMetadata),
	}
}

func (r *SimplePluginRegistry) RegisterPlugin(metadata PluginMetadata) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	id := NewPluginID(metadata.Name)
	r.plugins[id] = metadata
	return nil
}

func (r *SimplePluginRegistry) UnregisterPlugin(id PluginID) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	delete(r.plugins, id)
	return nil
}

func (r *SimplePluginRegistry) GetPlugin(id PluginID) (PluginMetadata, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	metadata, exists := r.plugins[id]
	if !exists {
		return PluginMetadata{}, fmt.Errorf("plugin %s not found in registry", id.String())
	}
	
	return metadata, nil
}

func (r *SimplePluginRegistry) ListPlugins() []PluginMetadata {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	plugins := make([]PluginMetadata, 0, len(r.plugins))
	for _, metadata := range r.plugins {
		plugins = append(plugins, metadata)
	}
	
	return plugins
}

func (r *SimplePluginRegistry) UpdatePlugin(id PluginID, metadata PluginMetadata) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	if _, exists := r.plugins[id]; !exists {
		return fmt.Errorf("plugin %s not found in registry", id.String())
	}
	
	r.plugins[id] = metadata
	return nil
}

func (r *SimplePluginRegistry) Get(id PluginID) (Plugin, error) {
	// For SimplePluginRegistry, we don't store actual Plugin instances, just metadata
	// This is a design mismatch - returning nil for now
	return nil, fmt.Errorf("plugin registry only stores metadata, not plugin instances")
}

func (r *SimplePluginRegistry) Register(plugin Plugin) error {
	metadata := plugin.Metadata()
	return r.RegisterPlugin(metadata)
}

func (r *SimplePluginRegistry) Unregister(pluginID PluginID) error {
	return r.UnregisterPlugin(pluginID)
}

func (r *SimplePluginRegistry) List() []Plugin {
	// Return empty slice since we don't store plugin instances
	return []Plugin{}
}

func (r *SimplePluginRegistry) ListByCapability(capability string) []Plugin {
	// Return empty slice since we don't store plugin instances
	return []Plugin{}
}

func (r *SimplePluginRegistry) Search(query string) []Plugin {
	// Return empty slice since we don't store plugin instances
	return []Plugin{}
}

func (r *SimplePluginRegistry) ResolveDependencies(pluginID PluginID) ([]Plugin, error) {
	return []Plugin{}, fmt.Errorf("dependency resolution not implemented")
}

func (r *SimplePluginRegistry) GetDependents(pluginID PluginID) ([]Plugin, error) {
	return []Plugin{}, fmt.Errorf("dependent tracking not implemented")
}

func (r *SimplePluginRegistry) ValidateDependencies(plugin Plugin) error {
	// For simple registry, no dependency validation
	return nil
}

func (r *SimplePluginRegistry) OnPluginRegistered(callback func(plugin Plugin)) {
	// Event callbacks not implemented for simple registry
}

func (r *SimplePluginRegistry) OnPluginUnregistered(callback func(pluginID PluginID)) {
	// Event callbacks not implemented for simple registry
}

func (r *SimplePluginRegistry) SearchPlugins(query map[string]interface{}) ([]PluginMetadata, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	// Simple search implementation - return all plugins for now
	plugins := make([]PluginMetadata, 0, len(r.plugins))
	for _, metadata := range r.plugins {
		plugins = append(plugins, metadata)
	}
	
	return plugins, nil
}
package plugin

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"
)

// PluginRegistry manages plugin registration and discovery
type PluginRegistry interface {
	// Registration
	Register(plugin Plugin) error
	Unregister(pluginID PluginID) error
	
	// Discovery
	Get(pluginID PluginID) (Plugin, error)
	List() []Plugin
	ListByCapability(capability string) []Plugin
	Search(query string) []Plugin
	
	// Dependencies
	ResolveDependencies(pluginID PluginID) ([]Plugin, error)
	GetDependents(pluginID PluginID) ([]Plugin, error)
	ValidateDependencies(plugin Plugin) error
	
	// Events
	OnPluginRegistered(callback func(plugin Plugin))
	OnPluginUnregistered(callback func(pluginID PluginID))
}

// PluginLoader handles loading and unloading of plugins
type PluginLoader interface {
	// Loading
	LoadFromPath(path string) (Plugin, error)
	LoadFromRepository(repo Repository, pluginID PluginID, version PluginVersion) (Plugin, error)
	LoadFromBytes(data []byte, metadata PluginMetadata) (Plugin, error)
	
	// Unloading
	Unload(pluginID PluginID) error
	
	// Hot swapping
	Reload(pluginID PluginID) error
	CanReload(pluginID PluginID) bool
	
	// Validation
	ValidatePlugin(plugin Plugin) error
	GetLoadedPlugins() []PluginID
}

// PluginSandbox provides isolated execution environment for plugins
type PluginSandbox interface {
	// Sandbox creation
	CreateSandbox(pluginID PluginID, config SandboxConfig) (Sandbox, error)
	DestroySandbox(sandboxID string) error
	
	// Resource management
	SetResourceLimits(sandboxID string, limits ResourceLimits) error
	GetResourceUsage(sandboxID string) (ResourceUsage, error)
	
	// Security
	SetPermissions(sandboxID string, permissions []string) error
	ValidateAccess(sandboxID string, resource string, operation string) error
}

// Sandbox represents an isolated execution environment
type Sandbox interface {
	ID() string
	PluginID() PluginID
	Execute(ctx context.Context, fn func() error) error
	IsActive() bool
	GetResourceUsage() ResourceUsage
	Destroy() error
}

// SandboxConfig configures a plugin sandbox
type SandboxConfig struct {
	ResourceLimits ResourceLimits
	Permissions    []string
	NetworkAccess  bool
	FileSystemAccess bool
	AllowedPaths   []string
	Environment    map[string]string
	Timeout        time.Duration
}

// ResourceLimits defines resource constraints for a sandbox
type ResourceLimits struct {
	MaxMemory    int64         // bytes
	MaxCPU       float64       // percentage (0.0 - 1.0)
	MaxDiskSpace int64         // bytes
	MaxFileHandles int
	MaxNetworkConnections int
	MaxExecutionTime time.Duration
}

// ResourceUsage represents current resource usage
type ResourceUsage struct {
	Memory           int64
	CPU              float64
	DiskSpace        int64
	FileHandles      int
	NetworkConnections int
	ExecutionTime    time.Duration
	LastUpdated      time.Time
}

// PluginManager orchestrates the entire plugin system
type PluginManager interface {
	// Lifecycle
	Start() error
	Stop() error
	IsRunning() bool
	
	// Plugin management
	RegisterPlugin(pluginID PluginID, plugin Plugin) error
	UnregisterPlugin(pluginID PluginID) error
	InstallPlugin(source PluginSource) error
	UninstallPlugin(pluginID PluginID) error
	EnablePlugin(pluginID PluginID) error
	DisablePlugin(pluginID PluginID) error
	
	// Plugin operations
	GetPlugin(pluginID PluginID) (Plugin, error)
	ListPlugins() []Plugin
	GetPluginStatus(pluginID PluginID) (PluginStatus, error)
	
	// Hot swapping
	ReloadPlugin(pluginID PluginID) error
	UpdatePlugin(pluginID PluginID, source PluginSource) error
	
	// Configuration
	UpdatePluginConfig(pluginID PluginID, config map[string]interface{}) error
	GetPluginConfig(pluginID PluginID) (map[string]interface{}, error)
	
	// Bulk operations
	UnloadAll() error
	
	// Health monitoring
	CheckPluginHealth(pluginID PluginID) (HealthStatus, error)
	GetSystemHealth() (SystemHealth, error)
	
	// Events
	OnPluginStateChanged(callback func(pluginID PluginID, oldState, newState PluginState))
	OnPluginError(callback func(pluginID PluginID, err error))
}

// PluginSource represents where a plugin comes from
type PluginSource struct {
	Type     SourceType
	Location string
	Version  PluginVersion
	Checksum string
	Metadata map[string]interface{}
}

type SourceType int

const (
	SourceTypeFile SourceType = iota
	SourceTypeGit
	SourceTypeRegistry
	SourceTypeHTTP
	SourceTypeDocker
)

// HealthStatus represents the health of a plugin
type HealthStatus struct {
	Healthy     bool
	Message     string
	LastCheck   time.Time
	CheckCount  int
	FailureCount int
	Details     map[string]interface{}
}

// SystemHealth represents overall system health
type SystemHealth struct {
	Healthy         bool
	TotalPlugins    int
	ActivePlugins   int
	FailedPlugins   int
	ResourceUsage   ResourceUsage
	LastCheck       time.Time
	Issues          []HealthIssue
}

// HealthIssue represents a health issue
type HealthIssue struct {
	Severity    Severity
	Component   string
	Message     string
	OccurredAt  time.Time
	Count       int
	Metadata    map[string]interface{}
}

type Severity int

const (
	SeverityInfo Severity = iota
	SeverityWarning
	SeverityError
	SeverityCritical
)

// Repository represents a plugin repository
type Repository interface {
	// Plugin discovery
	Search(query string) ([]PluginInfo, error)
	GetPlugin(pluginID PluginID, version PluginVersion) (PluginInfo, error)
	ListVersions(pluginID PluginID) ([]PluginVersion, error)
	
	// Plugin retrieval
	Download(pluginID PluginID, version PluginVersion) ([]byte, error)
	GetMetadata(pluginID PluginID, version PluginVersion) (PluginMetadata, error)
	
	// Repository management
	GetRepositoryInfo() RepositoryInfo
	Refresh() error
}

// PluginInfo contains information about a plugin in a repository
type PluginInfo struct {
	Metadata     PluginMetadata
	DownloadURL  string
	Checksum     string
	Size         int64
	Downloads    int64
	Rating       float64
	Reviews      int
	LastUpdated  time.Time
}

// RepositoryInfo contains information about a repository
type RepositoryInfo struct {
	Name         string
	URL          string
	Description  string
	PluginCount  int
	LastSync     time.Time
	Type         string
	Trusted      bool
}

// InMemoryPluginRegistry provides a simple in-memory plugin registry implementation
type InMemoryPluginRegistry struct {
	plugins          map[PluginID]Plugin
	pluginsByCapability map[string][]Plugin
	dependencies     map[PluginID][]PluginID
	dependents       map[PluginID][]PluginID
	
	registeredCallbacks   []func(plugin Plugin)
	unregisteredCallbacks []func(pluginID PluginID)
	
	mutex sync.RWMutex
}

// NewInMemoryPluginRegistry creates a new in-memory plugin registry
func NewInMemoryPluginRegistry() *InMemoryPluginRegistry {
	return &InMemoryPluginRegistry{
		plugins:               make(map[PluginID]Plugin),
		pluginsByCapability:   make(map[string][]Plugin),
		dependencies:          make(map[PluginID][]PluginID),
		dependents:            make(map[PluginID][]PluginID),
		registeredCallbacks:   make([]func(plugin Plugin), 0),
		unregisteredCallbacks: make([]func(pluginID PluginID), 0),
	}
}

func (r *InMemoryPluginRegistry) Register(plugin Plugin) error {
	if plugin == nil {
		return fmt.Errorf("plugin cannot be nil")
	}
	
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	pluginID := plugin.ID()
	
	// Check if already registered
	if _, exists := r.plugins[pluginID]; exists {
		return fmt.Errorf("plugin %s is already registered", pluginID)
	}
	
	// Validate dependencies
	if err := r.validateDependenciesLocked(plugin); err != nil {
		return fmt.Errorf("dependency validation failed: %w", err)
	}
	
	// Register plugin
	r.plugins[pluginID] = plugin
	
	// Index by capabilities
	for _, capability := range plugin.GetCapabilities() {
		r.pluginsByCapability[capability] = append(r.pluginsByCapability[capability], plugin)
	}
	
	// Update dependency tracking
	r.updateDependencyTrackingLocked(plugin)
	
	// Notify callbacks
	for _, callback := range r.registeredCallbacks {
		go callback(plugin)
	}
	
	return nil
}

func (r *InMemoryPluginRegistry) Unregister(pluginID PluginID) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	plugin, exists := r.plugins[pluginID]
	if !exists {
		return fmt.Errorf("plugin %s is not registered", pluginID)
	}
	
	// Check if other plugins depend on this one
	if dependents, exists := r.dependents[pluginID]; exists && len(dependents) > 0 {
		return fmt.Errorf("cannot unregister plugin %s: %d plugins depend on it", pluginID, len(dependents))
	}
	
	// Remove from plugins map
	delete(r.plugins, pluginID)
	
	// Remove from capability index
	for _, capability := range plugin.GetCapabilities() {
		if plugins, exists := r.pluginsByCapability[capability]; exists {
			for i, p := range plugins {
				if p.ID() == pluginID {
					r.pluginsByCapability[capability] = append(plugins[:i], plugins[i+1:]...)
					break
				}
			}
		}
	}
	
	// Clean up dependency tracking
	r.cleanupDependencyTrackingLocked(pluginID)
	
	// Notify callbacks
	for _, callback := range r.unregisteredCallbacks {
		go callback(pluginID)
	}
	
	return nil
}

func (r *InMemoryPluginRegistry) Get(pluginID PluginID) (Plugin, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	plugin, exists := r.plugins[pluginID]
	if !exists {
		return nil, fmt.Errorf("plugin %s not found", pluginID)
	}
	
	return plugin, nil
}

func (r *InMemoryPluginRegistry) List() []Plugin {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	plugins := make([]Plugin, 0, len(r.plugins))
	for _, plugin := range r.plugins {
		plugins = append(plugins, plugin)
	}
	
	return plugins
}

func (r *InMemoryPluginRegistry) ListByCapability(capability string) []Plugin {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	plugins, exists := r.pluginsByCapability[capability]
	if !exists {
		return make([]Plugin, 0)
	}
	
	// Return copy to prevent external modification
	result := make([]Plugin, len(plugins))
	copy(result, plugins)
	return result
}

func (r *InMemoryPluginRegistry) Search(query string) []Plugin {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	var results []Plugin
	queryLower := strings.ToLower(query)
	
	for _, plugin := range r.plugins {
		metadata := plugin.Metadata()
		
		// Search in name, description, and keywords
		if strings.Contains(strings.ToLower(metadata.Name), queryLower) ||
		   strings.Contains(strings.ToLower(metadata.Description), queryLower) {
			results = append(results, plugin)
			continue
		}
		
		// Search in keywords
		for _, keyword := range metadata.Keywords {
			if strings.Contains(strings.ToLower(keyword), queryLower) {
				results = append(results, plugin)
				break
			}
		}
	}
	
	return results
}

func (r *InMemoryPluginRegistry) ResolveDependencies(pluginID PluginID) ([]Plugin, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	plugin, exists := r.plugins[pluginID]
	if !exists {
		return nil, fmt.Errorf("plugin %s not found", pluginID)
	}
	
	var resolvedDeps []Plugin
	visited := make(map[PluginID]bool)
	
	if err := r.resolveDependenciesRecursive(plugin, visited, &resolvedDeps); err != nil {
		return nil, err
	}
	
	return resolvedDeps, nil
}

func (r *InMemoryPluginRegistry) GetDependents(pluginID PluginID) ([]Plugin, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	dependentIDs, exists := r.dependents[pluginID]
	if !exists {
		return make([]Plugin, 0), nil
	}
	
	var dependents []Plugin
	for _, depID := range dependentIDs {
		if plugin, exists := r.plugins[depID]; exists {
			dependents = append(dependents, plugin)
		}
	}
	
	return dependents, nil
}

func (r *InMemoryPluginRegistry) ValidateDependencies(plugin Plugin) error {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	return r.validateDependenciesLocked(plugin)
}

func (r *InMemoryPluginRegistry) OnPluginRegistered(callback func(plugin Plugin)) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	r.registeredCallbacks = append(r.registeredCallbacks, callback)
}

func (r *InMemoryPluginRegistry) OnPluginUnregistered(callback func(pluginID PluginID)) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	r.unregisteredCallbacks = append(r.unregisteredCallbacks, callback)
}

// Helper methods

func (r *InMemoryPluginRegistry) validateDependenciesLocked(plugin Plugin) error {
	metadata := plugin.Metadata()
	
	for _, dep := range metadata.Dependencies {
		depPlugin, exists := r.plugins[dep.PluginID]
		if !exists {
			if !dep.Optional {
				return fmt.Errorf("required dependency %s not found", dep.PluginID)
			}
			continue
		}
		
		// Check version compatibility
		depMetadata := depPlugin.Metadata()
		if !dep.Version.IsCompatibleWith(depMetadata.Version) {
			return fmt.Errorf("dependency %s version %s is not compatible with required version %s", 
				dep.PluginID, depMetadata.Version, dep.Version)
		}
	}
	
	return nil
}

func (r *InMemoryPluginRegistry) updateDependencyTrackingLocked(plugin Plugin) {
	pluginID := plugin.ID()
	metadata := plugin.Metadata()
	
	// Track dependencies
	var deps []PluginID
	for _, dep := range metadata.Dependencies {
		deps = append(deps, dep.PluginID)
		
		// Update dependents
		if r.dependents[dep.PluginID] == nil {
			r.dependents[dep.PluginID] = make([]PluginID, 0)
		}
		r.dependents[dep.PluginID] = append(r.dependents[dep.PluginID], pluginID)
	}
	r.dependencies[pluginID] = deps
}

func (r *InMemoryPluginRegistry) cleanupDependencyTrackingLocked(pluginID PluginID) {
	// Remove from dependencies
	deps, exists := r.dependencies[pluginID]
	if exists {
		for _, depID := range deps {
			if dependents, exists := r.dependents[depID]; exists {
				for i, id := range dependents {
					if id == pluginID {
						r.dependents[depID] = append(dependents[:i], dependents[i+1:]...)
						break
					}
				}
			}
		}
		delete(r.dependencies, pluginID)
	}
	
	// Remove from dependents
	delete(r.dependents, pluginID)
}

func (r *InMemoryPluginRegistry) resolveDependenciesRecursive(plugin Plugin, visited map[PluginID]bool, resolved *[]Plugin) error {
	pluginID := plugin.ID()
	
	if visited[pluginID] {
		return fmt.Errorf("circular dependency detected involving plugin %s", pluginID)
	}
	
	visited[pluginID] = true
	
	metadata := plugin.Metadata()
	for _, dep := range metadata.Dependencies {
		depPlugin, exists := r.plugins[dep.PluginID]
		if !exists {
			if !dep.Optional {
				return fmt.Errorf("required dependency %s not found", dep.PluginID)
			}
			continue
		}
		
		if err := r.resolveDependenciesRecursive(depPlugin, visited, resolved); err != nil {
			return err
		}
		
		// Add to resolved list if not already present
		found := false
		for _, p := range *resolved {
			if p.ID() == dep.PluginID {
				found = true
				break
			}
		}
		if !found {
			*resolved = append(*resolved, depPlugin)
		}
	}
	
	delete(visited, pluginID)
	return nil
}
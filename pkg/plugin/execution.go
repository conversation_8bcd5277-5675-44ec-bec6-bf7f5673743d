// Package plugin provides secure plugin execution interfaces
package plugin

import (
	"context"
	"fmt"
	"sync"
	"time"

	"eionz.com/demo/pkg/domain"
)

// SandboxEnvironment provides environment for plugin execution
type SandboxEnvironment struct {
	WorkingDir      string
	Environment     map[string]string
	AllowNetwork    bool
	AllowFileSystem bool
	Logger          interface{}
}

// SandboxResult represents the result of plugin execution in sandbox
type SandboxResult struct {
	Result     interface{}
	MemoryUsed int64
	CPUTime    time.Duration
	Events     []domain.DomainEvent
	Logs       []string
}

// ExecutionResult represents the result of plugin execution
type ExecutionResult struct {
	PluginID      string
	Success       bool
	Result        interface{}
	Error         error
	ExecutionTime time.Duration
	MemoryUsed    int64
	CPUTime       time.Duration
	Events        []domain.DomainEvent
	Logs          []string
	Metadata      map[string]interface{}
	ExecutedAt    time.Time
}

// ExecutionOptions configures plugin execution
type ExecutionOptions struct {
	Timeout         time.Duration
	MaxMemory       int64
	MaxCPUTime      time.Duration
	RequiredPerms   []string
	Input           interface{}
	Environment     map[string]string
	AllowNetwork    bool
	AllowFileSystem bool
	WorkingDir      string
	Logger          interface{}
}

// SecurePluginExecutor provides secure plugin execution with context and permissions
type SecurePluginExecutor interface {
	Execute(ctx context.Context, pluginID string, method string, options ExecutionOptions) (*ExecutionResult, error)
	ExecuteWithContext(pluginCtx *SecurePluginContext, method string, options ExecutionOptions) (*ExecutionResult, error)
	PreValidate(pluginID string, method string, options ExecutionOptions) error
	GetExecutionStats(pluginID string) (*ExecutionStats, error)
}

// ExecutionStats tracks plugin execution statistics
type ExecutionStats struct {
	PluginID            string
	TotalExecutions     int64
	SuccessfulExecutions int64
	FailedExecutions    int64
	AverageExecutionTime time.Duration
	TotalCPUTime        time.Duration
	TotalMemoryUsed     int64
	LastExecution       time.Time
	ErrorRate           float64
}

// DefaultSecurePluginExecutor implements SecurePluginExecutor
type DefaultSecurePluginExecutor struct {
	pluginManager   PluginManager
	securityManager *SecurityManager
	sandbox         PluginSandbox
	stats           map[string]*ExecutionStats
	mutex           sync.RWMutex
}

func NewDefaultSecurePluginExecutor(
	pluginManager PluginManager,
	securityManager *SecurityManager,
	sandbox PluginSandbox,
) *DefaultSecurePluginExecutor {
	return &DefaultSecurePluginExecutor{
		pluginManager:   pluginManager,
		securityManager: securityManager,
		sandbox:         sandbox,
		stats:           make(map[string]*ExecutionStats),
	}
}

func (exec *DefaultSecurePluginExecutor) Execute(ctx context.Context, pluginID string, method string, options ExecutionOptions) (*ExecutionResult, error) {
	// Get plugin context
	pluginCtx := exec.securityManager.GetPluginContext(pluginID)
	if pluginCtx == nil {
		return nil, fmt.Errorf("plugin context not found for plugin %s", pluginID)
	}
	
	return exec.ExecuteWithContext(pluginCtx, method, options)
}

func (exec *DefaultSecurePluginExecutor) ExecuteWithContext(pluginCtx *SecurePluginContext, method string, options ExecutionOptions) (*ExecutionResult, error) {
	startTime := time.Now()
	
	result := &ExecutionResult{
		PluginID:   pluginCtx.PluginID,
		Success:    false,
		Metadata:   make(map[string]interface{}),
		ExecutedAt: startTime,
	}
	
	// Pre-execution validation
	if err := exec.preExecutionValidation(pluginCtx, method, options); err != nil {
		result.Error = err
		exec.updateStats(pluginCtx.PluginID, result)
		return result, err
	}
	
	// Set up execution context with timeout
	execCtx, cancel := context.WithTimeout(pluginCtx.ExecutionCtx, options.Timeout)
	defer cancel()
	
	// Get plugin instance
	pluginID := NewPluginID(pluginCtx.PluginID)
	plugin, err := exec.pluginManager.GetPlugin(pluginID)
	if err != nil {
		result.Error = err
		exec.updateStats(pluginCtx.PluginID, result)
		return result, err
	}
	
	// Execute in sandbox
	sandboxResult, err := exec.executeInSandbox(execCtx, plugin, method, options, pluginCtx)
	
	// Calculate execution time
	result.ExecutionTime = time.Since(startTime)
	
	if err != nil {
		result.Error = err
		exec.updateStats(pluginCtx.PluginID, result)
		return result, err
	}
	
	// Process sandbox result
	result.Success = true
	result.Result = sandboxResult.Result
	result.MemoryUsed = sandboxResult.MemoryUsed
	result.CPUTime = sandboxResult.CPUTime
	result.Events = sandboxResult.Events
	result.Logs = sandboxResult.Logs
	
	// Update plugin activity
	pluginCtx.UpdateActivity()
	
	// Update statistics
	exec.updateStats(pluginCtx.PluginID, result)
	
	return result, nil
}

func (exec *DefaultSecurePluginExecutor) PreValidate(pluginID string, method string, options ExecutionOptions) error {
	pluginCtx := exec.securityManager.GetPluginContext(pluginID)
	if pluginCtx == nil {
		return fmt.Errorf("plugin context not found for plugin %s", pluginID)
	}
	
	return exec.preExecutionValidation(pluginCtx, method, options)
}

func (exec *DefaultSecurePluginExecutor) GetExecutionStats(pluginID string) (*ExecutionStats, error) {
	exec.mutex.RLock()
	defer exec.mutex.RUnlock()
	
	stats, exists := exec.stats[pluginID]
	if !exists {
		return nil, fmt.Errorf("no execution stats found for plugin %s", pluginID)
	}
	
	// Return a copy to prevent external modification
	return &ExecutionStats{
		PluginID:             stats.PluginID,
		TotalExecutions:      stats.TotalExecutions,
		SuccessfulExecutions: stats.SuccessfulExecutions,
		FailedExecutions:     stats.FailedExecutions,
		AverageExecutionTime: stats.AverageExecutionTime,
		TotalCPUTime:         stats.TotalCPUTime,
		TotalMemoryUsed:      stats.TotalMemoryUsed,
		LastExecution:        stats.LastExecution,
		ErrorRate:            stats.ErrorRate,
	}, nil
}

func (exec *DefaultSecurePluginExecutor) preExecutionValidation(pluginCtx *SecurePluginContext, method string, options ExecutionOptions) error {
	// Validate plugin context
	if !pluginCtx.IsValid() {
		return fmt.Errorf("plugin context is not valid")
	}
	
	// Validate required permissions
	if err := exec.securityManager.ValidatePluginExecution(pluginCtx.PluginID, options.RequiredPerms); err != nil {
		return fmt.Errorf("permission validation failed: %w", err)
	}
	
	// Validate plugin exists and is loaded
	pluginID := NewPluginID(pluginCtx.PluginID)
	plugin, err := exec.pluginManager.GetPlugin(pluginID)
	if err != nil {
		return fmt.Errorf("plugin not found or not loaded: %w", err)
	}
	
	// Validate method exists
	pluginMetadata := plugin.Metadata()
	// For now, assume all methods exist - in real implementation check plugin capabilities
	_ = pluginMetadata
	
	// Validate network access permission
	if options.AllowNetwork && !pluginCtx.SecurityCtx.HasPermission("network_access") {
		return fmt.Errorf("plugin does not have network access permission")
	}
	
	// Validate file system access permission
	if options.AllowFileSystem && !pluginCtx.SecurityCtx.HasPermission("read_files") {
		return fmt.Errorf("plugin does not have file system access permission")
	}
	
	// Validate timeout
	if options.Timeout <= 0 {
		return fmt.Errorf("timeout must be positive")
	}
	
	// Validate resource limits
	if options.MaxMemory <= 0 {
		return fmt.Errorf("max memory must be positive")
	}
	
	if options.MaxCPUTime <= 0 {
		return fmt.Errorf("max CPU time must be positive")
	}
	
	return nil
}

func (exec *DefaultSecurePluginExecutor) executeInSandbox(
	ctx context.Context,
	plugin Plugin,
	method string,
	options ExecutionOptions,
	pluginCtx PluginContext,
) (*SandboxResult, error) {
	// Prepare sandbox environment
	env := &SandboxEnvironment{
		WorkingDir:      options.WorkingDir,
		Environment:     options.Environment,
		AllowNetwork:    options.AllowNetwork,
		AllowFileSystem: options.AllowFileSystem,
		Logger:          options.Logger,
	}
	
	// Set resource limits
	limits := ResourceLimits{
		MaxMemory:        options.MaxMemory,
		MaxExecutionTime: options.Timeout,
	}
	
	// Execute plugin method in sandbox
	// For now, execute directly since we don't have sandbox execution method
	// In a real implementation, this would go through the sandbox
	
	// Note: env and limits are prepared for future sandbox integration
	_ = env    // Will be used when sandbox execution is implemented
	_ = limits // Will be used when sandbox execution is implemented
	
	// Simple execution - in reality this would be more complex
	result := map[string]interface{}{
		"message": "Plugin executed successfully",
		"input":   options.Input,
	}
	
	return &SandboxResult{
		Result:     result,
		MemoryUsed: 0,
		CPUTime:    0,
		Events:     []domain.DomainEvent{},
		Logs:       []string{"Plugin executed successfully"},
	}, nil
}

func (exec *DefaultSecurePluginExecutor) updateStats(pluginID string, result *ExecutionResult) {
	exec.mutex.Lock()
	defer exec.mutex.Unlock()
	
	stats, exists := exec.stats[pluginID]
	if !exists {
		stats = &ExecutionStats{
			PluginID: pluginID,
		}
		exec.stats[pluginID] = stats
	}
	
	stats.TotalExecutions++
	stats.LastExecution = result.ExecutedAt
	stats.TotalCPUTime += result.CPUTime
	stats.TotalMemoryUsed += result.MemoryUsed
	
	if result.Success {
		stats.SuccessfulExecutions++
	} else {
		stats.FailedExecutions++
	}
	
	// Calculate average execution time
	if stats.TotalExecutions > 0 {
		totalTime := time.Duration(0)
		for _, s := range exec.stats {
			if s.PluginID == pluginID {
				totalTime += result.ExecutionTime
			}
		}
		stats.AverageExecutionTime = totalTime / time.Duration(stats.TotalExecutions)
	}
	
	// Calculate error rate
	if stats.TotalExecutions > 0 {
		stats.ErrorRate = float64(stats.FailedExecutions) / float64(stats.TotalExecutions)
	}
}

// PluginExecutionAuditor tracks and audits plugin executions
type PluginExecutionAuditor struct {
	executions []ExecutionAuditEntry
	mutex      sync.RWMutex
}

// ExecutionAuditEntry represents an audit entry for plugin execution
type ExecutionAuditEntry struct {
	ID            string
	PluginID      string
	Method        string
	UserID        string
	SessionID     string
	ExecutedAt    time.Time
	Success       bool
	Error         string
	ExecutionTime time.Duration
	MemoryUsed    int64
	CPUTime       time.Duration
	Permissions   []string
	IPAddress     string
	UserAgent     string
}

func NewPluginExecutionAuditor() *PluginExecutionAuditor {
	return &PluginExecutionAuditor{
		executions: make([]ExecutionAuditEntry, 0),
	}
}

func (auditor *PluginExecutionAuditor) AuditExecution(
	pluginCtx *SecurePluginContext,
	method string,
	result *ExecutionResult,
) {
	auditor.mutex.Lock()
	defer auditor.mutex.Unlock()
	
	entry := ExecutionAuditEntry{
		ID:            fmt.Sprintf("audit-%d", time.Now().UnixNano()),
		PluginID:      result.PluginID,
		Method:        method,
		UserID:        pluginCtx.SecurityCtx.UserID,
		SessionID:     pluginCtx.SecurityCtx.SessionID,
		ExecutedAt:    result.ExecutedAt,
		Success:       result.Success,
		ExecutionTime: result.ExecutionTime,
		MemoryUsed:    result.MemoryUsed,
		CPUTime:       result.CPUTime,
		IPAddress:     pluginCtx.SecurityCtx.IPAddress,
		UserAgent:     pluginCtx.SecurityCtx.UserAgent,
	}
	
	if result.Error != nil {
		entry.Error = result.Error.Error()
	}
	
	// Collect permissions
	permissions := pluginCtx.SecurityCtx.PermissionSet.List()
	entry.Permissions = make([]string, len(permissions))
	for i, perm := range permissions {
		entry.Permissions[i] = perm.ID
	}
	
	auditor.executions = append(auditor.executions, entry)
}

func (auditor *PluginExecutionAuditor) GetAuditLog(limit int) []ExecutionAuditEntry {
	auditor.mutex.RLock()
	defer auditor.mutex.RUnlock()
	
	if limit <= 0 || limit > len(auditor.executions) {
		limit = len(auditor.executions)
	}
	
	// Return the most recent entries
	start := len(auditor.executions) - limit
	if start < 0 {
		start = 0
	}
	
	entries := make([]ExecutionAuditEntry, limit)
	copy(entries, auditor.executions[start:])
	
	return entries
}

func (auditor *PluginExecutionAuditor) GetAuditLogForPlugin(pluginID string, limit int) []ExecutionAuditEntry {
	auditor.mutex.RLock()
	defer auditor.mutex.RUnlock()
	
	var pluginEntries []ExecutionAuditEntry
	for _, entry := range auditor.executions {
		if entry.PluginID == pluginID {
			pluginEntries = append(pluginEntries, entry)
		}
	}
	
	if limit <= 0 || limit > len(pluginEntries) {
		return pluginEntries
	}
	
	// Return the most recent entries
	start := len(pluginEntries) - limit
	if start < 0 {
		start = 0
	}
	
	return pluginEntries[start:]
}

func (auditor *PluginExecutionAuditor) GetAuditLogForUser(userID string, limit int) []ExecutionAuditEntry {
	auditor.mutex.RLock()
	defer auditor.mutex.RUnlock()
	
	var userEntries []ExecutionAuditEntry
	for _, entry := range auditor.executions {
		if entry.UserID == userID {
			userEntries = append(userEntries, entry)
		}
	}
	
	if limit <= 0 || limit > len(userEntries) {
		return userEntries
	}
	
	// Return the most recent entries
	start := len(userEntries) - limit
	if start < 0 {
		start = 0
	}
	
	return userEntries[start:]
}

func (auditor *PluginExecutionAuditor) ClearAuditLog() {
	auditor.mutex.Lock()
	defer auditor.mutex.Unlock()
	
	auditor.executions = make([]ExecutionAuditEntry, 0)
}

func (auditor *PluginExecutionAuditor) GetStats() map[string]interface{} {
	auditor.mutex.RLock()
	defer auditor.mutex.RUnlock()
	
	totalExecutions := len(auditor.executions)
	successfulExecutions := 0
	
	for _, entry := range auditor.executions {
		if entry.Success {
			successfulExecutions++
		}
	}
	
	errorRate := 0.0
	if totalExecutions > 0 {
		errorRate = float64(totalExecutions-successfulExecutions) / float64(totalExecutions)
	}
	
	return map[string]interface{}{
		"total_executions":      totalExecutions,
		"successful_executions": successfulExecutions,
		"failed_executions":     totalExecutions - successfulExecutions,
		"error_rate":            errorRate,
	}
}
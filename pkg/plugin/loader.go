package plugin

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"plugin"
	"reflect"
	"sync"
	"time"
)

// PluginLoader handles loading and unloading of plugins
type DefaultPluginLoader struct {
	loadedPlugins map[PluginID]*LoadedPlugin
	sandbox       PluginSandbox
	registry      PluginRegistry
	mu            sync.RWMutex
	
	// Configuration
	pluginDirectory string
	hotReloadEnabled bool
	watchInterval   time.Duration
	
	// File watching
	fileWatcher *FileWatcher
	stopWatch   chan bool
	
	// Lifecycle hooks
	beforeLoad   []func(PluginID) error
	afterLoad    []func(Plugin) error
	beforeUnload []func(PluginID) error
	afterUnload  []func(PluginID) error
}

// LoadedPlugin represents a loaded plugin with metadata
type LoadedPlugin struct {
	Plugin       Plugin
	LoadPath     string
	LoadTime     time.Time
	Version      PluginVersion
	Sandbox      Sandbox
	Handle       *plugin.Plugin // Go plugin handle
	LastModified time.Time
	Dependencies []PluginID
	Dependents   []PluginID
}

// FileWatcher monitors plugin files for changes
type FileWatcher struct {
	watchedFiles map[string]time.Time
	mu           sync.RWMutex
}

// NewDefaultPluginLoader creates a new plugin loader
func NewDefaultPluginLoader(sandbox PluginSandbox, registry PluginRegistry, pluginDirectory string) *DefaultPluginLoader {
	return &DefaultPluginLoader{
		loadedPlugins:    make(map[PluginID]*LoadedPlugin),
		sandbox:          sandbox,
		registry:         registry,
		pluginDirectory:  pluginDirectory,
		hotReloadEnabled: false,
		watchInterval:    5 * time.Second,
		fileWatcher:      NewFileWatcher(),
		stopWatch:        make(chan bool, 1),
		beforeLoad:       make([]func(PluginID) error, 0),
		afterLoad:        make([]func(Plugin) error, 0),
		beforeUnload:     make([]func(PluginID) error, 0),
		afterUnload:      make([]func(PluginID) error, 0),
	}
}

func (dl *DefaultPluginLoader) LoadFromPath(path string) (Plugin, error) {
	// Validate path
	if !filepath.IsAbs(path) {
		path = filepath.Join(dl.pluginDirectory, path)
	}
	
	info, err := os.Stat(path)
	if err != nil {
		return nil, fmt.Errorf("plugin file not found: %w", err)
	}
	
	// For Go plugins, we expect .so files
	if filepath.Ext(path) != ".so" {
		return nil, fmt.Errorf("unsupported plugin format: %s (expected .so)", filepath.Ext(path))
	}
	
	// Load the plugin
	handle, err := plugin.Open(path)
	if err != nil {
		return nil, fmt.Errorf("failed to load plugin: %w", err)
	}
	
	// Look for the required plugin symbol
	pluginSymbol, err := handle.Lookup("PluginInstance")
	if err != nil {
		return nil, fmt.Errorf("plugin missing PluginInstance symbol: %w", err)
	}
	
	// Verify the symbol implements the Plugin interface
	pluginInstance, ok := pluginSymbol.(Plugin)
	if !ok {
		return nil, fmt.Errorf("PluginInstance does not implement Plugin interface")
	}
	
	pluginID := pluginInstance.ID()
	
	// Run before load hooks
	for _, hook := range dl.beforeLoad {
		if err := hook(pluginID); err != nil {
			return nil, fmt.Errorf("before load hook failed: %w", err)
		}
	}
	
	// Check if plugin is already loaded
	dl.mu.RLock()
	if existing, exists := dl.loadedPlugins[pluginID]; exists {
		dl.mu.RUnlock()
		return existing.Plugin, fmt.Errorf("plugin %s is already loaded", pluginID)
	}
	dl.mu.RUnlock()
	
	// Create sandbox for the plugin
	sandboxConfig := dl.createSandboxConfig(pluginInstance)
	sandbox, err := dl.sandbox.CreateSandbox(pluginID, sandboxConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create sandbox: %w", err)
	}
	
	// Initialize the plugin in sandbox
	err = sandbox.Execute(context.Background(), func() error {
		return pluginInstance.Initialize(dl.createPluginContext(pluginID, sandbox))
	})
	if err != nil {
		dl.sandbox.DestroySandbox(sandbox.ID())
		return nil, fmt.Errorf("plugin initialization failed: %w", err)
	}
	
	// Create loaded plugin entry
	loadedPlugin := &LoadedPlugin{
		Plugin:       pluginInstance,
		LoadPath:     path,
		LoadTime:     time.Now(),
		Version:      pluginInstance.Metadata().Version,
		Sandbox:      sandbox,
		Handle:       handle,
		LastModified: info.ModTime(),
		Dependencies: make([]PluginID, 0),
		Dependents:   make([]PluginID, 0),
	}
	
	// Register dependencies
	metadata := pluginInstance.Metadata()
	for _, dep := range metadata.Dependencies {
		loadedPlugin.Dependencies = append(loadedPlugin.Dependencies, dep.PluginID)
	}
	
	// Store loaded plugin
	dl.mu.Lock()
	dl.loadedPlugins[pluginID] = loadedPlugin
	dl.mu.Unlock()
	
	// Register with registry
	if err := dl.registry.Register(pluginInstance); err != nil {
		dl.mu.Lock()
		delete(dl.loadedPlugins, pluginID)
		dl.mu.Unlock()
		dl.sandbox.DestroySandbox(sandbox.ID())
		return nil, fmt.Errorf("failed to register plugin: %w", err)
	}
	
	// Start file watching if hot reload is enabled
	if dl.hotReloadEnabled {
		dl.fileWatcher.WatchFile(path, info.ModTime())
	}
	
	// Run after load hooks
	for _, hook := range dl.afterLoad {
		if err := hook(pluginInstance); err != nil {
			// Log error but don't fail the load
			// In production, you'd use a proper logger
			fmt.Printf("Warning: after load hook failed: %v\n", err)
		}
	}
	
	return pluginInstance, nil
}

func (dl *DefaultPluginLoader) LoadFromRepository(repo Repository, pluginID PluginID, version PluginVersion) (Plugin, error) {
	// Download plugin from repository
	data, err := repo.Download(pluginID, version)
	if err != nil {
		return nil, fmt.Errorf("failed to download plugin: %w", err)
	}
	
	// Get metadata
	metadata, err := repo.GetMetadata(pluginID, version)
	if err != nil {
		return nil, fmt.Errorf("failed to get metadata: %w", err)
	}
	
	return dl.LoadFromBytes(data, metadata)
}

func (dl *DefaultPluginLoader) LoadFromBytes(data []byte, metadata PluginMetadata) (Plugin, error) {
	// Create temporary file
	tempFile := filepath.Join(dl.pluginDirectory, fmt.Sprintf("%s-%s.so", 
		metadata.ID.String(), metadata.Version.String()))
	
	if err := os.WriteFile(tempFile, data, 0755); err != nil {
		return nil, fmt.Errorf("failed to write plugin file: %w", err)
	}
	
	// Load from the temporary file
	plugin, err := dl.LoadFromPath(tempFile)
	if err != nil {
		os.Remove(tempFile) // Cleanup on failure
		return nil, err
	}
	
	return plugin, nil
}

func (dl *DefaultPluginLoader) Unload(pluginID PluginID) error {
	dl.mu.Lock()
	defer dl.mu.Unlock()
	
	loadedPlugin, exists := dl.loadedPlugins[pluginID]
	if !exists {
		return fmt.Errorf("plugin %s is not loaded", pluginID)
	}
	
	// Check for dependents
	if len(loadedPlugin.Dependents) > 0 {
		return fmt.Errorf("cannot unload plugin %s: %d plugins depend on it", 
			pluginID, len(loadedPlugin.Dependents))
	}
	
	// Run before unload hooks
	for _, hook := range dl.beforeUnload {
		if err := hook(pluginID); err != nil {
			return fmt.Errorf("before unload hook failed: %w", err)
		}
	}
	
	// Stop the plugin in sandbox
	err := loadedPlugin.Sandbox.Execute(context.Background(), func() error {
		return loadedPlugin.Plugin.Stop()
	})
	if err != nil {
		// Log error but continue with unload
		fmt.Printf("Warning: plugin stop failed: %v\n", err)
	}
	
	// Cleanup the plugin
	err = loadedPlugin.Sandbox.Execute(context.Background(), func() error {
		return loadedPlugin.Plugin.Cleanup()
	})
	if err != nil {
		// Log error but continue with unload
		fmt.Printf("Warning: plugin cleanup failed: %v\n", err)
	}
	
	// Destroy sandbox
	if err := dl.sandbox.DestroySandbox(loadedPlugin.Sandbox.ID()); err != nil {
		fmt.Printf("Warning: sandbox cleanup failed: %v\n", err)
	}
	
	// Unregister from registry
	if err := dl.registry.Unregister(pluginID); err != nil {
		fmt.Printf("Warning: registry unregister failed: %v\n", err)
	}
	
	// Stop watching file
	if dl.hotReloadEnabled {
		dl.fileWatcher.UnwatchFile(loadedPlugin.LoadPath)
	}
	
	// Remove from loaded plugins
	delete(dl.loadedPlugins, pluginID)
	
	// Run after unload hooks
	for _, hook := range dl.afterUnload {
		if err := hook(pluginID); err != nil {
			fmt.Printf("Warning: after unload hook failed: %v\n", err)
		}
	}
	
	return nil
}

func (dl *DefaultPluginLoader) Reload(pluginID PluginID) error {
	dl.mu.RLock()
	loadedPlugin, exists := dl.loadedPlugins[pluginID]
	if !exists {
		dl.mu.RUnlock()
		return fmt.Errorf("plugin %s is not loaded", pluginID)
	}
	
	loadPath := loadedPlugin.LoadPath
	dl.mu.RUnlock()
	
	if !dl.CanReload(pluginID) {
		return fmt.Errorf("plugin %s cannot be reloaded", pluginID)
	}
	
	// Unload the plugin
	if err := dl.Unload(pluginID); err != nil {
		return fmt.Errorf("failed to unload plugin for reload: %w", err)
	}
	
	// Load it again
	_, err := dl.LoadFromPath(loadPath)
	if err != nil {
		return fmt.Errorf("failed to reload plugin: %w", err)
	}
	
	return nil
}

func (dl *DefaultPluginLoader) CanReload(pluginID PluginID) bool {
	dl.mu.RLock()
	defer dl.mu.RUnlock()
	
	loadedPlugin, exists := dl.loadedPlugins[pluginID]
	if !exists {
		return false
	}
	
	// Check if plugin has dependents
	if len(loadedPlugin.Dependents) > 0 {
		return false
	}
	
	// Check if plugin supports hot reload (based on capabilities)
	capabilities := loadedPlugin.Plugin.GetCapabilities()
	for _, cap := range capabilities {
		if cap == "hot-reload" {
			return true
		}
	}
	
	return false
}

func (dl *DefaultPluginLoader) ValidatePlugin(plugin Plugin) error {
	// Basic validation
	if plugin == nil {
		return fmt.Errorf("plugin cannot be nil")
	}
	
	if plugin.ID().String() == "" {
		return fmt.Errorf("plugin ID cannot be empty")
	}
	
	metadata := plugin.Metadata()
	if metadata.Name == "" {
		return fmt.Errorf("plugin name cannot be empty")
	}
	
	if metadata.Version.String() == "" {
		return fmt.Errorf("plugin version cannot be empty")
	}
	
	// Validate configuration schema
	configSchema := plugin.GetConfigSchema()
	if err := dl.validateConfigSchema(configSchema); err != nil {
		return fmt.Errorf("invalid config schema: %w", err)
	}
	
	return nil
}

func (dl *DefaultPluginLoader) GetLoadedPlugins() []PluginID {
	dl.mu.RLock()
	defer dl.mu.RUnlock()
	
	pluginIDs := make([]PluginID, 0, len(dl.loadedPlugins))
	for pluginID := range dl.loadedPlugins {
		pluginIDs = append(pluginIDs, pluginID)
	}
	
	return pluginIDs
}

// Hot reload functionality

func (dl *DefaultPluginLoader) EnableHotReload(enabled bool) {
	dl.mu.Lock()
	defer dl.mu.Unlock()
	
	dl.hotReloadEnabled = enabled
	
	if enabled && dl.fileWatcher != nil {
		go dl.watchForChanges()
	} else if !enabled {
		select {
		case dl.stopWatch <- true:
		default:
		}
	}
}

func (dl *DefaultPluginLoader) SetWatchInterval(interval time.Duration) {
	dl.mu.Lock()
	defer dl.mu.Unlock()
	
	dl.watchInterval = interval
}

func (dl *DefaultPluginLoader) watchForChanges() {
	ticker := time.NewTicker(dl.watchInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			dl.checkForFileChanges()
			
		case <-dl.stopWatch:
			return
		}
	}
}

func (dl *DefaultPluginLoader) checkForFileChanges() {
	changedFiles := dl.fileWatcher.GetChangedFiles()
	
	for filePath := range changedFiles {
		// Find plugin by file path
		var pluginID PluginID
		dl.mu.RLock()
		for id, loaded := range dl.loadedPlugins {
			if loaded.LoadPath == filePath {
				pluginID = id
				break
			}
		}
		dl.mu.RUnlock()
		
		if pluginID.String() != "" && dl.CanReload(pluginID) {
			go func(id PluginID) {
				if err := dl.Reload(id); err != nil {
					fmt.Printf("Hot reload failed for plugin %s: %v\n", id, err)
				} else {
					fmt.Printf("Hot reloaded plugin %s\n", id)
				}
			}(pluginID)
		}
	}
}

// Hook management

func (dl *DefaultPluginLoader) AddBeforeLoadHook(hook func(PluginID) error) {
	dl.mu.Lock()
	defer dl.mu.Unlock()
	
	dl.beforeLoad = append(dl.beforeLoad, hook)
}

func (dl *DefaultPluginLoader) AddAfterLoadHook(hook func(Plugin) error) {
	dl.mu.Lock()
	defer dl.mu.Unlock()
	
	dl.afterLoad = append(dl.afterLoad, hook)
}

func (dl *DefaultPluginLoader) AddBeforeUnloadHook(hook func(PluginID) error) {
	dl.mu.Lock()
	defer dl.mu.Unlock()
	
	dl.beforeUnload = append(dl.beforeUnload, hook)
}

func (dl *DefaultPluginLoader) AddAfterUnloadHook(hook func(PluginID) error) {
	dl.mu.Lock()
	defer dl.mu.Unlock()
	
	dl.afterUnload = append(dl.afterUnload, hook)
}

// Helper methods

func (dl *DefaultPluginLoader) createSandboxConfig(plugin Plugin) SandboxConfig {
	return SandboxConfig{
		ResourceLimits: ResourceLimits{
			MaxMemory:        100 * 1024 * 1024, // 100MB default
			MaxExecutionTime: 30 * time.Second,   // 30s default
		},
		Permissions:      []string{"*"}, // Grant all permissions by default
		NetworkAccess:    true,
		FileSystemAccess: true,
		AllowedPaths:     []string{dl.pluginDirectory},
		Environment:      make(map[string]string),
		Timeout:          60 * time.Second,
	}
}

func (dl *DefaultPluginLoader) createPluginContext(pluginID PluginID, sandbox Sandbox) PluginContext {
	// This would be implemented based on the actual PluginContext interface
	// For now, return nil as we haven't implemented PluginContext yet
	return nil
}

func (dl *DefaultPluginLoader) validateConfigSchema(schema ConfigSchema) error {
	// Validate that all required properties are defined
	for _, required := range schema.Required {
		if _, exists := schema.Properties[required]; !exists {
			return fmt.Errorf("required property %s not defined in schema", required)
		}
	}
	
	// Validate property schemas
	for name, prop := range schema.Properties {
		if err := dl.validatePropertySchema(name, prop); err != nil {
			return err
		}
	}
	
	return nil
}

func (dl *DefaultPluginLoader) validatePropertySchema(name string, prop PropertySchema) error {
	validTypes := map[string]bool{
		"string": true, "number": true, "boolean": true, "array": true, "object": true,
	}
	
	if !validTypes[prop.Type] {
		return fmt.Errorf("invalid type %s for property %s", prop.Type, name)
	}
	
	// Validate enum values if present
	if len(prop.Enum) > 0 && prop.Default != nil {
		validDefault := false
		for _, enumValue := range prop.Enum {
			if reflect.DeepEqual(prop.Default, enumValue) {
				validDefault = true
				break
			}
		}
		if !validDefault {
			return fmt.Errorf("default value for property %s is not in enum", name)
		}
	}
	
	return nil
}

// FileWatcher implementation

func NewFileWatcher() *FileWatcher {
	return &FileWatcher{
		watchedFiles: make(map[string]time.Time),
	}
}

func (fw *FileWatcher) WatchFile(path string, modTime time.Time) {
	fw.mu.Lock()
	defer fw.mu.Unlock()
	
	fw.watchedFiles[path] = modTime
}

func (fw *FileWatcher) UnwatchFile(path string) {
	fw.mu.Lock()
	defer fw.mu.Unlock()
	
	delete(fw.watchedFiles, path)
}

func (fw *FileWatcher) GetChangedFiles() map[string]time.Time {
	fw.mu.RLock()
	defer fw.mu.RUnlock()
	
	changedFiles := make(map[string]time.Time)
	
	for path, lastModTime := range fw.watchedFiles {
		if info, err := os.Stat(path); err == nil {
			if info.ModTime().After(lastModTime) {
				changedFiles[path] = info.ModTime()
				// Update the stored modification time
				fw.watchedFiles[path] = info.ModTime()
			}
		}
	}
	
	return changedFiles
}
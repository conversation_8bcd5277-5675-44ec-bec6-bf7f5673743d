package plugin

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"
)

// MockPlugin for testing
type MockPlugin struct {
	id           PluginID
	metadata     PluginMetadata
	capabilities []string
	configSchema ConfigSchema
	initialized  bool
	started      bool
	stopped      bool
	healthy      bool
	lastError    error
}

func NewMockPlugin(id string, version PluginVersion) *MockPlugin {
	pluginID := NewPluginID(id)
	return &MockPlugin{
		id: pluginID,
		metadata: PluginMetadata{
			ID:          pluginID,
			Name:        id,
			Description: "Mock plugin for testing",
			Version:     version,
			Author:      "Test",
			License:     "MIT",
		},
		capabilities: []string{"test"},
		configSchema: ConfigSchema{
			Properties: make(map[string]PropertySchema),
			Required:   []string{},
		},
		healthy: true,
	}
}

func (mp *MockPlugin) ID() PluginID {
	return mp.id
}

func (mp *MockPlugin) Metadata() PluginMetadata {
	return mp.metadata
}

func (mp *MockPlugin) Initialize(ctx PluginContext) error {
	mp.initialized = true
	return nil
}

func (mp *MockPlugin) Start() error {
	if !mp.initialized {
		return fmt.Errorf("plugin not initialized")
	}
	mp.started = true
	return nil
}

func (mp *MockPlugin) Stop() error {
	mp.stopped = true
	mp.started = false
	return nil
}

func (mp *MockPlugin) Cleanup() error {
	mp.initialized = false
	return nil
}

func (mp *MockPlugin) IsHealthy() bool {
	return mp.healthy
}

func (mp *MockPlugin) GetStatus() PluginStatus {
	state := PluginStateActive
	if !mp.initialized {
		state = PluginStateUnloaded
	} else if !mp.started {
		state = PluginStateLoaded
	}

	return PluginStatus{
		State:      state,
		Message:    "Mock plugin status",
		LastUpdate: time.Now(),
		Uptime:     time.Since(time.Now()),
	}
}

func (mp *MockPlugin) GetLastError() error {
	return mp.lastError
}

func (mp *MockPlugin) GetConfigSchema() ConfigSchema {
	return mp.configSchema
}

func (mp *MockPlugin) ValidateConfig(config map[string]interface{}) error {
	return nil
}

func (mp *MockPlugin) UpdateConfig(config map[string]interface{}) error {
	return nil
}

func (mp *MockPlugin) GetCapabilities() []string {
	return mp.capabilities
}

func (mp *MockPlugin) HasCapability(capability string) bool {
	for _, cap := range mp.capabilities {
		if cap == capability {
			return true
		}
	}
	return false
}

func (mp *MockPlugin) SetHealthy(healthy bool) {
	mp.healthy = healthy
}

func (mp *MockPlugin) SetLastError(err error) {
	mp.lastError = err
}

func (mp *MockPlugin) AddCapability(capability string) {
	mp.capabilities = append(mp.capabilities, capability)
}

// Mock Repository for testing
type MockRepository struct {
	plugins map[string]map[string]PluginInfo // pluginID -> version -> info
}

func NewMockRepository() *MockRepository {
	return &MockRepository{
		plugins: make(map[string]map[string]PluginInfo),
	}
}

func (mr *MockRepository) Search(query string) ([]PluginInfo, error) {
	var results []PluginInfo
	for _, versions := range mr.plugins {
		for _, info := range versions {
			if info.Metadata.Name == query {
				results = append(results, info)
			}
		}
	}
	return results, nil
}

func (mr *MockRepository) GetPlugin(pluginID PluginID, version PluginVersion) (PluginInfo, error) {
	versions, exists := mr.plugins[pluginID.String()]
	if !exists {
		return PluginInfo{}, fmt.Errorf("plugin %s not found", pluginID)
	}

	info, exists := versions[version.String()]
	if !exists {
		return PluginInfo{}, fmt.Errorf("version %s not found for plugin %s", version, pluginID)
	}

	return info, nil
}

func (mr *MockRepository) ListVersions(pluginID PluginID) ([]PluginVersion, error) {
	versions, exists := mr.plugins[pluginID.String()]
	if !exists {
		return nil, fmt.Errorf("plugin %s not found", pluginID)
	}

	var result []PluginVersion
	for range versions {
		// Parse version string back to PluginVersion
		// For simplicity, assume it's in format "major.minor.patch"
		result = append(result, NewPluginVersion(1, 0, 0)) // Simplified
	}

	return result, nil
}

func (mr *MockRepository) Download(pluginID PluginID, version PluginVersion) ([]byte, error) {
	_, err := mr.GetPlugin(pluginID, version)
	if err != nil {
		return nil, err
	}

	// Return mock plugin binary data
	return []byte(fmt.Sprintf("mock-plugin-data-%s-%s", pluginID, version)), nil
}

func (mr *MockRepository) GetMetadata(
	pluginID PluginID,
	version PluginVersion,
) (PluginMetadata, error) {
	info, err := mr.GetPlugin(pluginID, version)
	if err != nil {
		return PluginMetadata{}, err
	}

	return info.Metadata, nil
}

func (mr *MockRepository) GetRepositoryInfo() RepositoryInfo {
	return RepositoryInfo{
		Name:        "Mock Repository",
		URL:         "mock://repository",
		Description: "Mock repository for testing",
		PluginCount: len(mr.plugins),
		LastSync:    time.Now(),
		Type:        "mock",
		Trusted:     true,
	}
}

func (mr *MockRepository) Refresh() error {
	return nil
}

func (mr *MockRepository) AddPlugin(pluginID PluginID, version PluginVersion, info PluginInfo) {
	if mr.plugins[pluginID.String()] == nil {
		mr.plugins[pluginID.String()] = make(map[string]PluginInfo)
	}
	mr.plugins[pluginID.String()][version.String()] = info
}

// Tests

func TestDefaultPluginLoader_Creation(t *testing.T) {
	sandbox := NewInMemoryPluginSandbox()
	registry := NewInMemoryPluginRegistry()
	pluginDir := "/tmp/plugins"

	loader := NewDefaultPluginLoader(sandbox, registry, pluginDir)

	if loader == nil {
		t.Fatal("Loader should not be nil")
	}

	if len(loader.GetLoadedPlugins()) != 0 {
		t.Error("Initial loaded plugins should be empty")
	}
}

func TestDefaultPluginLoader_ValidatePlugin(t *testing.T) {
	sandbox := NewInMemoryPluginSandbox()
	registry := NewInMemoryPluginRegistry()
	loader := NewDefaultPluginLoader(sandbox, registry, "/tmp")

	// Test valid plugin
	plugin := NewMockPlugin("test-plugin", NewPluginVersion(1, 0, 0))
	err := loader.ValidatePlugin(plugin)
	if err != nil {
		t.Errorf("Valid plugin should pass validation: %v", err)
	}

	// Test nil plugin
	err = loader.ValidatePlugin(nil)
	if err == nil {
		t.Error("Nil plugin should fail validation")
	}

	// Test plugin with empty ID
	invalidPlugin := NewMockPlugin("", NewPluginVersion(1, 0, 0))
	err = loader.ValidatePlugin(invalidPlugin)
	if err == nil {
		t.Error("Plugin with empty ID should fail validation")
	}
}

func TestDefaultPluginLoader_LoadFromRepository(t *testing.T) {
	sandbox := NewInMemoryPluginSandbox()
	registry := NewInMemoryPluginRegistry()

	// Create temporary directory for plugins
	tmpDir, err := os.MkdirTemp("", "plugin-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir)

	loader := NewDefaultPluginLoader(sandbox, registry, tmpDir)

	// Create mock repository
	repo := NewMockRepository()
	pluginID := NewPluginID("test-plugin")
	version := NewPluginVersion(1, 0, 0)

	metadata := PluginMetadata{
		ID:      pluginID,
		Name:    "Test Plugin",
		Version: version,
	}

	info := PluginInfo{
		Metadata: metadata,
	}

	repo.AddPlugin(pluginID, version, info)

	// Test loading from repository
	_, err = loader.LoadFromRepository(repo, pluginID, version)
	// This will fail because we're not creating a valid .so file,
	// but we can test that the download worked
	if err == nil {
		t.Error("Expected error when loading invalid plugin file")
	}

	// Check that the file was created
	expectedPath := filepath.Join(
		tmpDir,
		fmt.Sprintf("%s-%s.so", pluginID.String(), version.String()),
	)
	if _, err := os.Stat(expectedPath); os.IsNotExist(err) {
		t.Error("Plugin file should have been created")
	}
}

func TestDefaultPluginLoader_HotReload(t *testing.T) {
	sandbox := NewInMemoryPluginSandbox()
	registry := NewInMemoryPluginRegistry()
	loader := NewDefaultPluginLoader(sandbox, registry, "/tmp")

	// Test enabling hot reload
	loader.EnableHotReload(true)
	loader.SetWatchInterval(100 * time.Millisecond)

	// Test that hot reload is enabled (no direct way to test, but no errors should occur)
	// In a real test, you would create a plugin file, load it, modify it, and verify reload

	// Test disabling hot reload
	loader.EnableHotReload(false)
}

var (
	beforeLoadCalled   bool
	afterLoadCalled    bool
	beforeUnloadCalled bool
	afterUnloadCalled  bool
)

func TestDefaultPluginLoader_Hooks(t *testing.T) {
	sandbox := NewInMemoryPluginSandbox()
	registry := NewInMemoryPluginRegistry()
	loader := NewDefaultPluginLoader(sandbox, registry, "/tmp")

	// Add hooks
	loader.AddBeforeLoadHook(func(pluginID PluginID) error {
		beforeLoadCalled = true
		return nil
	})

	loader.AddAfterLoadHook(func(plugin Plugin) error {
		afterLoadCalled = true
		return nil
	})

	loader.AddBeforeUnloadHook(func(pluginID PluginID) error {
		beforeUnloadCalled = true
		return nil
	})

	loader.AddAfterUnloadHook(func(pluginID PluginID) error {
		afterUnloadCalled = true
		return nil
	})

	// Verify hooks were added (we can't easily test execution without a real plugin)
	if len(loader.beforeLoad) != 1 {
		t.Error("Before load hook should be added")
	}

	if len(loader.afterLoad) != 1 {
		t.Error("After load hook should be added")
	}

	if len(loader.beforeUnload) != 1 {
		t.Error("Before unload hook should be added")
	}

	if len(loader.afterUnload) != 1 {
		t.Error("After unload hook should be added")
	}
}

func TestFileWatcher(t *testing.T) {
	watcher := NewFileWatcher()

	// Create a temporary file
	tmpFile, err := os.CreateTemp("", "watch-test-*")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())
	defer tmpFile.Close()

	// Get initial mod time
	info, err := tmpFile.Stat()
	if err != nil {
		t.Fatalf("Failed to get file info: %v", err)
	}

	initialModTime := info.ModTime()

	// Start watching
	watcher.WatchFile(tmpFile.Name(), initialModTime)

	// Check no changes initially
	changes := watcher.GetChangedFiles()
	if len(changes) != 0 {
		t.Errorf("Expected no changes initially, got %d", len(changes))
	}

	// Modify the file
	time.Sleep(10 * time.Millisecond) // Ensure different mod time
	tmpFile.WriteString("modified content")
	tmpFile.Sync()

	// Check for changes
	changes = watcher.GetChangedFiles()
	if len(changes) != 1 {
		t.Errorf("Expected 1 change, got %d", len(changes))
	}

	if _, exists := changes[tmpFile.Name()]; !exists {
		t.Error("File should be in changed files list")
	}

	// Unwatch file
	watcher.UnwatchFile(tmpFile.Name())

	// Modify again
	tmpFile.WriteString("more content")
	tmpFile.Sync()

	// Should not detect changes after unwatching
	changes = watcher.GetChangedFiles()
	if len(changes) != 0 {
		t.Errorf("Expected no changes after unwatching, got %d", len(changes))
	}
}

func TestDefaultPluginLoader_CanReload(t *testing.T) {
	sandbox := NewInMemoryPluginSandbox()
	registry := NewInMemoryPluginRegistry()
	loader := NewDefaultPluginLoader(sandbox, registry, "/tmp")

	// Test with non-existent plugin
	pluginID := NewPluginID("non-existent")
	if loader.CanReload(pluginID) {
		t.Error("Non-existent plugin should not be reloadable")
	}

	// Test with plugin that doesn't support hot reload
	plugin := NewMockPlugin("test-plugin", NewPluginVersion(1, 0, 0))

	loadedPlugin := &LoadedPlugin{
		Plugin:     plugin,
		Dependents: []PluginID{}, // No dependents
	}

	loader.mu.Lock()
	loader.loadedPlugins[plugin.ID()] = loadedPlugin
	loader.mu.Unlock()

	if loader.CanReload(plugin.ID()) {
		t.Error("Plugin without hot-reload capability should not be reloadable")
	}

	// Test with plugin that supports hot reload
	plugin.AddCapability("hot-reload")

	if !loader.CanReload(plugin.ID()) {
		t.Error("Plugin with hot-reload capability should be reloadable")
	}

	// Test with plugin that has dependents
	loadedPlugin.Dependents = []PluginID{NewPluginID("dependent-plugin")}

	if loader.CanReload(plugin.ID()) {
		t.Error("Plugin with dependents should not be reloadable")
	}
}


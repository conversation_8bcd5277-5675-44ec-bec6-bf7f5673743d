package loader

import (
	"context"
	"errors"
	"os"
	"path/filepath"
	"testing"
	"time"
)

// TestFileContentLoader_Load_Success tests successful file loading
func TestFileContentLoader_Load_Success(t *testing.T) {
	// Create a temporary file with test content
	content := "Test file content"
	tmpFile, err := os.CreateTemp("", "test-file-*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.Write([]byte(content)); err != nil {
		t.Fatalf("Failed to write to temp file: %v", err)
	}
	if err := tmpFile.Close(); err != nil {
		t.Fatalf("Failed to close temp file: %v", err)
	}

	// Create a file loader with no delay for faster testing
	loader := NewFileContentLoader(tmpFile.Name())
	loader.Delay = 0

	// Test initial state
	if loader.IsLoading() {
		t.Error("Loader should not be in loading state initially")
	}
	if loader.Error() != nil {
		t.<PERSON>rf("Loader should not have error initially, got: %v", loader.Error())
	}
	if loader.Content() != "" {
		t.Errorf("Loader should not have content initially, got: %s", loader.Content())
	}

	// Load the content
	ctx := context.Background()
	resultCh := loader.Load(ctx)

	// Wait for the result
	result := <-resultCh

	// Verify the result
	if result.Err != nil {
		t.Errorf("Expected no error, got: %v", result.Err)
	}
	if result.Content != content {
		t.Errorf("Expected content: %s, got: %s", content, result.Content)
	}

	// Verify the loader state
	if loader.IsLoading() {
		t.Error("Loader should not be in loading state after completion")
	}
	if loader.Error() != nil {
		t.Errorf("Loader should not have error after successful load, got: %v", loader.Error())
	}
	if loader.Content() != content {
		t.Errorf("Loader content should match, expected: %s, got: %s", content, loader.Content())
	}
}

// TestFileContentLoader_Load_FileNotFound tests file loading when file doesn't exist
func TestFileContentLoader_Load_FileNotFound(t *testing.T) {
	// Create a file loader with a non-existent file
	nonExistentPath := filepath.Join(os.TempDir(), "non-existent-file.txt")
	loader := NewFileContentLoader(nonExistentPath)
	loader.Delay = 0

	// Load the content
	ctx := context.Background()
	resultCh := loader.Load(ctx)

	// Wait for the result
	result := <-resultCh

	// Verify the result
	if result.Err == nil {
		t.Error("Expected an error for non-existent file, got nil")
	}
	if result.Content != "" {
		t.Errorf("Expected empty content for error case, got: %s", result.Content)
	}

	// Verify the loader state
	if loader.IsLoading() {
		t.Error("Loader should not be in loading state after error")
	}
	if loader.Error() == nil {
		t.Error("Loader should have error after failed load, got nil")
	}
	if loader.Content() != "" {
		t.Errorf("Loader content should be empty after error, got: %s", loader.Content())
	}
}

// TestFileContentLoader_Load_EmptyPath tests file loading with empty path
func TestFileContentLoader_Load_EmptyPath(t *testing.T) {
	// Create a file loader with empty path
	loader := NewFileContentLoader("")
	loader.Delay = 0

	// Load the content
	ctx := context.Background()
	resultCh := loader.Load(ctx)

	// Wait for the result
	result := <-resultCh

	// Verify the result
	if result.Err == nil {
		t.Error("Expected an error for empty path, got nil")
	}
	if result.Content != "" {
		t.Errorf("Expected empty content for error case, got: %s", result.Content)
	}

	// Verify the loader state
	if loader.IsLoading() {
		t.Error("Loader should not be in loading state after error")
	}
	if loader.Error() == nil {
		t.Error("Loader should have error after failed load, got nil")
	}
	if loader.Content() != "" {
		t.Errorf("Loader content should be empty after error, got: %s", loader.Content())
	}
}

// TestFileContentLoader_Load_Cancellation tests cancellation of file loading
func TestFileContentLoader_Load_Cancellation(t *testing.T) {
	// Create a file loader with a delay
	loader := NewFileContentLoader("some-file.txt")
	loader.Delay = 100 * time.Millisecond

	// Create a context with cancellation
	ctx, cancel := context.WithCancel(context.Background())

	// Start loading
	resultCh := loader.Load(ctx)

	// Cancel the context immediately
	cancel()

	// Wait for the result
	result := <-resultCh

	// Verify the result
	if result.Err == nil {
		t.Error("Expected an error after cancellation, got nil")
	}
	if !errors.Is(result.Err, context.Canceled) {
		t.Errorf("Expected context.Canceled error, got: %v", result.Err)
	}
	if result.Content != "" {
		t.Errorf("Expected empty content after cancellation, got: %s", result.Content)
	}

	// Verify the loader state
	if loader.IsLoading() {
		t.Error("Loader should not be in loading state after cancellation")
	}
	if loader.Error() == nil {
		t.Error("Loader should have error after cancellation, got nil")
	}
	if !errors.Is(loader.Error(), context.Canceled) {
		t.Errorf("Loader error should be context.Canceled, got: %v", loader.Error())
	}
}

// TestStringContentLoader_Load_Success tests successful string content loading
func TestStringContentLoader_Load_Success(t *testing.T) {
	// Create a string loader with test content
	content := "Test string content"
	loader := NewStringContentLoader(content)
	loader.Delay = 0

	// Test initial state
	if loader.IsLoading() {
		t.Error("Loader should not be in loading state initially")
	}
	if loader.Error() != nil {
		t.Errorf("Loader should not have error initially, got: %v", loader.Error())
	}
	if loader.Content() != "" {
		t.Errorf("Loader should not have content initially, got: %s", loader.Content())
	}

	// Load the content
	ctx := context.Background()
	resultCh := loader.Load(ctx)

	// Wait for the result
	result := <-resultCh

	// Verify the result
	if result.Err != nil {
		t.Errorf("Expected no error, got: %v", result.Err)
	}
	if result.Content != content {
		t.Errorf("Expected content: %s, got: %s", content, result.Content)
	}

	// Verify the loader state
	if loader.IsLoading() {
		t.Error("Loader should not be in loading state after completion")
	}
	if loader.Error() != nil {
		t.Errorf("Loader should not have error after successful load, got: %v", loader.Error())
	}
	if loader.Content() != content {
		t.Errorf("Loader content should match, expected: %s, got: %s", content, loader.Content())
	}
}

// TestStringContentLoader_Load_Cancellation tests cancellation of string loading
func TestStringContentLoader_Load_Cancellation(t *testing.T) {
	// Create a string loader with a delay
	loader := NewStringContentLoader("test content")
	loader.Delay = 100 * time.Millisecond

	// Create a context with cancellation
	ctx, cancel := context.WithCancel(context.Background())

	// Start loading
	resultCh := loader.Load(ctx)

	// Cancel the context immediately
	cancel()

	// Wait for the result
	result := <-resultCh

	// Verify the result
	if result.Err == nil {
		t.Error("Expected an error after cancellation, got nil")
	}
	if !errors.Is(result.Err, context.Canceled) {
		t.Errorf("Expected context.Canceled error, got: %v", result.Err)
	}
	if result.Content != "" {
		t.Errorf("Expected empty content after cancellation, got: %s", result.Content)
	}

	// Verify the loader state
	if loader.IsLoading() {
		t.Error("Loader should not be in loading state after cancellation")
	}
	if loader.Error() == nil {
		t.Error("Loader should have error after cancellation, got nil")
	}
	if !errors.Is(loader.Error(), context.Canceled) {
		t.Errorf("Loader error should be context.Canceled, got: %v", loader.Error())
	}
}

// TestBaseLoader_Reset tests the Reset method
func TestBaseLoader_Reset(t *testing.T) {
	// Create a base loader and set some state
	loader := &BaseLoader{}
	loader.setLoading(true)
	loader.setError(errors.New("test error"))
	loader.setContent("test content")

	// Verify the initial state
	if !loader.IsLoading() {
		t.Error("Loader should be in loading state")
	}
	if loader.Error() == nil {
		t.Error("Loader should have an error")
	}
	if loader.Content() != "test content" {
		t.Errorf("Loader content should be 'test content', got: %s", loader.Content())
	}

	// Reset the loader
	loader.Reset()

	// Verify the state after reset
	if loader.IsLoading() {
		t.Error("Loader should not be in loading state after reset")
	}
	if loader.Error() != nil {
		t.Errorf("Loader should not have error after reset, got: %v", loader.Error())
	}
	if loader.Content() != "test content" {
		t.Errorf("Loader content should not be affected by reset, expected: 'test content', got: %s", loader.Content())
	}
}

// TestConcurrentAccess tests concurrent access to the loader
func TestConcurrentAccess(t *testing.T) {
	// Create a loader
	loader := &BaseLoader{}

	// Number of concurrent goroutines
	const numGoroutines = 100

	// Create a wait group to wait for all goroutines
	done := make(chan bool)

	// Start goroutines that concurrently access the loader
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			// Perform various operations on the loader
			loader.setLoading(true)
			loader.IsLoading()
			loader.setError(errors.New("test error"))
			loader.Error()
			loader.setContent("test content")
			loader.Content()
			loader.Reset()

			// Signal completion
			done <- true
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// If we reach here without deadlocks or panics, the test passes
}

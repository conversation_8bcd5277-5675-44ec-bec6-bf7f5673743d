// Package loader provides a framework for loading content from various sources.
// It is designed to be independent of any UI framework and can be used in any Go application.
// The package includes interfaces and implementations for loading content from files and strings,
// with built-in support for cancellation, error handling, and state management.
package loader

import (
	"context"
	"sync"
)

// LoadResult represents the result of a content loading operation
type LoadResult struct {
	Content string
	Err     error
}

// ContentLoader is an interface for loading content from various sources
// This is a pure interface with no dependencies on any UI framework
type ContentLoader interface {
	// Load initiates the content loading process with context for cancellation
	Load(ctx context.Context) <-chan <PERSON><PERSON><PERSON><PERSON>ult

	// IsLoading returns whether content is currently being loaded
	IsLoading() bool

	// Error returns any error that occurred during loading
	Error() error

	// Content returns the loaded content
	Content() string

	// Reset clears any errors and resets the loading state
	Reset()
}

// BaseLoader provides common functionality for all loaders
type BaseLoader struct {
	content    string
	isLoading  bool
	loadingErr error
	mu         sync.Mutex
}

// IsLoading returns whether content is currently being loaded
func (b *BaseLoader) IsLoading() bool {
	b.mu.Lock()
	defer b.mu.Unlock()
	return b.isLoading
}

// Error returns any error that occurred during loading
func (b *BaseLoader) Error() error {
	b.mu.Lock()
	defer b.mu.Unlock()
	return b.loadingErr
}

// Content returns the loaded content
func (b *BaseLoader) Content() string {
	b.mu.Lock()
	defer b.mu.Unlock()
	return b.content
}

// Reset clears any errors and resets the loading state
func (b *BaseLoader) Reset() {
	b.mu.Lock()
	defer b.mu.Unlock()
	b.isLoading = false
	b.loadingErr = nil
}

// setLoading sets the loading state
func (b *BaseLoader) setLoading(loading bool) {
	b.mu.Lock()
	defer b.mu.Unlock()
	b.isLoading = loading
}

// setContent sets the content
func (b *BaseLoader) setContent(content string) {
	b.mu.Lock()
	defer b.mu.Unlock()
	b.content = content
}

// setError sets the error
func (b *BaseLoader) setError(err error) {
	b.mu.Lock()
	defer b.mu.Unlock()
	b.loadingErr = err
}

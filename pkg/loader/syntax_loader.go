package loader

import (
	"context"
	"path/filepath"
	"strings"

	"eionz.com/demo/pkg/syntax"
)

// SyntaxHighlightingLoader wraps a ContentLoader to add syntax highlighting
type SyntaxHighlightingLoader struct {
	BaseLoader
	baseLoader         ContentLoader
	highlighter        *syntax.SyntaxHighlightingService
	filePath          string
	autoDetectLanguage bool
	forceLanguage     string
	enabled           bool
}

// NewSyntaxHighlightingLoader creates a new syntax highlighting loader
func NewSyntaxHighlightingLoader(baseLoader ContentLoader) *SyntaxHighlightingLoader {
	return &SyntaxHighlightingLoader{
		baseLoader:         baseLoader,
		highlighter:        syntax.GetGlobalSyntaxService(),
		autoDetectLanguage: true,
		enabled:            true,
	}
}

// NewSyntaxHighlightingLoaderWithService creates a loader with a specific highlighting service
func NewSyntaxHighlightingLoaderWithService(baseLoader ContentLoader, highlighter *syntax.SyntaxHighlightingService) *SyntaxHighlightingLoader {
	return &SyntaxHighlightingLoader{
		baseLoader:         baseLoader,
		highlighter:        highlighter,
		autoDetectLanguage: true,
		enabled:            true,
	}
}

// SetFilePath sets the file path for language detection
func (shl *SyntaxHighlightingLoader) SetFilePath(filePath string) {
	shl.filePath = filePath
}

// SetLanguage forces a specific language instead of auto-detection
func (shl *SyntaxHighlightingLoader) SetLanguage(language string) {
	shl.forceLanguage = language
	shl.autoDetectLanguage = false
}

// SetAutoDetectLanguage enables or disables automatic language detection
func (shl *SyntaxHighlightingLoader) SetAutoDetectLanguage(enabled bool) {
	shl.autoDetectLanguage = enabled
}

// SetEnabled enables or disables syntax highlighting
func (shl *SyntaxHighlightingLoader) SetEnabled(enabled bool) {
	shl.enabled = enabled
}

// Load loads content and applies syntax highlighting
func (shl *SyntaxHighlightingLoader) Load(ctx context.Context) <-chan LoadResult {
	resultCh := make(chan LoadResult, 1)
	
	shl.setLoading(true)
	shl.setError(nil)
	
	go func() {
		defer close(resultCh)
		defer shl.setLoading(false)
		
		// Load content using base loader
		baseResultCh := shl.baseLoader.Load(ctx)
		
		// Wait for base loader to complete
		select {
		case baseResult := <-baseResultCh:
			if baseResult.Err != nil {
				shl.setError(baseResult.Err)
				resultCh <- baseResult
				return
			}
			
			// Apply syntax highlighting if enabled
			content := baseResult.Content
			if shl.enabled && shl.highlighter.IsEnabled() && content != "" {
				language := shl.detectLanguage(content)
				highlightedContent := shl.highlighter.HighlightText(content, language)
				content = highlightedContent
			}
			
			// Set the final content
			shl.setContent(content)
			resultCh <- LoadResult{Content: content, Err: nil}
			
		case <-ctx.Done():
			err := ctx.Err()
			shl.setError(err)
			resultCh <- LoadResult{Content: "", Err: err}
		}
	}()
	
	return resultCh
}

// detectLanguage determines the language for syntax highlighting
func (shl *SyntaxHighlightingLoader) detectLanguage(content string) string {
	// If language is forced, use that
	if !shl.autoDetectLanguage && shl.forceLanguage != "" {
		return shl.forceLanguage
	}
	
	// Auto-detect from file path if available
	if shl.filePath != "" {
		ext := strings.ToLower(filepath.Ext(shl.filePath))
		return syntax.GetLanguageFromExtension(ext)
	}
	
	// Try to detect from content patterns (simple heuristics)
	return shl.detectLanguageFromContent(content)
}

// detectLanguageFromContent uses simple heuristics to detect language from content
func (shl *SyntaxHighlightingLoader) detectLanguageFromContent(content string) string {
	content = strings.TrimSpace(content)
	
	// Check for common file patterns
	if strings.HasPrefix(content, "package ") {
		return "go"
	}
	if strings.HasPrefix(content, "#!/bin/bash") || strings.HasPrefix(content, "#!/bin/sh") {
		return "shell"
	}
	if strings.HasPrefix(content, "#!/usr/bin/env python") || strings.HasPrefix(content, "#!/usr/bin/python") {
		return "python"
	}
	if strings.HasPrefix(content, "<?php") {
		return "php"
	}
	if strings.HasPrefix(content, "<!DOCTYPE html") || strings.HasPrefix(content, "<html") {
		return "html"
	}
	
	// Check for JSON
	if (strings.HasPrefix(content, "{") && strings.HasSuffix(content, "}")) ||
		(strings.HasPrefix(content, "[") && strings.HasSuffix(content, "]")) {
		return "json"
	}
	
	// Check for YAML (starts with key: value or ---)
	lines := strings.Split(content, "\n")
	if len(lines) > 0 {
		firstLine := strings.TrimSpace(lines[0])
		if strings.HasPrefix(firstLine, "---") {
			return "yaml"
		}
		if strings.Contains(firstLine, ":") && !strings.Contains(firstLine, "//") {
			return "yaml"
		}
	}
	
	// Check for common programming language patterns
	if strings.Contains(content, "function ") && strings.Contains(content, "{") {
		if strings.Contains(content, "console.log") || strings.Contains(content, "document.") {
			return "javascript"
		}
	}
	
	if strings.Contains(content, "def ") && strings.Contains(content, ":") {
		return "python"
	}
	
	if strings.Contains(content, "public class ") || strings.Contains(content, "import java.") {
		return "java"
	}
	
	if strings.Contains(content, "#include") && strings.Contains(content, "int main") {
		return "c"
	}
	
	// Check for SQL
	sqlKeywords := []string{"SELECT", "INSERT", "UPDATE", "DELETE", "CREATE TABLE", "ALTER TABLE"}
	upperContent := strings.ToUpper(content)
	for _, keyword := range sqlKeywords {
		if strings.Contains(upperContent, keyword) {
			return "sql"
		}
	}
	
	// Check for CSS
	if strings.Contains(content, "{") && strings.Contains(content, ":") && strings.Contains(content, ";") {
		if strings.Contains(content, "color:") || strings.Contains(content, "font-") || strings.Contains(content, "margin:") {
			return "css"
		}
	}
	
	// Default to plain text
	return "text"
}

// IsLoading delegates to base loader and adds highlighting state
func (shl *SyntaxHighlightingLoader) IsLoading() bool {
	return shl.BaseLoader.IsLoading() || shl.baseLoader.IsLoading()
}

// Error returns error from base loader or highlighting
func (shl *SyntaxHighlightingLoader) Error() error {
	if err := shl.BaseLoader.Error(); err != nil {
		return err
	}
	return shl.baseLoader.Error()
}

// Reset clears state for both base loader and highlighting
func (shl *SyntaxHighlightingLoader) Reset() {
	shl.BaseLoader.Reset()
	shl.baseLoader.Reset()
}

// GetHighlighter returns the syntax highlighting service
func (shl *SyntaxHighlightingLoader) GetHighlighter() *syntax.SyntaxHighlightingService {
	return shl.highlighter
}
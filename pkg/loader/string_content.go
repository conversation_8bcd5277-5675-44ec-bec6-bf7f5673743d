package loader

import (
	"context"
	"time"
)

// StringContentLoader provides content from a string
type StringContentLoader struct {
	BaseLoader
	stringContent string
	// Add delay for demo purposes - can be removed in production
	Delay time.Duration
}

// NewStringContentLoader creates a new string content loader
func NewStringContentLoader(content string) *StringContentLoader {
	return &StringContentLoader{
		stringContent: content,
		Delay:         500 * time.Millisecond, // Default delay for demo purposes
	}
}

// Load returns a channel that will receive a LoadResult with the string content
func (s *StringContentLoader) Load(ctx context.Context) <-chan <PERSON><PERSON><PERSON>esult {
	resultCh := make(chan <PERSON>adResult, 1)

	s.setLoading(true)
	s.setError(nil)

	go func() {
		defer close(resultCh)

		// Simulate loading delay for demo purposes
		if s.Delay > 0 {
			select {
			case <-time.After(s.Delay):
				// Continue after delay
			case <-ctx.Done():
				// Context was cancelled
				s.setError(ctx.Err())
				s.setLoading(false)
				resultCh <- LoadResult{Content: "", Err: ctx.Err()}
				return
			}
		}

		// Set the content and return
		s.setContent(s.stringContent)
		s.setLoading(false)
		resultCh <- LoadResult{Content: s.stringContent, Err: nil}
	}()

	return resultCh
}

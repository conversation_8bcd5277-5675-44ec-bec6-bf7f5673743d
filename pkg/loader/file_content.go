package loader

import (
	"context"
	"errors"
	"io"
	"os"
	"time"
)

// FileContentLoader loads content from a file
type FileContentLoader struct {
	BaseLoader
	FilePath string
	// Add delay for demo purposes - can be removed in production
	Delay time.Duration
}

// NewFileContentLoader creates a new file content loader
func NewFileContentLoader(filePath string) *FileContentLoader {
	return &FileContentLoader{
		FilePath: filePath,
		Delay:    500 * time.Millisecond, // Default delay for demo purposes
	}
}

// Load reads the file and returns a channel that will receive a LoadResult
func (f *FileContentLoader) Load(ctx context.Context) <-chan <PERSON>adResult {
	resultCh := make(chan <PERSON><PERSON>, 1)

	f.setLoading(true)
	f.setError(nil)

	go func() {
		defer close(resultCh)

		// Simulate loading delay for demo purposes
		if f.Delay > 0 {
			select {
			case <-time.After(f.Delay):
				// Continue after delay
			case <-ctx.Done():
				// Context was cancelled
				f.setError(ctx.Err())
				f.setLoading(false)
				resultCh <- LoadResult{Content: "", Err: ctx.Err()}
				return
			}
		}

		// Check if file exists
		if f.FilePath == "" {
			err := errors.New("no file path specified")
			f.setError(err)
			f.setLoading(false)
			resultCh <- LoadResult{Content: "", Err: err}
			return
		}

		// Open the file
		file, err := os.Open(f.FilePath)
		if err != nil {
			f.setError(err)
			f.setLoading(false)
			resultCh <- LoadResult{Content: "", Err: err}
			return
		}
		defer file.Close()

		// Read the file content
		content, err := io.ReadAll(file)
		if err != nil {
			f.setError(err)
			f.setLoading(false)
			resultCh <- LoadResult{Content: "", Err: err}
			return
		}

		// Set the content and return
		contentStr := string(content)
		f.setContent(contentStr)
		f.setLoading(false)
		resultCh <- LoadResult{Content: contentStr, Err: nil}
	}()

	return resultCh
}

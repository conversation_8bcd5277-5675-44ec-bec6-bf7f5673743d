package themes

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"eionz.com/demo/pkg/syntax"
	"github.com/charmbracelet/lipgloss"
)

// AppTheme represents a complete application theme
type AppTheme struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Author      string                 `json:"author"`
	Version     string                 `json:"version"`
	UI          UITheme                `json:"ui"`
	Syntax      SyntaxThemeDefinition  `json:"syntax"`
	Custom      map[string]interface{} `json:"custom,omitempty"`
}

// UITheme contains colors and styles for UI elements
type UITheme struct {
	// Background colors
	Primary       string `json:"primary"`
	Secondary     string `json:"secondary"`
	Background    string `json:"background"`
	Surface       string `json:"surface"`
	
	// Text colors
	OnPrimary     string `json:"on_primary"`
	OnSecondary   string `json:"on_secondary"`
	OnBackground  string `json:"on_background"`
	OnSurface     string `json:"on_surface"`
	
	// State colors
	Success       string `json:"success"`
	Warning       string `json:"warning"`
	Error         string `json:"error"`
	Info          string `json:"info"`
	
	// Border and accent colors
	Border        string `json:"border"`
	BorderFocused string `json:"border_focused"`
	Selection     string `json:"selection"`
	Highlight     string `json:"highlight"`
	
	// Component-specific colors
	Sidebar       ComponentTheme `json:"sidebar"`
	Header        ComponentTheme `json:"header"`
	Footer        ComponentTheme `json:"footer"`
	Input         ComponentTheme `json:"input"`
	Button        ComponentTheme `json:"button"`
}

// ComponentTheme contains theme settings for specific components
type ComponentTheme struct {
	Background string `json:"background"`
	Foreground string `json:"foreground"`
	Border     string `json:"border"`
	Accent     string `json:"accent"`
}

// SyntaxThemeDefinition contains syntax highlighting color definitions
type SyntaxThemeDefinition struct {
	Keyword     string `json:"keyword"`
	String      string `json:"string"`
	Comment     string `json:"comment"`
	Function    string `json:"function"`
	Type        string `json:"type"`
	Number      string `json:"number"`
	Operator    string `json:"operator"`
	Variable    string `json:"variable"`
	Constant    string `json:"constant"`
	Punctuation string `json:"punctuation"`
	Error       string `json:"error"`
	Background  string `json:"background"`
}

// ThemeManager manages application themes
type ThemeManager struct {
	themes        map[string]*AppTheme
	currentTheme  *AppTheme
	customThemes  map[string]*AppTheme
	themesDir     string
}

// NewThemeManager creates a new theme manager
func NewThemeManager(themesDir string) *ThemeManager {
	tm := &ThemeManager{
		themes:       make(map[string]*AppTheme),
		customThemes: make(map[string]*AppTheme),
		themesDir:    themesDir,
	}
	
	// Register built-in themes
	tm.registerBuiltinThemes()
	
	// Load custom themes
	tm.loadCustomThemes()
	
	// Set default theme
	tm.SetTheme("dark")
	
	return tm
}

// registerBuiltinThemes registers built-in application themes
func (tm *ThemeManager) registerBuiltinThemes() {
	// Dark theme (default)
	darkTheme := &AppTheme{
		Name:        "dark",
		Description: "Dark theme optimized for low-light environments",
		Author:      "TermiLLM Team",
		Version:     "1.0.0",
		UI: UITheme{
			Primary:       "#569CD6",
			Secondary:     "#4EC9B0",
			Background:    "#1E1E1E",
			Surface:       "#252526",
			OnPrimary:     "#FFFFFF",
			OnSecondary:   "#000000",
			OnBackground:  "#CCCCCC",
			OnSurface:     "#CCCCCC",
			Success:       "#4EC9B0",
			Warning:       "#FFCC02",
			Error:         "#F48771",
			Info:          "#9CDCFE",
			Border:        "#3C3C3C",
			BorderFocused: "#007ACC",
			Selection:     "#264F78",
			Highlight:     "#FFE792",
			Sidebar: ComponentTheme{
				Background: "#2D2D30",
				Foreground: "#CCCCCC",
				Border:     "#3C3C3C",
				Accent:     "#007ACC",
			},
			Header: ComponentTheme{
				Background: "#2D2D30",
				Foreground: "#CCCCCC",
				Border:     "#3C3C3C",
				Accent:     "#569CD6",
			},
			Footer: ComponentTheme{
				Background: "#007ACC",
				Foreground: "#FFFFFF",
				Border:     "#3C3C3C",
				Accent:     "#569CD6",
			},
			Input: ComponentTheme{
				Background: "#3C3C3C",
				Foreground: "#CCCCCC",
				Border:     "#6E6E6E",
				Accent:     "#007ACC",
			},
			Button: ComponentTheme{
				Background: "#0E639C",
				Foreground: "#FFFFFF",
				Border:     "#007ACC",
				Accent:     "#569CD6",
			},
		},
		Syntax: SyntaxThemeDefinition{
			Keyword:     "#569CD6",
			String:      "#CE9178",
			Comment:     "#6A9955",
			Function:    "#DCDCAA",
			Type:        "#4EC9B0",
			Number:      "#B5CEA8",
			Operator:    "#D4D4D4",
			Variable:    "#9CDCFE",
			Constant:    "#4FC1FF",
			Punctuation: "#D4D4D4",
			Error:       "#F44747",
			Background:  "#CCCCCC",
		},
	}
	tm.themes["dark"] = darkTheme
	
	// Light theme
	lightTheme := &AppTheme{
		Name:        "light",
		Description: "Light theme optimized for bright environments",
		Author:      "TermiLLM Team",
		Version:     "1.0.0",
		UI: UITheme{
			Primary:       "#0078D4",
			Secondary:     "#107C10",
			Background:    "#FFFFFF",
			Surface:       "#F3F2F1",
			OnPrimary:     "#FFFFFF",
			OnSecondary:   "#FFFFFF",
			OnBackground:  "#323130",
			OnSurface:     "#323130",
			Success:       "#107C10",
			Warning:       "#FF8C00",
			Error:         "#D13438",
			Info:          "#0078D4",
			Border:        "#E1DFDD",
			BorderFocused: "#0078D4",
			Selection:     "#B3D6FC",
			Highlight:     "#FFFF88",
			Sidebar: ComponentTheme{
				Background: "#F3F2F1",
				Foreground: "#323130",
				Border:     "#E1DFDD",
				Accent:     "#0078D4",
			},
			Header: ComponentTheme{
				Background: "#F3F2F1",
				Foreground: "#323130",
				Border:     "#E1DFDD",
				Accent:     "#0078D4",
			},
			Footer: ComponentTheme{
				Background: "#0078D4",
				Foreground: "#FFFFFF",
				Border:     "#E1DFDD",
				Accent:     "#005A9E",
			},
			Input: ComponentTheme{
				Background: "#FFFFFF",
				Foreground: "#323130",
				Border:     "#8A8886",
				Accent:     "#0078D4",
			},
			Button: ComponentTheme{
				Background: "#0078D4",
				Foreground: "#FFFFFF",
				Border:     "#005A9E",
				Accent:     "#106EBE",
			},
		},
		Syntax: SyntaxThemeDefinition{
			Keyword:     "#0000FF",
			String:      "#A31515",
			Comment:     "#008000",
			Function:    "#795E26",
			Type:        "#267F99",
			Number:      "#098658",
			Operator:    "#000000",
			Variable:    "#001080",
			Constant:    "#0070C1",
			Punctuation: "#000000",
			Error:       "#CD3131",
			Background:  "#000000",
		},
	}
	tm.themes["light"] = lightTheme
	
	// High contrast theme
	highContrastTheme := &AppTheme{
		Name:        "high-contrast",
		Description: "High contrast theme for accessibility",
		Author:      "TermiLLM Team",
		Version:     "1.0.0",
		UI: UITheme{
			Primary:       "#FFFFFF",
			Secondary:     "#FFFF00",
			Background:    "#000000",
			Surface:       "#000000",
			OnPrimary:     "#000000",
			OnSecondary:   "#000000",
			OnBackground:  "#FFFFFF",
			OnSurface:     "#FFFFFF",
			Success:       "#00FF00",
			Warning:       "#FFFF00",
			Error:         "#FF0000",
			Info:          "#00FFFF",
			Border:        "#FFFFFF",
			BorderFocused: "#FFFF00",
			Selection:     "#FFFFFF",
			Highlight:     "#FFFF00",
			Sidebar: ComponentTheme{
				Background: "#000000",
				Foreground: "#FFFFFF",
				Border:     "#FFFFFF",
				Accent:     "#FFFF00",
			},
			Header: ComponentTheme{
				Background: "#000000",
				Foreground: "#FFFFFF",
				Border:     "#FFFFFF",
				Accent:     "#FFFF00",
			},
			Footer: ComponentTheme{
				Background: "#FFFFFF",
				Foreground: "#000000",
				Border:     "#FFFFFF",
				Accent:     "#000000",
			},
			Input: ComponentTheme{
				Background: "#000000",
				Foreground: "#FFFFFF",
				Border:     "#FFFFFF",
				Accent:     "#FFFF00",
			},
			Button: ComponentTheme{
				Background: "#FFFFFF",
				Foreground: "#000000",
				Border:     "#FFFFFF",
				Accent:     "#FFFF00",
			},
		},
		Syntax: SyntaxThemeDefinition{
			Keyword:     "#FFFF00",
			String:      "#00FF00",
			Comment:     "#808080",
			Function:    "#FFFFFF",
			Type:        "#00FFFF",
			Number:      "#FF00FF",
			Operator:    "#FFFFFF",
			Variable:    "#FFFFFF",
			Constant:    "#00FFFF",
			Punctuation: "#FFFFFF",
			Error:       "#FF0000",
			Background:  "#FFFFFF",
		},
	}
	tm.themes["high-contrast"] = highContrastTheme
	
	// Solarized Dark theme
	solarizedDark := &AppTheme{
		Name:        "solarized-dark",
		Description: "Solarized Dark color scheme",
		Author:      "Ethan Schoonover (adapted)",
		Version:     "1.0.0",
		UI: UITheme{
			Primary:       "#268BD2",
			Secondary:     "#2AA198",
			Background:    "#002B36",
			Surface:       "#073642",
			OnPrimary:     "#FDF6E3",
			OnSecondary:   "#FDF6E3",
			OnBackground:  "#839496",
			OnSurface:     "#839496",
			Success:       "#859900",
			Warning:       "#B58900",
			Error:         "#DC322F",
			Info:          "#268BD2",
			Border:        "#586E75",
			BorderFocused: "#268BD2",
			Selection:     "#073642",
			Highlight:     "#B58900",
			Sidebar: ComponentTheme{
				Background: "#073642",
				Foreground: "#839496",
				Border:     "#586E75",
				Accent:     "#268BD2",
			},
			Header: ComponentTheme{
				Background: "#073642",
				Foreground: "#839496",
				Border:     "#586E75",
				Accent:     "#268BD2",
			},
			Footer: ComponentTheme{
				Background: "#268BD2",
				Foreground: "#FDF6E3",
				Border:     "#586E75",
				Accent:     "#2AA198",
			},
			Input: ComponentTheme{
				Background: "#073642",
				Foreground: "#839496",
				Border:     "#586E75",
				Accent:     "#268BD2",
			},
			Button: ComponentTheme{
				Background: "#268BD2",
				Foreground: "#FDF6E3",
				Border:     "#2AA198",
				Accent:     "#859900",
			},
		},
		Syntax: SyntaxThemeDefinition{
			Keyword:     "#859900",
			String:      "#2AA198",
			Comment:     "#586E75",
			Function:    "#268BD2",
			Type:        "#B58900",
			Number:      "#D33682",
			Operator:    "#839496",
			Variable:    "#839496",
			Constant:    "#CB4B16",
			Punctuation: "#839496",
			Error:       "#DC322F",
			Background:  "#839496",
		},
	}
	tm.themes["solarized-dark"] = solarizedDark
}

// loadCustomThemes loads custom themes from the themes directory
func (tm *ThemeManager) loadCustomThemes() {
	if tm.themesDir == "" {
		return
	}
	
	// Create themes directory if it doesn't exist
	if _, err := os.Stat(tm.themesDir); os.IsNotExist(err) {
		os.MkdirAll(tm.themesDir, 0755)
		return
	}
	
	// Read all JSON files in the themes directory
	files, err := filepath.Glob(filepath.Join(tm.themesDir, "*.json"))
	if err != nil {
		return
	}
	
	for _, file := range files {
		theme, err := tm.loadThemeFromFile(file)
		if err != nil {
			continue // Skip invalid themes
		}
		tm.customThemes[theme.Name] = theme
	}
}

// loadThemeFromFile loads a theme from a JSON file
func (tm *ThemeManager) loadThemeFromFile(filePath string) (*AppTheme, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}
	
	var theme AppTheme
	if err := json.Unmarshal(data, &theme); err != nil {
		return nil, err
	}
	
	return &theme, nil
}

// GetAvailableThemes returns a list of all available theme names
func (tm *ThemeManager) GetAvailableThemes() []string {
	var themes []string
	
	// Add built-in themes
	for name := range tm.themes {
		themes = append(themes, name)
	}
	
	// Add custom themes
	for name := range tm.customThemes {
		themes = append(themes, name)
	}
	
	return themes
}

// GetTheme returns a theme by name
func (tm *ThemeManager) GetTheme(name string) (*AppTheme, bool) {
	// Check built-in themes first
	if theme, exists := tm.themes[name]; exists {
		return theme, true
	}
	
	// Check custom themes
	if theme, exists := tm.customThemes[name]; exists {
		return theme, true
	}
	
	return nil, false
}

// SetTheme sets the current theme
func (tm *ThemeManager) SetTheme(name string) error {
	theme, exists := tm.GetTheme(name)
	if !exists {
		return fmt.Errorf("theme not found: %s", name)
	}
	
	tm.currentTheme = theme
	return nil
}

// GetCurrentTheme returns the current theme
func (tm *ThemeManager) GetCurrentTheme() *AppTheme {
	return tm.currentTheme
}

// SaveTheme saves a custom theme to the themes directory
func (tm *ThemeManager) SaveTheme(theme *AppTheme) error {
	if tm.themesDir == "" {
		return fmt.Errorf("themes directory not configured")
	}
	
	// Create themes directory if it doesn't exist
	if err := os.MkdirAll(tm.themesDir, 0755); err != nil {
		return err
	}
	
	// Marshal theme to JSON
	data, err := json.MarshalIndent(theme, "", "  ")
	if err != nil {
		return err
	}
	
	// Write to file
	filePath := filepath.Join(tm.themesDir, theme.Name+".json")
	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return err
	}
	
	// Add to custom themes
	tm.customThemes[theme.Name] = theme
	
	return nil
}

// DeleteTheme deletes a custom theme
func (tm *ThemeManager) DeleteTheme(name string) error {
	// Can't delete built-in themes
	if _, exists := tm.themes[name]; exists {
		return fmt.Errorf("cannot delete built-in theme: %s", name)
	}
	
	// Remove from custom themes
	delete(tm.customThemes, name)
	
	// Remove file if it exists
	if tm.themesDir != "" {
		filePath := filepath.Join(tm.themesDir, name+".json")
		os.Remove(filePath)
	}
	
	return nil
}

// CreateSyntaxTheme converts theme definition to syntax.SyntaxTheme
func (tm *ThemeManager) CreateSyntaxTheme(syntaxDef SyntaxThemeDefinition) syntax.SyntaxTheme {
	return syntax.SyntaxTheme{
		Keyword:     lipgloss.NewStyle().Foreground(lipgloss.Color(syntaxDef.Keyword)),
		String:      lipgloss.NewStyle().Foreground(lipgloss.Color(syntaxDef.String)),
		Comment:     lipgloss.NewStyle().Foreground(lipgloss.Color(syntaxDef.Comment)),
		Function:    lipgloss.NewStyle().Foreground(lipgloss.Color(syntaxDef.Function)),
		Type:        lipgloss.NewStyle().Foreground(lipgloss.Color(syntaxDef.Type)),
		Number:      lipgloss.NewStyle().Foreground(lipgloss.Color(syntaxDef.Number)),
		Operator:    lipgloss.NewStyle().Foreground(lipgloss.Color(syntaxDef.Operator)),
		Variable:    lipgloss.NewStyle().Foreground(lipgloss.Color(syntaxDef.Variable)),
		Constant:    lipgloss.NewStyle().Foreground(lipgloss.Color(syntaxDef.Constant)),
		Punctuation: lipgloss.NewStyle().Foreground(lipgloss.Color(syntaxDef.Punctuation)),
		Error:       lipgloss.NewStyle().Foreground(lipgloss.Color(syntaxDef.Error)),
		Background:  lipgloss.NewStyle().Foreground(lipgloss.Color(syntaxDef.Background)),
	}
}

// ApplyTheme applies the current theme to the application
func (tm *ThemeManager) ApplyTheme() error {
	if tm.currentTheme == nil {
		return fmt.Errorf("no theme selected")
	}
	
	// Apply syntax theme
	syntaxTheme := tm.CreateSyntaxTheme(tm.currentTheme.Syntax)
	syntaxService := syntax.GetGlobalSyntaxService()
	syntaxService.SetTheme(syntaxTheme)
	
	// TODO: Apply UI theme to application components
	// This would involve updating global style variables or notifying components
	
	return nil
}

// GetThemeStyles returns lipgloss styles for the current theme
func (tm *ThemeManager) GetThemeStyles() ThemeStyles {
	if tm.currentTheme == nil {
		// Return default styles if no theme is set
		return DefaultThemeStyles()
	}
	
	theme := tm.currentTheme.UI
	
	return ThemeStyles{
		Primary:       lipgloss.NewStyle().Foreground(lipgloss.Color(theme.Primary)),
		Secondary:     lipgloss.NewStyle().Foreground(lipgloss.Color(theme.Secondary)),
		Background:    lipgloss.NewStyle().Background(lipgloss.Color(theme.Background)),
		Surface:       lipgloss.NewStyle().Background(lipgloss.Color(theme.Surface)),
		OnBackground:  lipgloss.NewStyle().Foreground(lipgloss.Color(theme.OnBackground)),
		OnSurface:     lipgloss.NewStyle().Foreground(lipgloss.Color(theme.OnSurface)),
		Success:       lipgloss.NewStyle().Foreground(lipgloss.Color(theme.Success)),
		Warning:       lipgloss.NewStyle().Foreground(lipgloss.Color(theme.Warning)),
		Error:         lipgloss.NewStyle().Foreground(lipgloss.Color(theme.Error)),
		Info:          lipgloss.NewStyle().Foreground(lipgloss.Color(theme.Info)),
		Border:        lipgloss.NewStyle().BorderForeground(lipgloss.Color(theme.Border)),
		BorderFocused: lipgloss.NewStyle().BorderForeground(lipgloss.Color(theme.BorderFocused)),
		Selection:     lipgloss.NewStyle().Background(lipgloss.Color(theme.Selection)),
		Highlight:     lipgloss.NewStyle().Background(lipgloss.Color(theme.Highlight)),
	}
}

// ThemeStyles contains lipgloss styles for the current theme
type ThemeStyles struct {
	Primary       lipgloss.Style
	Secondary     lipgloss.Style
	Background    lipgloss.Style
	Surface       lipgloss.Style
	OnBackground  lipgloss.Style
	OnSurface     lipgloss.Style
	Success       lipgloss.Style
	Warning       lipgloss.Style
	Error         lipgloss.Style
	Info          lipgloss.Style
	Border        lipgloss.Style
	BorderFocused lipgloss.Style
	Selection     lipgloss.Style
	Highlight     lipgloss.Style
}

// DefaultThemeStyles returns default styles
func DefaultThemeStyles() ThemeStyles {
	return ThemeStyles{
		Primary:       lipgloss.NewStyle().Foreground(lipgloss.Color("12")),
		Secondary:     lipgloss.NewStyle().Foreground(lipgloss.Color("14")),
		Background:    lipgloss.NewStyle().Background(lipgloss.Color("0")),
		Surface:       lipgloss.NewStyle().Background(lipgloss.Color("8")),
		OnBackground:  lipgloss.NewStyle().Foreground(lipgloss.Color("7")),
		OnSurface:     lipgloss.NewStyle().Foreground(lipgloss.Color("15")),
		Success:       lipgloss.NewStyle().Foreground(lipgloss.Color("10")),
		Warning:       lipgloss.NewStyle().Foreground(lipgloss.Color("11")),
		Error:         lipgloss.NewStyle().Foreground(lipgloss.Color("9")),
		Info:          lipgloss.NewStyle().Foreground(lipgloss.Color("12")),
		Border:        lipgloss.NewStyle().BorderForeground(lipgloss.Color("8")),
		BorderFocused: lipgloss.NewStyle().BorderForeground(lipgloss.Color("12")),
		Selection:     lipgloss.NewStyle().Background(lipgloss.Color("4")),
		Highlight:     lipgloss.NewStyle().Background(lipgloss.Color("11")),
	}
}

// GetDefaultThemesPath returns the default themes directory path
func GetDefaultThemesPath() string {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "./themes"
	}
	return filepath.Join(homeDir, ".termillm", "themes")
}

// Global theme manager instance
var globalThemeManager *ThemeManager

// GetGlobalThemeManager returns the global theme manager
func GetGlobalThemeManager() *ThemeManager {
	if globalThemeManager == nil {
		globalThemeManager = NewThemeManager(GetDefaultThemesPath())
	}
	return globalThemeManager
}

// InitializeGlobalThemeManager initializes the global theme manager
func InitializeGlobalThemeManager(themesDir string) {
	globalThemeManager = NewThemeManager(themesDir)
}
// Package ratelimit provides API rate limiting and throttling capabilities
package ratelimit

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// Limiter defines the interface for rate limiting
type Limiter interface {
	Allow(key string) bool
	Reserve(key string) Reservation
	Wait(ctx context.Context, key string) error
	Limit(key string) Rate
	Reset(key string)
}

// Rate represents the rate limit configuration
type Rate struct {
	Limit  int           `json:"limit"`
	Window time.Duration `json:"window"`
}

// Reservation holds information about a rate limit reservation
type Reservation struct {
	OK       bool
	Delay    time.Duration
	TimeLeft time.Duration
}

// TokenBucketLimiter implements rate limiting using token bucket algorithm
type TokenBucketLimiter struct {
	buckets map[string]*TokenBucket
	rate    Rate
	mutex   sync.RWMutex
}

// TokenBucket represents a token bucket for rate limiting
type TokenBucket struct {
	tokens     float64
	lastRefill time.Time
	capacity   float64
	refillRate float64
	mutex      sync.Mutex
}

// NewTokenBucketLimiter creates a new token bucket rate limiter
func NewTokenBucketLimiter(rate Rate) *TokenBucketLimiter {
	return &TokenBucketLimiter{
		buckets: make(map[string]*TokenBucket),
		rate:    rate,
	}
}

// Allow checks if a request is allowed under the rate limit
func (tbl *TokenBucketLimiter) Allow(key string) bool {
	bucket := tbl.getBucket(key)
	return bucket.consume(1)
}

// Reserve reserves a token and returns reservation details
func (tbl *TokenBucketLimiter) Reserve(key string) Reservation {
	bucket := tbl.getBucket(key)
	
	bucket.mutex.Lock()
	defer bucket.mutex.Unlock()
	
	bucket.refill()
	
	if bucket.tokens >= 1 {
		bucket.tokens--
		return Reservation{OK: true, Delay: 0}
	}
	
	// Calculate delay needed
	tokensNeeded := 1 - bucket.tokens
	delay := time.Duration(tokensNeeded/bucket.refillRate) * time.Second
	
	return Reservation{
		OK:       false,
		Delay:    delay,
		TimeLeft: delay,
	}
}

// Wait waits until a token is available or context is cancelled
func (tbl *TokenBucketLimiter) Wait(ctx context.Context, key string) error {
	reservation := tbl.Reserve(key)
	
	if reservation.OK {
		return nil
	}
	
	if reservation.Delay <= 0 {
		return fmt.Errorf("rate limit exceeded")
	}
	
	timer := time.NewTimer(reservation.Delay)
	defer timer.Stop()
	
	select {
	case <-timer.C:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// Limit returns the current rate limit for a key
func (tbl *TokenBucketLimiter) Limit(key string) Rate {
	return tbl.rate
}

// Reset resets the rate limit state for a key
func (tbl *TokenBucketLimiter) Reset(key string) {
	tbl.mutex.Lock()
	defer tbl.mutex.Unlock()
	delete(tbl.buckets, key)
}

// getBucket gets or creates a token bucket for a key
func (tbl *TokenBucketLimiter) getBucket(key string) *TokenBucket {
	tbl.mutex.Lock()
	defer tbl.mutex.Unlock()
	
	bucket, exists := tbl.buckets[key]
	if !exists {
		bucket = &TokenBucket{
			tokens:     float64(tbl.rate.Limit),
			lastRefill: time.Now(),
			capacity:   float64(tbl.rate.Limit),
			refillRate: float64(tbl.rate.Limit) / tbl.rate.Window.Seconds(),
		}
		tbl.buckets[key] = bucket
	}
	
	return bucket
}

// consume attempts to consume tokens from the bucket
func (tb *TokenBucket) consume(tokens float64) bool {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()
	
	tb.refill()
	
	if tb.tokens >= tokens {
		tb.tokens -= tokens
		return true
	}
	
	return false
}

// refill refills the token bucket based on elapsed time
func (tb *TokenBucket) refill() {
	now := time.Now()
	elapsed := now.Sub(tb.lastRefill).Seconds()
	tokensToAdd := elapsed * tb.refillRate
	
	tb.tokens = min(tb.capacity, tb.tokens+tokensToAdd)
	tb.lastRefill = now
}

func min(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

// SlidingWindowLimiter implements rate limiting using sliding window algorithm
type SlidingWindowLimiter struct {
	windows map[string]*SlidingWindow
	rate    Rate
	mutex   sync.RWMutex
}

// SlidingWindow represents a sliding window for rate limiting
type SlidingWindow struct {
	requests []time.Time
	mutex    sync.Mutex
}

// NewSlidingWindowLimiter creates a new sliding window rate limiter
func NewSlidingWindowLimiter(rate Rate) *SlidingWindowLimiter {
	return &SlidingWindowLimiter{
		windows: make(map[string]*SlidingWindow),
		rate:    rate,
	}
}

// Allow checks if a request is allowed under the rate limit
func (swl *SlidingWindowLimiter) Allow(key string) bool {
	window := swl.getWindow(key)
	
	window.mutex.Lock()
	defer window.mutex.Unlock()
	
	now := time.Now()
	cutoff := now.Add(-swl.rate.Window)
	
	// Remove old requests
	window.cleanup(cutoff)
	
	if len(window.requests) < swl.rate.Limit {
		window.requests = append(window.requests, now)
		return true
	}
	
	return false
}

// Reserve reserves a slot and returns reservation details
func (swl *SlidingWindowLimiter) Reserve(key string) Reservation {
	window := swl.getWindow(key)
	
	window.mutex.Lock()
	defer window.mutex.Unlock()
	
	now := time.Now()
	cutoff := now.Add(-swl.rate.Window)
	
	// Remove old requests
	window.cleanup(cutoff)
	
	if len(window.requests) < swl.rate.Limit {
		window.requests = append(window.requests, now)
		return Reservation{OK: true, Delay: 0}
	}
	
	// Calculate delay until oldest request expires
	if len(window.requests) > 0 {
		oldestRequest := window.requests[0]
		delay := oldestRequest.Add(swl.rate.Window).Sub(now)
		if delay > 0 {
			return Reservation{
				OK:       false,
				Delay:    delay,
				TimeLeft: delay,
			}
		}
	}
	
	return Reservation{OK: false, Delay: 0}
}

// Wait waits until a slot is available or context is cancelled
func (swl *SlidingWindowLimiter) Wait(ctx context.Context, key string) error {
	reservation := swl.Reserve(key)
	
	if reservation.OK {
		return nil
	}
	
	if reservation.Delay <= 0 {
		return fmt.Errorf("rate limit exceeded")
	}
	
	timer := time.NewTimer(reservation.Delay)
	defer timer.Stop()
	
	select {
	case <-timer.C:
		if swl.Allow(key) {
			return nil
		}
		return fmt.Errorf("rate limit still exceeded after delay")
	case <-ctx.Done():
		return ctx.Err()
	}
}

// Limit returns the current rate limit
func (swl *SlidingWindowLimiter) Limit(key string) Rate {
	return swl.rate
}

// Reset resets the rate limit state for a key
func (swl *SlidingWindowLimiter) Reset(key string) {
	swl.mutex.Lock()
	defer swl.mutex.Unlock()
	delete(swl.windows, key)
}

// getWindow gets or creates a sliding window for a key
func (swl *SlidingWindowLimiter) getWindow(key string) *SlidingWindow {
	swl.mutex.Lock()
	defer swl.mutex.Unlock()
	
	window, exists := swl.windows[key]
	if !exists {
		window = &SlidingWindow{
			requests: make([]time.Time, 0),
		}
		swl.windows[key] = window
	}
	
	return window
}

// cleanup removes old requests from the window
func (sw *SlidingWindow) cleanup(cutoff time.Time) {
	validRequests := make([]time.Time, 0, len(sw.requests))
	
	for _, req := range sw.requests {
		if req.After(cutoff) {
			validRequests = append(validRequests, req)
		}
	}
	
	sw.requests = validRequests
}

// MultiLimiter combines multiple rate limiters with different strategies
type MultiLimiter struct {
	limiters []Limiter
	strategy CombinationStrategy
}

// CombinationStrategy defines how multiple limiters are combined
type CombinationStrategy int

const (
	StrategyMostRestrictive CombinationStrategy = iota
	StrategyLeastRestrictive
	StrategyAllMustPass
)

// NewMultiLimiter creates a new multi-limiter
func NewMultiLimiter(strategy CombinationStrategy, limiters ...Limiter) *MultiLimiter {
	return &MultiLimiter{
		limiters: limiters,
		strategy: strategy,
	}
}

// Allow checks if a request is allowed by the combined limiters
func (ml *MultiLimiter) Allow(key string) bool {
	switch ml.strategy {
	case StrategyMostRestrictive:
		for _, limiter := range ml.limiters {
			if !limiter.Allow(key) {
				return false
			}
		}
		return true
		
	case StrategyLeastRestrictive:
		for _, limiter := range ml.limiters {
			if limiter.Allow(key) {
				return true
			}
		}
		return false
		
	case StrategyAllMustPass:
		for _, limiter := range ml.limiters {
			if !limiter.Allow(key) {
				return false
			}
		}
		return true
		
	default:
		return false
	}
}

// Reserve reserves from the appropriate limiter based on strategy
func (ml *MultiLimiter) Reserve(key string) Reservation {
	var bestReservation Reservation
	
	for i, limiter := range ml.limiters {
		reservation := limiter.Reserve(key)
		
		if i == 0 {
			bestReservation = reservation
			continue
		}
		
		switch ml.strategy {
		case StrategyMostRestrictive:
			if reservation.Delay > bestReservation.Delay {
				bestReservation = reservation
			}
		case StrategyLeastRestrictive:
			if reservation.OK || reservation.Delay < bestReservation.Delay {
				bestReservation = reservation
			}
		}
	}
	
	return bestReservation
}

// Wait waits according to the combination strategy
func (ml *MultiLimiter) Wait(ctx context.Context, key string) error {
	reservation := ml.Reserve(key)
	
	if reservation.OK {
		return nil
	}
	
	if reservation.Delay <= 0 {
		return fmt.Errorf("rate limit exceeded")
	}
	
	timer := time.NewTimer(reservation.Delay)
	defer timer.Stop()
	
	select {
	case <-timer.C:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// Limit returns the combined rate limit
func (ml *MultiLimiter) Limit(key string) Rate {
	if len(ml.limiters) == 0 {
		return Rate{}
	}
	return ml.limiters[0].Limit(key)
}

// Reset resets all limiters
func (ml *MultiLimiter) Reset(key string) {
	for _, limiter := range ml.limiters {
		limiter.Reset(key)
	}
}

// RateLimitMiddleware provides HTTP middleware for rate limiting
type RateLimitMiddleware struct {
	limiter     Limiter
	keyExtractor KeyExtractor
	onLimit     OnLimitHandler
}

// KeyExtractor extracts the rate limiting key from a request
type KeyExtractor func(req interface{}) string

// OnLimitHandler handles rate limit violations
type OnLimitHandler func(key string, limit Rate, req interface{}) error

// NewRateLimitMiddleware creates new rate limiting middleware
func NewRateLimitMiddleware(limiter Limiter, keyExtractor KeyExtractor) *RateLimitMiddleware {
	return &RateLimitMiddleware{
		limiter:      limiter,
		keyExtractor: keyExtractor,
		onLimit:      defaultOnLimitHandler,
	}
}

// SetOnLimitHandler sets the handler for rate limit violations
func (rlm *RateLimitMiddleware) SetOnLimitHandler(handler OnLimitHandler) {
	rlm.onLimit = handler
}

// CheckLimit checks if a request should be rate limited
func (rlm *RateLimitMiddleware) CheckLimit(req interface{}) error {
	key := rlm.keyExtractor(req)
	
	if !rlm.limiter.Allow(key) {
		limit := rlm.limiter.Limit(key)
		return rlm.onLimit(key, limit, req)
	}
	
	return nil
}

// defaultOnLimitHandler is the default rate limit violation handler
func defaultOnLimitHandler(key string, limit Rate, req interface{}) error {
	return fmt.Errorf("rate limit exceeded for key %s: %d requests per %v", key, limit.Limit, limit.Window)
}

// Common key extractors

// IPKeyExtractor extracts IP address as the rate limiting key
func IPKeyExtractor(req interface{}) string {
	// This would extract IP from HTTP request in real implementation
	return "default-ip"
}

// UserKeyExtractor extracts user ID as the rate limiting key
func UserKeyExtractor(req interface{}) string {
	// This would extract user ID from HTTP request in real implementation
	return "default-user"
}

// APIKeyExtractor extracts API key as the rate limiting key
func APIKeyExtractor(req interface{}) string {
	// This would extract API key from HTTP request in real implementation
	return "default-api-key"
}

// CompositeKeyExtractor combines multiple key extractors
func CompositeKeyExtractor(extractors ...KeyExtractor) KeyExtractor {
	return func(req interface{}) string {
		var parts []string
		for _, extractor := range extractors {
			parts = append(parts, extractor(req))
		}
		return fmt.Sprintf("%v", parts)
	}
}

// RateLimitConfig holds configuration for different rate limits
type RateLimitConfig struct {
	Global    Rate            `json:"global"`
	PerUser   Rate            `json:"per_user"`
	PerIP     Rate            `json:"per_ip"`
	PerAPIKey Rate            `json:"per_api_key"`
	Custom    map[string]Rate `json:"custom"`
}

// DefaultRateLimitConfig returns sensible default rate limits
func DefaultRateLimitConfig() RateLimitConfig {
	return RateLimitConfig{
		Global:    Rate{Limit: 10000, Window: time.Hour},
		PerUser:   Rate{Limit: 1000, Window: time.Hour},
		PerIP:     Rate{Limit: 100, Window: time.Minute},
		PerAPIKey: Rate{Limit: 5000, Window: time.Hour},
		Custom:    make(map[string]Rate),
	}
}

// LimiterManager manages multiple rate limiters
type LimiterManager struct {
	limiters map[string]Limiter
	config   RateLimitConfig
	mutex    sync.RWMutex
}

// NewLimiterManager creates a new limiter manager
func NewLimiterManager(config RateLimitConfig) *LimiterManager {
	lm := &LimiterManager{
		limiters: make(map[string]Limiter),
		config:   config,
	}
	
	// Create default limiters
	lm.limiters["global"] = NewTokenBucketLimiter(config.Global)
	lm.limiters["per_user"] = NewTokenBucketLimiter(config.PerUser)
	lm.limiters["per_ip"] = NewSlidingWindowLimiter(config.PerIP)
	lm.limiters["per_api_key"] = NewTokenBucketLimiter(config.PerAPIKey)
	
	// Create custom limiters
	for name, rate := range config.Custom {
		lm.limiters[name] = NewTokenBucketLimiter(rate)
	}
	
	return lm
}

// GetLimiter returns a limiter by name
func (lm *LimiterManager) GetLimiter(name string) (Limiter, bool) {
	lm.mutex.RLock()
	defer lm.mutex.RUnlock()
	
	limiter, exists := lm.limiters[name]
	return limiter, exists
}

// AddLimiter adds a new limiter
func (lm *LimiterManager) AddLimiter(name string, limiter Limiter) {
	lm.mutex.Lock()
	defer lm.mutex.Unlock()
	
	lm.limiters[name] = limiter
}

// RemoveLimiter removes a limiter
func (lm *LimiterManager) RemoveLimiter(name string) {
	lm.mutex.Lock()
	defer lm.mutex.Unlock()
	
	delete(lm.limiters, name)
}

// CheckLimits checks multiple rate limits for a request
func (lm *LimiterManager) CheckLimits(checks map[string]string) error {
	for limiterName, key := range checks {
		if limiter, exists := lm.GetLimiter(limiterName); exists {
			if !limiter.Allow(key) {
				limit := limiter.Limit(key)
				return fmt.Errorf("rate limit exceeded for %s: %d requests per %v", limiterName, limit.Limit, limit.Window)
			}
		}
	}
	return nil
}

// ResetLimits resets rate limits for specified keys
func (lm *LimiterManager) ResetLimits(resets map[string]string) {
	for limiterName, key := range resets {
		if limiter, exists := lm.GetLimiter(limiterName); exists {
			limiter.Reset(key)
		}
	}
}

// GetStats returns statistics for all limiters
func (lm *LimiterManager) GetStats() map[string]interface{} {
	lm.mutex.RLock()
	defer lm.mutex.RUnlock()
	
	stats := make(map[string]interface{})
	stats["limiter_count"] = len(lm.limiters)
	stats["config"] = lm.config
	
	return stats
}
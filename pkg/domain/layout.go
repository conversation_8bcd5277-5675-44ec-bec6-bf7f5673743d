package domain

import (
	"fmt"
	"time"
)

// LayoutID represents a unique layout identifier
type LayoutID struct {
	value string
}

func NewLayoutID(value string) LayoutID {
	return LayoutID{value: value}
}

func (id LayoutID) String() string {
	return id.value
}

func (id LayoutID) Equals(other ValueObject) bool {
	if otherID, ok := other.(LayoutID); ok {
		return id.value == otherID.value
	}
	return false
}

// Dimensions represents window dimensions
type Dimensions struct {
	Width  int
	Height int
}

func NewDimensions(width, height int) (Dimensions, error) {
	if width <= 0 || height <= 0 {
		return Dimensions{}, fmt.Errorf("dimensions must be positive: width=%d, height=%d", width, height)
	}
	return Dimensions{Width: width, Height: height}, nil
}

func (d Dimensions) Equals(other ValueObject) bool {
	if otherDim, ok := other.(Dimensions); ok {
		return d.Width == otherDim.Width && d.Height == otherDim.Height
	}
	return false
}

func (d Dimensions) AspectRatio() float64 {
	if d.Height == 0 {
		return 0
	}
	return float64(d.Width) / float64(d.Height)
}

// ComponentID represents a UI component identifier
type ComponentID struct {
	value string
}

func NewComponentID(value string) ComponentID {
	return ComponentID{value: value}
}

func (id ComponentID) String() string {
	return id.value
}

func (id ComponentID) Equals(other ValueObject) bool {
	if otherID, ok := other.(ComponentID); ok {
		return id.value == otherID.value
	}
	return false
}

// Focus represents the current focus state
type Focus struct {
	activeComponent ComponentID
	focusStack      []ComponentID
}

func NewFocus(activeComponent ComponentID) Focus {
	return Focus{
		activeComponent: activeComponent,
		focusStack:      []ComponentID{activeComponent},
	}
}

func (f Focus) ActiveComponent() ComponentID {
	return f.activeComponent
}

func (f Focus) FocusStack() []ComponentID {
	// Return a copy to prevent external modification
	stack := make([]ComponentID, len(f.focusStack))
	copy(stack, f.focusStack)
	return stack
}

func (f Focus) SetActiveComponent(componentID ComponentID) Focus {
	// Add to focus stack if not already active
	if f.activeComponent != componentID {
		newStack := append(f.focusStack, componentID)
		return Focus{
			activeComponent: componentID,
			focusStack:      newStack,
		}
	}
	return f
}

func (f Focus) PopFocus() Focus {
	if len(f.focusStack) <= 1 {
		return f // Can't pop the last focus
	}

	newStack := f.focusStack[:len(f.focusStack)-1]
	newActive := newStack[len(newStack)-1]
	
	return Focus{
		activeComponent: newActive,
		focusStack:      newStack,
	}
}

func (f Focus) Equals(other ValueObject) bool {
	if otherFocus, ok := other.(Focus); ok {
		if f.activeComponent != otherFocus.activeComponent {
			return false
		}
		if len(f.focusStack) != len(otherFocus.focusStack) {
			return false
		}
		for i, comp := range f.focusStack {
			if comp != otherFocus.focusStack[i] {
				return false
			}
		}
		return true
	}
	return false
}

// SidebarLayout represents sidebar-specific layout information
type SidebarLayout struct {
	Width   int
	Visible bool
	Docked  bool
}

func NewSidebarLayout(width int, visible, docked bool) (SidebarLayout, error) {
	if width < 0 {
		return SidebarLayout{}, fmt.Errorf("sidebar width cannot be negative: %d", width)
	}
	return SidebarLayout{
		Width:   width,
		Visible: visible,
		Docked:  docked,
	}, nil
}

func (sl SidebarLayout) Equals(other ValueObject) bool {
	if otherSL, ok := other.(SidebarLayout); ok {
		return sl.Width == otherSL.Width &&
			sl.Visible == otherSL.Visible &&
			sl.Docked == otherSL.Docked
	}
	return false
}

// MainPanelLayout represents main panel layout information
type MainPanelLayout struct {
	Width  int
	Height int
}

func NewMainPanelLayout(width, height int) (MainPanelLayout, error) {
	if width <= 0 || height <= 0 {
		return MainPanelLayout{}, fmt.Errorf("main panel dimensions must be positive: width=%d, height=%d", width, height)
	}
	return MainPanelLayout{Width: width, Height: height}, nil
}

func (mpl MainPanelLayout) Equals(other ValueObject) bool {
	if otherMPL, ok := other.(MainPanelLayout); ok {
		return mpl.Width == otherMPL.Width && mpl.Height == otherMPL.Height
	}
	return false
}

// Layout aggregate root
type Layout struct {
	id          LayoutID
	dimensions  Dimensions
	focus       Focus
	sidebar     SidebarLayout
	mainPanel   MainPanelLayout
	lastUpdated time.Time
	version     int
	events      []DomainEvent
}

func NewLayout(id LayoutID, dimensions Dimensions) *Layout {
	mainPanelLayout, _ := NewMainPanelLayout(dimensions.Width, dimensions.Height)
	sidebarLayout, _ := NewSidebarLayout(30, true, true)
	
	layout := &Layout{
		id:          id,
		dimensions:  dimensions,
		focus:       NewFocus(NewComponentID("main")),
		sidebar:     sidebarLayout,
		mainPanel:   mainPanelLayout,
		lastUpdated: time.Now(),
		version:     1,
		events:      make([]DomainEvent, 0),
	}

	layout.AddDomainEvent(NewLayoutCreatedEvent(id, dimensions))
	return layout
}

func (l *Layout) ID() string {
	return l.id.String()
}

func (l *Layout) GetDimensions() Dimensions {
	return l.dimensions
}

func (l *Layout) GetFocus() Focus {
	return l.focus
}

func (l *Layout) GetSidebar() SidebarLayout {
	return l.sidebar
}

func (l *Layout) GetMainPanel() MainPanelLayout {
	return l.mainPanel
}

func (l *Layout) Version() int {
	return l.version
}

func (l *Layout) LastUpdated() time.Time {
	return l.lastUpdated
}

func (l *Layout) Resize(newDimensions Dimensions) error {
	if newDimensions.Equals(l.dimensions) {
		return nil // No change needed
	}

	oldDimensions := l.dimensions
	l.dimensions = newDimensions
	
	// Recalculate main panel dimensions
	mainPanelWidth := newDimensions.Width
	if l.sidebar.Visible {
		mainPanelWidth -= l.sidebar.Width
	}
	
	var err error
	l.mainPanel, err = NewMainPanelLayout(mainPanelWidth, newDimensions.Height)
	if err != nil {
		l.dimensions = oldDimensions // Rollback
		return fmt.Errorf("failed to resize layout: %w", err)
	}
	
	l.lastUpdated = time.Now()
	l.version++
	l.AddDomainEvent(NewLayoutResizedEvent(l.id, oldDimensions, newDimensions))
	
	return nil
}

func (l *Layout) SetFocus(componentID ComponentID) {
	if l.focus.ActiveComponent() == componentID {
		return // Already focused
	}

	oldFocus := l.focus
	l.focus = l.focus.SetActiveComponent(componentID)
	l.lastUpdated = time.Now()
	l.version++
	l.AddDomainEvent(NewFocusChangedEvent(l.id, oldFocus.ActiveComponent(), componentID))
}

func (l *Layout) ToggleSidebarVisibility() {
	wasVisible := l.sidebar.Visible
	l.sidebar.Visible = !l.sidebar.Visible
	
	// Recalculate main panel width
	mainPanelWidth := l.dimensions.Width
	if l.sidebar.Visible {
		mainPanelWidth -= l.sidebar.Width
	}
	
	l.mainPanel, _ = NewMainPanelLayout(mainPanelWidth, l.dimensions.Height)
	l.lastUpdated = time.Now()
	l.version++
	l.AddDomainEvent(NewSidebarToggleEvent(l.id, wasVisible, l.sidebar.Visible))
}

func (l *Layout) SetSidebarWidth(width int) error {
	if width < 0 {
		return fmt.Errorf("sidebar width cannot be negative: %d", width)
	}
	
	if l.sidebar.Width == width {
		return nil // No change needed
	}
	
	oldWidth := l.sidebar.Width
	l.sidebar.Width = width
	
	// Recalculate main panel width
	mainPanelWidth := l.dimensions.Width
	if l.sidebar.Visible {
		mainPanelWidth -= l.sidebar.Width
	}
	
	var err error
	l.mainPanel, err = NewMainPanelLayout(mainPanelWidth, l.dimensions.Height)
	if err != nil {
		l.sidebar.Width = oldWidth // Rollback
		return fmt.Errorf("failed to set sidebar width: %w", err)
	}
	
	l.lastUpdated = time.Now()
	l.version++
	l.AddDomainEvent(NewSidebarResizedEvent(l.id, oldWidth, width))
	
	return nil
}

func (l *Layout) GetDomainEvents() []DomainEvent {
	events := make([]DomainEvent, len(l.events))
	copy(events, l.events)
	return events
}

func (l *Layout) ClearDomainEvents() {
	l.events = make([]DomainEvent, 0)
}

func (l *Layout) AddDomainEvent(event DomainEvent) {
	l.events = append(l.events, event)
}

// Layout repository interface
type LayoutRepository interface {
	Repository[*Layout]
	GetByUserID(userID string) (*Layout, error)
	FindByDimensions(dimensions Dimensions) ([]*Layout, error)
}

// Layout domain service
type LayoutService interface {
	DomainService
	CreateLayout(dimensions Dimensions) (*Layout, error)
	ResizeLayout(layoutID LayoutID, dimensions Dimensions) error
	SetFocus(layoutID LayoutID, componentID ComponentID) error
	ToggleSidebar(layoutID LayoutID) error
	OptimizeLayout(layout *Layout) (*Layout, error)
}
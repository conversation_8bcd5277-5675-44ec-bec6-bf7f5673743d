package domain

import (
	"time"
)

// Content Domain Events

// ContentCreatedEvent is fired when new content is created
type ContentCreatedEvent struct {
	eventID    string
	contentID  ContentID
	source     ContentSource
	occurredOn time.Time
	version    int
}

func NewContentCreatedEvent(contentID ContentID, source ContentSource) *ContentCreatedEvent {
	return &ContentCreatedEvent{
		eventID:    generateEventID(),
		contentID:  contentID,
		source:     source,
		occurredOn: time.Now(),
		version:    1,
	}
}

func (e *ContentCreatedEvent) EventID() string {
	return e.eventID
}

func (e *ContentCreatedEvent) EventType() string {
	return "ContentCreated"
}

func (e *ContentCreatedEvent) OccurredOn() time.Time {
	return e.occurredOn
}

func (e *ContentCreatedEvent) AggregateID() string {
	return e.contentID.String()
}

func (e *ContentCreatedEvent) EventVersion() int {
	return e.version
}

func (e *ContentCreatedEvent) ContentID() ContentID {
	return e.contentID
}

func (e *ContentCreatedEvent) Source() ContentSource {
	return e.source
}

// ContentLoadedEvent is fired when content is successfully loaded
type ContentLoadedEvent struct {
	eventID    string
	contentID  ContentID
	size       int64
	metadata   ContentMetadata
	occurredOn time.Time
	version    int
}

func NewContentLoadedEvent(contentID ContentID, size int64, metadata ContentMetadata) *ContentLoadedEvent {
	return &ContentLoadedEvent{
		eventID:    generateEventID(),
		contentID:  contentID,
		size:       size,
		metadata:   metadata,
		occurredOn: time.Now(),
		version:    1,
	}
}

func (e *ContentLoadedEvent) EventID() string {
	return e.eventID
}

func (e *ContentLoadedEvent) EventType() string {
	return "ContentLoaded"
}

func (e *ContentLoadedEvent) OccurredOn() time.Time {
	return e.occurredOn
}

func (e *ContentLoadedEvent) AggregateID() string {
	return e.contentID.String()
}

func (e *ContentLoadedEvent) EventVersion() int {
	return e.version
}

func (e *ContentLoadedEvent) ContentID() ContentID {
	return e.contentID
}

func (e *ContentLoadedEvent) Size() int64 {
	return e.size
}

func (e *ContentLoadedEvent) Metadata() ContentMetadata {
	return e.metadata
}

// ContentStateChangedEvent is fired when content state changes
type ContentStateChangedEvent struct {
	eventID    string
	contentID  ContentID
	oldState   ContentState
	newState   ContentState
	occurredOn time.Time
	version    int
}

func NewContentStateChangedEvent(contentID ContentID, oldState, newState ContentState) *ContentStateChangedEvent {
	return &ContentStateChangedEvent{
		eventID:    generateEventID(),
		contentID:  contentID,
		oldState:   oldState,
		newState:   newState,
		occurredOn: time.Now(),
		version:    1,
	}
}

func (e *ContentStateChangedEvent) EventID() string {
	return e.eventID
}

func (e *ContentStateChangedEvent) EventType() string {
	return "ContentStateChanged"
}

func (e *ContentStateChangedEvent) OccurredOn() time.Time {
	return e.occurredOn
}

func (e *ContentStateChangedEvent) AggregateID() string {
	return e.contentID.String()
}

func (e *ContentStateChangedEvent) EventVersion() int {
	return e.version
}

func (e *ContentStateChangedEvent) ContentID() ContentID {
	return e.contentID
}

func (e *ContentStateChangedEvent) OldState() ContentState {
	return e.oldState
}

func (e *ContentStateChangedEvent) NewState() ContentState {
	return e.newState
}

// ContentErrorEvent is fired when content loading or processing fails
type ContentErrorEvent struct {
	eventID    string
	contentID  ContentID
	error      error
	occurredOn time.Time
	version    int
}

func NewContentErrorEvent(contentID ContentID, err error) *ContentErrorEvent {
	return &ContentErrorEvent{
		eventID:    generateEventID(),
		contentID:  contentID,
		error:      err,
		occurredOn: time.Now(),
		version:    1,
	}
}

func (e *ContentErrorEvent) EventID() string {
	return e.eventID
}

func (e *ContentErrorEvent) EventType() string {
	return "ContentError"
}

func (e *ContentErrorEvent) OccurredOn() time.Time {
	return e.occurredOn
}

func (e *ContentErrorEvent) AggregateID() string {
	return e.contentID.String()
}

func (e *ContentErrorEvent) EventVersion() int {
	return e.version
}

func (e *ContentErrorEvent) ContentID() ContentID {
	return e.contentID
}

func (e *ContentErrorEvent) Error() error {
	return e.error
}

// ContentTransformedEvent is fired when content is transformed
type ContentTransformedEvent struct {
	eventID     string
	contentID   ContentID
	oldMetadata ContentMetadata
	newMetadata ContentMetadata
	occurredOn  time.Time
	version     int
}

func NewContentTransformedEvent(contentID ContentID, oldMetadata, newMetadata ContentMetadata) *ContentTransformedEvent {
	return &ContentTransformedEvent{
		eventID:     generateEventID(),
		contentID:   contentID,
		oldMetadata: oldMetadata,
		newMetadata: newMetadata,
		occurredOn:  time.Now(),
		version:     1,
	}
}

func (e *ContentTransformedEvent) EventID() string {
	return e.eventID
}

func (e *ContentTransformedEvent) EventType() string {
	return "ContentTransformed"
}

func (e *ContentTransformedEvent) OccurredOn() time.Time {
	return e.occurredOn
}

func (e *ContentTransformedEvent) AggregateID() string {
	return e.contentID.String()
}

func (e *ContentTransformedEvent) EventVersion() int {
	return e.version
}

func (e *ContentTransformedEvent) ContentID() ContentID {
	return e.contentID
}

func (e *ContentTransformedEvent) OldMetadata() ContentMetadata {
	return e.oldMetadata
}

func (e *ContentTransformedEvent) NewMetadata() ContentMetadata {
	return e.newMetadata
}

// ContentCachedEvent is fired when content is cached
type ContentCachedEvent struct {
	eventID    string
	contentID  ContentID
	occurredOn time.Time
	version    int
}

func NewContentCachedEvent(contentID ContentID) *ContentCachedEvent {
	return &ContentCachedEvent{
		eventID:    generateEventID(),
		contentID:  contentID,
		occurredOn: time.Now(),
		version:    1,
	}
}

func (e *ContentCachedEvent) EventID() string {
	return e.eventID
}

func (e *ContentCachedEvent) EventType() string {
	return "ContentCached"
}

func (e *ContentCachedEvent) OccurredOn() time.Time {
	return e.occurredOn
}

func (e *ContentCachedEvent) AggregateID() string {
	return e.contentID.String()
}

func (e *ContentCachedEvent) EventVersion() int {
	return e.version
}

func (e *ContentCachedEvent) ContentID() ContentID {
	return e.contentID
}
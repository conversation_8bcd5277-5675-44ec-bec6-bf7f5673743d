// Package domain contains the core domain types and interfaces
package domain

import (
	"time"
)

// Entity is the base interface for all domain entities
type Entity interface {
	ID() string
}

// ValueObject is a marker interface for value objects
type ValueObject interface {
	Equals(other ValueObject) bool
}

// AggregateRoot represents a domain aggregate root
type AggregateRoot interface {
	Entity
	GetDomainEvents() []DomainEvent
	ClearDomainEvents()
	AddDomainEvent(event DomainEvent)
}

// DomainEvent represents something that happened in the domain
type DomainEvent interface {
	EventID() string
	EventType() string
	OccurredOn() time.Time
	AggregateID() string
	EventVersion() int
}

// Repository is the base interface for all repositories
type Repository[T AggregateRoot] interface {
	GetByID(id string) (T, error)
	Save(aggregate T) error
	Delete(id string) error
}

// DomainService represents a domain service
type DomainService interface {
	// Marker interface for domain services
}

// Specification pattern for domain queries
type Specification[T Entity] interface {
	IsSatisfiedBy(candidate T) bool
	And(other Specification[T]) Specification[T]
	Or(other Specification[T]) Specification[T]
	Not() Specification[T]
}
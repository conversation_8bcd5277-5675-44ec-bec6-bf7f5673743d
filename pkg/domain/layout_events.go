package domain

import (
	"time"
)

// Layout Domain Events

// LayoutCreatedEvent is fired when a new layout is created
type LayoutCreatedEvent struct {
	eventID     string
	layoutID    LayoutID
	dimensions  Dimensions
	occurredOn  time.Time
	version     int
}

func NewLayoutCreatedEvent(layoutID LayoutID, dimensions Dimensions) *LayoutCreatedEvent {
	return &LayoutCreatedEvent{
		eventID:    generateEventID(),
		layoutID:   layoutID,
		dimensions: dimensions,
		occurredOn: time.Now(),
		version:    1,
	}
}

func (e *LayoutCreatedEvent) EventID() string {
	return e.eventID
}

func (e *LayoutCreatedEvent) EventType() string {
	return "LayoutCreated"
}

func (e *LayoutCreatedEvent) OccurredOn() time.Time {
	return e.occurredOn
}

func (e *LayoutCreatedEvent) AggregateID() string {
	return e.layoutID.String()
}

func (e *LayoutCreatedEvent) EventVersion() int {
	return e.version
}

func (e *LayoutCreatedEvent) LayoutID() LayoutID {
	return e.layoutID
}

func (e *LayoutCreatedEvent) Dimensions() Dimensions {
	return e.dimensions
}

// LayoutResizedEvent is fired when a layout is resized
type LayoutResizedEvent struct {
	eventID       string
	layoutID      LayoutID
	oldDimensions Dimensions
	newDimensions Dimensions
	occurredOn    time.Time
	version       int
}

func NewLayoutResizedEvent(layoutID LayoutID, oldDimensions, newDimensions Dimensions) *LayoutResizedEvent {
	return &LayoutResizedEvent{
		eventID:       generateEventID(),
		layoutID:      layoutID,
		oldDimensions: oldDimensions,
		newDimensions: newDimensions,
		occurredOn:    time.Now(),
		version:       1,
	}
}

func (e *LayoutResizedEvent) EventID() string {
	return e.eventID
}

func (e *LayoutResizedEvent) EventType() string {
	return "LayoutResized"
}

func (e *LayoutResizedEvent) OccurredOn() time.Time {
	return e.occurredOn
}

func (e *LayoutResizedEvent) AggregateID() string {
	return e.layoutID.String()
}

func (e *LayoutResizedEvent) EventVersion() int {
	return e.version
}

func (e *LayoutResizedEvent) LayoutID() LayoutID {
	return e.layoutID
}

func (e *LayoutResizedEvent) OldDimensions() Dimensions {
	return e.oldDimensions
}

func (e *LayoutResizedEvent) NewDimensions() Dimensions {
	return e.newDimensions
}

// FocusChangedEvent is fired when focus changes between components
type FocusChangedEvent struct {
	eventID      string
	layoutID     LayoutID
	oldComponent ComponentID
	newComponent ComponentID
	occurredOn   time.Time
	version      int
}

func NewFocusChangedEvent(layoutID LayoutID, oldComponent, newComponent ComponentID) *FocusChangedEvent {
	return &FocusChangedEvent{
		eventID:      generateEventID(),
		layoutID:     layoutID,
		oldComponent: oldComponent,
		newComponent: newComponent,
		occurredOn:   time.Now(),
		version:      1,
	}
}

func (e *FocusChangedEvent) EventID() string {
	return e.eventID
}

func (e *FocusChangedEvent) EventType() string {
	return "FocusChanged"
}

func (e *FocusChangedEvent) OccurredOn() time.Time {
	return e.occurredOn
}

func (e *FocusChangedEvent) AggregateID() string {
	return e.layoutID.String()
}

func (e *FocusChangedEvent) EventVersion() int {
	return e.version
}

func (e *FocusChangedEvent) LayoutID() LayoutID {
	return e.layoutID
}

func (e *FocusChangedEvent) OldComponent() ComponentID {
	return e.oldComponent
}

func (e *FocusChangedEvent) NewComponent() ComponentID {
	return e.newComponent
}

// SidebarToggleEvent is fired when sidebar visibility is toggled
type SidebarToggleEvent struct {
	eventID    string
	layoutID   LayoutID
	wasVisible bool
	isVisible  bool
	occurredOn time.Time
	version    int
}

func NewSidebarToggleEvent(layoutID LayoutID, wasVisible, isVisible bool) *SidebarToggleEvent {
	return &SidebarToggleEvent{
		eventID:    generateEventID(),
		layoutID:   layoutID,
		wasVisible: wasVisible,
		isVisible:  isVisible,
		occurredOn: time.Now(),
		version:    1,
	}
}

func (e *SidebarToggleEvent) EventID() string {
	return e.eventID
}

func (e *SidebarToggleEvent) EventType() string {
	return "SidebarToggle"
}

func (e *SidebarToggleEvent) OccurredOn() time.Time {
	return e.occurredOn
}

func (e *SidebarToggleEvent) AggregateID() string {
	return e.layoutID.String()
}

func (e *SidebarToggleEvent) EventVersion() int {
	return e.version
}

func (e *SidebarToggleEvent) LayoutID() LayoutID {
	return e.layoutID
}

func (e *SidebarToggleEvent) WasVisible() bool {
	return e.wasVisible
}

func (e *SidebarToggleEvent) IsVisible() bool {
	return e.isVisible
}

// SidebarResizedEvent is fired when sidebar width changes
type SidebarResizedEvent struct {
	eventID  string
	layoutID LayoutID
	oldWidth int
	newWidth int
	occurredOn time.Time
	version  int
}

func NewSidebarResizedEvent(layoutID LayoutID, oldWidth, newWidth int) *SidebarResizedEvent {
	return &SidebarResizedEvent{
		eventID:    generateEventID(),
		layoutID:   layoutID,
		oldWidth:   oldWidth,
		newWidth:   newWidth,
		occurredOn: time.Now(),
		version:    1,
	}
}

func (e *SidebarResizedEvent) EventID() string {
	return e.eventID
}

func (e *SidebarResizedEvent) EventType() string {
	return "SidebarResized"
}

func (e *SidebarResizedEvent) OccurredOn() time.Time {
	return e.occurredOn
}

func (e *SidebarResizedEvent) AggregateID() string {
	return e.layoutID.String()
}

func (e *SidebarResizedEvent) EventVersion() int {
	return e.version
}

func (e *SidebarResizedEvent) LayoutID() LayoutID {
	return e.layoutID
}

func (e *SidebarResizedEvent) OldWidth() int {
	return e.oldWidth
}

func (e *SidebarResizedEvent) NewWidth() int {
	return e.newWidth
}

// generateEventID generates a unique event ID
func generateEventID() string {
	// Simple implementation - in production, use UUID or similar
	return time.Now().Format("20060102150405.000000") + "-" + randomString(8)
}

func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
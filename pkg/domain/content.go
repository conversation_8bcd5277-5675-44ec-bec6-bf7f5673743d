package domain

import (
	"fmt"
	"mime"
	"time"
)

// ContentID represents a unique content identifier
type ContentID struct {
	value string
}

func NewContentID(value string) ContentID {
	return ContentID{value: value}
}

func (id ContentID) String() string {
	return id.value
}

func (id ContentID) Equals(other ValueObject) bool {
	if otherID, ok := other.(ContentID); ok {
		return id.value == otherID.value
	}
	return false
}

// SourceType represents the type of content source
type SourceType int

const (
	SourceTypeFile SourceType = iota
	SourceTypeString
	SourceTypeHTTP
	SourceTypePlugin
	SourceTypeDatabase
	SourceTypeStream
)

func (st SourceType) String() string {
	switch st {
	case SourceTypeFile:
		return "file"
	case SourceTypeString:
		return "string"
	case SourceTypeHTTP:
		return "http"
	case SourceTypePlugin:
		return "plugin"
	case SourceTypeDatabase:
		return "database"
	case SourceTypeStream:
		return "stream"
	default:
		return "unknown"
	}
}

// ContentSource represents where content comes from
type ContentSource struct {
	Type     SourceType
	Location string
	Options  map[string]interface{}
}

func NewContentSource(sourceType SourceType, location string, options map[string]interface{}) ContentSource {
	if options == nil {
		options = make(map[string]interface{})
	}
	return ContentSource{
		Type:     sourceType,
		Location: location,
		Options:  options,
	}
}

func (cs ContentSource) Equals(other ValueObject) bool {
	if otherCS, ok := other.(ContentSource); ok {
		return cs.Type == otherCS.Type && cs.Location == otherCS.Location
		// Note: Options comparison omitted for simplicity
	}
	return false
}

func (cs ContentSource) GetOption(key string) (interface{}, bool) {
	value, exists := cs.Options[key]
	return value, exists
}

func (cs ContentSource) SetOption(key string, value interface{}) ContentSource {
	newOptions := make(map[string]interface{})
	for k, v := range cs.Options {
		newOptions[k] = v
	}
	newOptions[key] = value
	
	return ContentSource{
		Type:     cs.Type,
		Location: cs.Location,
		Options:  newOptions,
	}
}

// ContentMetadata holds metadata about content
type ContentMetadata struct {
	MimeType     string
	Size         int64
	LastModified time.Time
	Encoding     string
	Checksum     string
	Tags         []string
}

func NewContentMetadata(mimeType string, size int64) ContentMetadata {
	return ContentMetadata{
		MimeType:     mimeType,
		Size:         size,
		LastModified: time.Now(),
		Encoding:     "utf-8",
		Tags:         make([]string, 0),
	}
}

func (cm ContentMetadata) Equals(other ValueObject) bool {
	if otherCM, ok := other.(ContentMetadata); ok {
		return cm.MimeType == otherCM.MimeType &&
			cm.Size == otherCM.Size &&
			cm.Encoding == otherCM.Encoding &&
			cm.Checksum == otherCM.Checksum
	}
	return false
}

func (cm ContentMetadata) IsText() bool {
	mediaType, _, err := mime.ParseMediaType(cm.MimeType)
	if err != nil {
		return false
	}
	return mediaType == "text/plain" || mediaType == "text/html" || mediaType == "application/json"
}

func (cm ContentMetadata) IsBinary() bool {
	return !cm.IsText()
}

// ContentState represents the current state of content
type ContentState int

const (
	ContentStateUnloaded ContentState = iota
	ContentStateLoading
	ContentStateLoaded
	ContentStateError
	ContentStateCached
	ContentStateTransformed
)

func (cs ContentState) String() string {
	switch cs {
	case ContentStateUnloaded:
		return "unloaded"
	case ContentStateLoading:
		return "loading"
	case ContentStateLoaded:
		return "loaded"
	case ContentStateError:
		return "error"
	case ContentStateCached:
		return "cached"
	case ContentStateTransformed:
		return "transformed"
	default:
		return "unknown"
	}
}

// Content aggregate root
type Content struct {
	id       ContentID
	source   ContentSource
	data     []byte
	metadata ContentMetadata
	state    ContentState
	error    error
	version  int
	events   []DomainEvent
	loadedAt time.Time
}

func NewContent(id ContentID, source ContentSource) *Content {
	content := &Content{
		id:       id,
		source:   source,
		data:     nil,
		metadata: ContentMetadata{},
		state:    ContentStateUnloaded,
		version:  1,
		events:   make([]DomainEvent, 0),
		loadedAt: time.Time{},
	}
	
	content.AddDomainEvent(NewContentCreatedEvent(id, source))
	return content
}

func (c *Content) ID() string {
	return c.id.String()
}

func (c *Content) Source() ContentSource {
	return c.source
}

func (c *Content) Data() []byte {
	// Return copy to prevent external modification
	if c.data == nil {
		return nil
	}
	data := make([]byte, len(c.data))
	copy(data, c.data)
	return data
}

func (c *Content) Metadata() ContentMetadata {
	return c.metadata
}

func (c *Content) State() ContentState {
	return c.state
}

func (c *Content) Error() error {
	return c.error
}

func (c *Content) Version() int {
	return c.version
}

func (c *Content) LoadedAt() time.Time {
	return c.loadedAt
}

func (c *Content) IsLoaded() bool {
	return c.state == ContentStateLoaded || c.state == ContentStateCached || c.state == ContentStateTransformed
}

func (c *Content) SetLoading() {
	if c.state == ContentStateLoading {
		return // Already loading
	}
	
	oldState := c.state
	c.state = ContentStateLoading
	c.error = nil
	c.version++
	c.AddDomainEvent(NewContentStateChangedEvent(c.id, oldState, c.state))
}

func (c *Content) SetLoaded(data []byte, metadata ContentMetadata) error {
	if c.state != ContentStateLoading {
		return fmt.Errorf("content must be in loading state to set loaded")
	}
	
	if data == nil {
		return fmt.Errorf("data cannot be nil")
	}
	
	oldState := c.state
	c.data = make([]byte, len(data))
	copy(c.data, data)
	c.metadata = metadata
	c.state = ContentStateLoaded
	c.error = nil
	c.loadedAt = time.Now()
	c.version++
	
	c.AddDomainEvent(NewContentLoadedEvent(c.id, int64(len(data)), metadata))
	c.AddDomainEvent(NewContentStateChangedEvent(c.id, oldState, c.state))
	
	return nil
}

func (c *Content) SetError(err error) {
	if err == nil {
		return
	}
	
	oldState := c.state
	c.state = ContentStateError
	c.error = err
	c.version++
	c.AddDomainEvent(NewContentErrorEvent(c.id, err))
	c.AddDomainEvent(NewContentStateChangedEvent(c.id, oldState, c.state))
}

func (c *Content) Transform(data []byte, newMetadata ContentMetadata) error {
	if !c.IsLoaded() {
		return fmt.Errorf("content must be loaded to transform")
	}
	
	if data == nil {
		return fmt.Errorf("transformed data cannot be nil")
	}
	
	oldState := c.state
	oldMetadata := c.metadata
	
	c.data = make([]byte, len(data))
	copy(c.data, data)
	c.metadata = newMetadata
	c.state = ContentStateTransformed
	c.version++
	
	c.AddDomainEvent(NewContentTransformedEvent(c.id, oldMetadata, newMetadata))
	c.AddDomainEvent(NewContentStateChangedEvent(c.id, oldState, c.state))
	
	return nil
}

func (c *Content) Cache() {
	if c.state == ContentStateCached {
		return // Already cached
	}
	
	if !c.IsLoaded() {
		return // Can only cache loaded content
	}
	
	oldState := c.state
	c.state = ContentStateCached
	c.version++
	c.AddDomainEvent(NewContentCachedEvent(c.id))
	c.AddDomainEvent(NewContentStateChangedEvent(c.id, oldState, c.state))
}

func (c *Content) GetDomainEvents() []DomainEvent {
	events := make([]DomainEvent, len(c.events))
	copy(events, c.events)
	return events
}

func (c *Content) ClearDomainEvents() {
	c.events = make([]DomainEvent, 0)
}

func (c *Content) AddDomainEvent(event DomainEvent) {
	c.events = append(c.events, event)
}

// Content repository interface
type ContentRepository interface {
	Repository[*Content]
	FindBySource(source ContentSource) ([]*Content, error)
	FindByState(state ContentState) ([]*Content, error)
	FindByMimeType(mimeType string) ([]*Content, error)
}

// Transformer interface for content transformation
type Transformer interface {
	CanTransform(content *Content) bool
	Transform(content *Content, options map[string]interface{}) ([]byte, ContentMetadata, error)
	Name() string
}

// Content domain service
type ContentService interface {
	DomainService
	LoadContent(source ContentSource) (*Content, error)
	TransformContent(content *Content, transformer Transformer, options map[string]interface{}) error
	CacheContent(content *Content) error
	ValidateContent(content *Content) error
}
package eventsourcing

import (
	"context"
	"testing"
	"time"

	"eionz.com/demo/pkg/domain"
)

func TestInMemoryEventStore_SaveAndLoadEvents(t *testing.T) {
	store := NewInMemoryEventStore()
	ctx := context.Background()
	
	// Create test events
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	
	events := []domain.DomainEvent{
		domain.NewLayoutCreatedEvent(layoutID, dimensions),
		domain.NewLayoutResizedEvent(layoutID, dimensions, domain.Dimensions{Width: 120, Height: 60}),
	}
	
	// Save events
	err := store.SaveEvents(ctx, "test-layout", events, 0)
	if err != nil {
		t.Fatalf("Failed to save events: %v", err)
	}
	
	// Load all events
	loadedEvents, err := store.LoadAllEvents(ctx, "test-layout")
	if err != nil {
		t.Fatalf("Failed to load events: %v", err)
	}
	
	if len(loadedEvents) != 2 {
		t.Errorf("Expected 2 events, got %d", len(loadedEvents))
	}
	
	// Verify event types
	if loadedEvents[0].EventType() != "LayoutCreated" {
		t.Errorf("Expected first event to be LayoutCreated, got %s", loadedEvents[0].EventType())
	}
	
	if loadedEvents[1].EventType() != "LayoutResized" {
		t.Errorf("Expected second event to be LayoutResized, got %s", loadedEvents[1].EventType())
	}
}

func TestInMemoryEventStore_LoadEventsFromVersion(t *testing.T) {
	store := NewInMemoryEventStore()
	ctx := context.Background()
	
	// Create test events
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	
	// Save first batch
	events1 := []domain.DomainEvent{
		domain.NewLayoutCreatedEvent(layoutID, dimensions),
		domain.NewLayoutResizedEvent(layoutID, dimensions, domain.Dimensions{Width: 120, Height: 60}),
	}
	store.SaveEvents(ctx, "test-layout", events1, 0)
	
	// Save second batch
	events2 := []domain.DomainEvent{
		domain.NewFocusChangedEvent(layoutID, domain.NewComponentID("old"), domain.NewComponentID("new")),
	}
	store.SaveEvents(ctx, "test-layout", events2, 2)
	
	// Load events from version 2
	loadedEvents, err := store.LoadEvents(ctx, "test-layout", 2)
	if err != nil {
		t.Fatalf("Failed to load events from version: %v", err)
	}
	
	if len(loadedEvents) != 1 {
		t.Errorf("Expected 1 event from version 2, got %d", len(loadedEvents))
	}
	
	if loadedEvents[0].EventType() != "FocusChanged" {
		t.Errorf("Expected FocusChanged event, got %s", loadedEvents[0].EventType())
	}
}

func TestInMemoryEventStore_ConcurrencyControl(t *testing.T) {
	store := NewInMemoryEventStore()
	ctx := context.Background()
	
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	
	// Save initial events
	events1 := []domain.DomainEvent{
		domain.NewLayoutCreatedEvent(layoutID, dimensions),
	}
	store.SaveEvents(ctx, "test-layout", events1, 0)
	
	// Try to save with wrong expected version
	events2 := []domain.DomainEvent{
		domain.NewLayoutResizedEvent(layoutID, dimensions, domain.Dimensions{Width: 120, Height: 60}),
	}
	err := store.SaveEvents(ctx, "test-layout", events2, 0) // Wrong version
	if err == nil {
		t.Error("Expected concurrency error when saving with wrong version")
	}
	
	// Save with correct expected version
	err = store.SaveEvents(ctx, "test-layout", events2, 1) // Correct version
	if err != nil {
		t.Errorf("Failed to save with correct version: %v", err)
	}
}

func TestInMemoryEventStore_Snapshots(t *testing.T) {
	store := NewInMemoryEventStore()
	ctx := context.Background()
	
	aggregateID := "test-aggregate"
	aggregateData := []byte(`{"id":"test","version":5}`)
	
	// Create snapshot
	err := store.CreateSnapshot(ctx, aggregateID, aggregateData, 5)
	if err != nil {
		t.Fatalf("Failed to create snapshot: %v", err)
	}
	
	// Load snapshot
	snapshot, err := store.LoadSnapshot(ctx, aggregateID)
	if err != nil {
		t.Fatalf("Failed to load snapshot: %v", err)
	}
	
	if snapshot.AggregateID != aggregateID {
		t.Errorf("Expected aggregate ID %s, got %s", aggregateID, snapshot.AggregateID)
	}
	
	if snapshot.Version != 5 {
		t.Errorf("Expected version 5, got %d", snapshot.Version)
	}
	
	if string(snapshot.Data) != string(aggregateData) {
		t.Errorf("Snapshot data mismatch")
	}
}

func TestInMemoryEventStore_GetEventsByType(t *testing.T) {
	store := NewInMemoryEventStore()
	ctx := context.Background()
	
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	
	// Save mixed events
	events := []domain.DomainEvent{
		domain.NewLayoutCreatedEvent(layoutID, dimensions),
		domain.NewLayoutResizedEvent(layoutID, dimensions, domain.Dimensions{Width: 120, Height: 60}),
		domain.NewLayoutCreatedEvent(domain.NewLayoutID("layout2"), dimensions),
	}
	store.SaveEvents(ctx, "test-layout", events[:2], 0)
	store.SaveEvents(ctx, "test-layout2", events[2:], 0)
	
	// Get only LayoutCreated events
	createdEvents, err := store.GetEventsByType(ctx, "LayoutCreated", time.Time{})
	if err != nil {
		t.Fatalf("Failed to get events by type: %v", err)
	}
	
	if len(createdEvents) != 2 {
		t.Errorf("Expected 2 LayoutCreated events, got %d", len(createdEvents))
	}
	
	for _, event := range createdEvents {
		if event.EventType() != "LayoutCreated" {
			t.Errorf("Expected LayoutCreated event, got %s", event.EventType())
		}
	}
}

func TestInMemoryEventStore_GetEventsInTimeRange(t *testing.T) {
	store := NewInMemoryEventStore()
	ctx := context.Background()
	
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	
	// Create events with different timestamps
	now := time.Now()
	event1 := domain.NewLayoutCreatedEvent(layoutID, dimensions)
	event2 := domain.NewLayoutResizedEvent(layoutID, dimensions, domain.Dimensions{Width: 120, Height: 60})
	
	events := []domain.DomainEvent{event1, event2}
	store.SaveEvents(ctx, "test-layout", events, 0)
	
	// Get events in time range
	startTime := now.Add(-1 * time.Hour)
	endTime := now.Add(1 * time.Hour)
	
	rangeEvents, err := store.GetEventsInTimeRange(ctx, startTime, endTime)
	if err != nil {
		t.Fatalf("Failed to get events in time range: %v", err)
	}
	
	if len(rangeEvents) != 2 {
		t.Errorf("Expected 2 events in time range, got %d", len(rangeEvents))
	}
}

func TestInMemoryEventStore_GetAggregateVersion(t *testing.T) {
	store := NewInMemoryEventStore()
	ctx := context.Background()
	
	// Check version for non-existent aggregate
	version, err := store.GetAggregateVersion(ctx, "non-existent")
	if err != nil {
		t.Fatalf("Failed to get version for non-existent aggregate: %v", err)
	}
	
	if version != 0 {
		t.Errorf("Expected version 0 for non-existent aggregate, got %d", version)
	}
	
	// Save events and check version
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	events := []domain.DomainEvent{
		domain.NewLayoutCreatedEvent(layoutID, dimensions),
		domain.NewLayoutResizedEvent(layoutID, dimensions, domain.Dimensions{Width: 120, Height: 60}),
	}
	store.SaveEvents(ctx, "test-layout", events, 0)
	
	version, err = store.GetAggregateVersion(ctx, "test-layout")
	if err != nil {
		t.Fatalf("Failed to get aggregate version: %v", err)
	}
	
	if version != 2 {
		t.Errorf("Expected version 2, got %d", version)
	}
}

func TestInMemoryEventStore_DeleteEvents(t *testing.T) {
	store := NewInMemoryEventStore()
	ctx := context.Background()
	
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	
	// Save events
	events := []domain.DomainEvent{
		domain.NewLayoutCreatedEvent(layoutID, dimensions),
		domain.NewLayoutResizedEvent(layoutID, dimensions, domain.Dimensions{Width: 120, Height: 60}),
		domain.NewFocusChangedEvent(layoutID, domain.NewComponentID("old"), domain.NewComponentID("new")),
	}
	store.SaveEvents(ctx, "test-layout", events, 0)
	
	// Delete events up to version 2
	err := store.DeleteEvents(ctx, "test-layout", 2)
	if err != nil {
		t.Fatalf("Failed to delete events: %v", err)
	}
	
	// Check remaining events
	remainingEvents, err := store.LoadAllEvents(ctx, "test-layout")
	if err != nil {
		t.Fatalf("Failed to load remaining events: %v", err)
	}
	
	if len(remainingEvents) != 1 {
		t.Errorf("Expected 1 remaining event, got %d", len(remainingEvents))
	}
	
	if remainingEvents[0].EventType() != "FocusChanged" {
		t.Errorf("Expected remaining event to be FocusChanged, got %s", remainingEvents[0].EventType())
	}
}

func TestInMemoryEventStore_GetEventCount(t *testing.T) {
	store := NewInMemoryEventStore()
	ctx := context.Background()
	
	// Initially should be 0
	count, err := store.GetEventCount(ctx)
	if err != nil {
		t.Fatalf("Failed to get event count: %v", err)
	}
	
	if count != 0 {
		t.Errorf("Expected initial count 0, got %d", count)
	}
	
	// Add events
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	events := []domain.DomainEvent{
		domain.NewLayoutCreatedEvent(layoutID, dimensions),
		domain.NewLayoutResizedEvent(layoutID, dimensions, domain.Dimensions{Width: 120, Height: 60}),
	}
	store.SaveEvents(ctx, "test-layout", events, 0)
	
	// Check count
	count, err = store.GetEventCount(ctx)
	if err != nil {
		t.Fatalf("Failed to get event count: %v", err)
	}
	
	if count != 2 {
		t.Errorf("Expected count 2, got %d", count)
	}
}

func TestInMemoryEventStore_GetEventTypes(t *testing.T) {
	store := NewInMemoryEventStore()
	ctx := context.Background()
	
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	
	// Save events of different types
	events := []domain.DomainEvent{
		domain.NewLayoutCreatedEvent(layoutID, dimensions),
		domain.NewLayoutResizedEvent(layoutID, dimensions, domain.Dimensions{Width: 120, Height: 60}),
		domain.NewFocusChangedEvent(layoutID, domain.NewComponentID("old"), domain.NewComponentID("new")),
	}
	store.SaveEvents(ctx, "test-layout", events, 0)
	
	// Get event types
	eventTypes, err := store.GetEventTypes(ctx)
	if err != nil {
		t.Fatalf("Failed to get event types: %v", err)
	}
	
	expectedTypes := []string{"FocusChanged", "LayoutCreated", "LayoutResized"}
	if len(eventTypes) != len(expectedTypes) {
		t.Errorf("Expected %d event types, got %d", len(expectedTypes), len(eventTypes))
	}
	
	// Check that all expected types are present (order may vary due to sorting)
	typeMap := make(map[string]bool)
	for _, eventType := range eventTypes {
		typeMap[eventType] = true
	}
	
	for _, expectedType := range expectedTypes {
		if !typeMap[expectedType] {
			t.Errorf("Expected event type %s not found", expectedType)
		}
	}
}

func TestInMemoryEventStream(t *testing.T) {
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	
	events := []domain.DomainEvent{
		domain.NewLayoutCreatedEvent(layoutID, dimensions),
		domain.NewLayoutResizedEvent(layoutID, dimensions, domain.Dimensions{Width: 120, Height: 60}),
	}
	
	stream := NewInMemoryEventStream("test-layout", 2, events)
	
	// Test stream properties
	if stream.AggregateID() != "test-layout" {
		t.Errorf("Expected aggregate ID 'test-layout', got %s", stream.AggregateID())
	}
	
	if stream.Version() != 2 {
		t.Errorf("Expected version 2, got %d", stream.Version())
	}
	
	if len(stream.Events()) != 2 {
		t.Errorf("Expected 2 events, got %d", len(stream.Events()))
	}
	
	// Test iteration
	count := 0
	for stream.HasNext() {
		event := stream.Next()
		if event == nil {
			t.Error("Event should not be nil")
		}
		count++
	}
	
	if count != 2 {
		t.Errorf("Expected to iterate over 2 events, got %d", count)
	}
	
	// Test reset
	stream.Reset()
	if !stream.HasNext() {
		t.Error("Stream should have events after reset")
	}
}

func TestInMemoryEventStore_GetEventStream(t *testing.T) {
	store := NewInMemoryEventStore()
	ctx := context.Background()
	
	layoutID := domain.NewLayoutID("test-layout")
	dimensions, _ := domain.NewDimensions(100, 50)
	
	events := []domain.DomainEvent{
		domain.NewLayoutCreatedEvent(layoutID, dimensions),
		domain.NewLayoutResizedEvent(layoutID, dimensions, domain.Dimensions{Width: 120, Height: 60}),
	}
	store.SaveEvents(ctx, "test-layout", events, 0)
	
	// Get event stream
	stream, err := store.GetEventStream(ctx, "test-layout")
	if err != nil {
		t.Fatalf("Failed to get event stream: %v", err)
	}
	
	if stream.AggregateID() != "test-layout" {
		t.Errorf("Expected aggregate ID 'test-layout', got %s", stream.AggregateID())
	}
	
	if stream.Version() != 2 {
		t.Errorf("Expected version 2, got %d", stream.Version())
	}
	
	// Test streaming events
	eventCount := 0
	for stream.HasNext() {
		event := stream.Next()
		if event == nil {
			t.Error("Event should not be nil")
		}
		eventCount++
	}
	
	if eventCount != 2 {
		t.Errorf("Expected to stream 2 events, got %d", eventCount)
	}
}
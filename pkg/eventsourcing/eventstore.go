// Package eventsourcing implements event sourcing infrastructure
package eventsourcing

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"time"

	"eionz.com/demo/pkg/domain"
)

// EventStore manages the storage and retrieval of domain events
type EventStore interface {
	// Store operations
	SaveEvents(ctx context.Context, aggregateID string, events []domain.DomainEvent, expectedVersion int) error
	LoadEvents(ctx context.Context, aggregateID string, fromVersion int) ([]domain.DomainEvent, error)
	LoadAllEvents(ctx context.Context, aggregateID string) ([]domain.DomainEvent, error)
	
	// Stream operations
	GetEventStream(ctx context.Context, aggregateID string) (EventStream, error)
	CreateSnapshot(ctx context.Context, aggregateID string, aggregateData []byte, version int) error
	LoadSnapshot(ctx context.Context, aggregateID string) (*Snapshot, error)
	
	// Query operations
	GetEventsByType(ctx context.Context, eventType string, fromTimestamp time.Time) ([]domain.DomainEvent, error)
	GetEventsInTimeRange(ctx context.Context, startTime, endTime time.Time) ([]domain.DomainEvent, error)
	GetAggregateVersion(ctx context.Context, aggregateID string) (int, error)
	
	// Management operations
	DeleteEvents(ctx context.Context, aggregateID string, toVersion int) error
	GetEventCount(ctx context.Context) (int64, error)
	GetEventTypes(ctx context.Context) ([]string, error)
}

// EventStream represents a stream of events for an aggregate
type EventStream interface {
	AggregateID() string
	Version() int
	Events() []domain.DomainEvent
	HasNext() bool
	Next() domain.DomainEvent
	Reset()
}

// Snapshot represents a point-in-time state of an aggregate
type Snapshot struct {
	AggregateID   string
	AggregateType string
	Version       int
	Data          []byte
	CreatedAt     time.Time
	Metadata      map[string]interface{}
}

// StoredEvent represents an event as stored in the event store
type StoredEvent struct {
	ID            string
	AggregateID   string
	AggregateType string
	EventType     string
	EventData     []byte
	Metadata      []byte
	Version       int
	Sequence      int64
	Timestamp     time.Time
}

// EventMetadata contains additional information about an event
type EventMetadata struct {
	Source        string
	CorrelationID string
	CausationID   string
	UserID        string
	SessionID     string
	IPAddress     string
	UserAgent     string
	Custom        map[string]interface{}
}

// InMemoryEventStore provides an in-memory implementation of EventStore
type InMemoryEventStore struct {
	events     map[string][]StoredEvent // aggregateID -> events
	snapshots  map[string]*Snapshot     // aggregateID -> snapshot
	sequence   int64                    // Global sequence number
	mutex      sync.RWMutex
	eventTypes map[string]bool // Track unique event types
}

func NewInMemoryEventStore() *InMemoryEventStore {
	return &InMemoryEventStore{
		events:     make(map[string][]StoredEvent),
		snapshots:  make(map[string]*Snapshot),
		sequence:   0,
		eventTypes: make(map[string]bool),
	}
}

func (es *InMemoryEventStore) SaveEvents(ctx context.Context, aggregateID string, events []domain.DomainEvent, expectedVersion int) error {
	if len(events) == 0 {
		return nil
	}
	
	es.mutex.Lock()
	defer es.mutex.Unlock()
	
	// Check version concurrency
	existingEvents, exists := es.events[aggregateID]
	currentVersion := 0
	if exists {
		currentVersion = len(existingEvents)
	}
	
	if expectedVersion != -1 && currentVersion != expectedVersion {
		return fmt.Errorf("concurrency conflict: expected version %d, but current version is %d", expectedVersion, currentVersion)
	}
	
	// Convert domain events to stored events
	var storedEvents []StoredEvent
	for i, event := range events {
		// Serialize event data
		eventData, err := json.Marshal(event)
		if err != nil {
			return fmt.Errorf("failed to serialize event: %w", err)
		}
		
		// Create metadata
		metadata := EventMetadata{
			Source: "event-store",
		}
		metadataBytes, err := json.Marshal(metadata)
		if err != nil {
			return fmt.Errorf("failed to serialize metadata: %w", err)
		}
		
		es.sequence++
		storedEvent := StoredEvent{
			ID:            generateEventID(),
			AggregateID:   aggregateID,
			AggregateType: "unknown", // Would be determined from aggregate
			EventType:     event.EventType(),
			EventData:     eventData,
			Metadata:      metadataBytes,
			Version:       currentVersion + i + 1,
			Sequence:      es.sequence,
			Timestamp:     event.OccurredOn(),
		}
		
		storedEvents = append(storedEvents, storedEvent)
		es.eventTypes[event.EventType()] = true
	}
	
	// Store events
	if !exists {
		es.events[aggregateID] = storedEvents
	} else {
		es.events[aggregateID] = append(es.events[aggregateID], storedEvents...)
	}
	
	return nil
}

func (es *InMemoryEventStore) LoadEvents(ctx context.Context, aggregateID string, fromVersion int) ([]domain.DomainEvent, error) {
	es.mutex.RLock()
	defer es.mutex.RUnlock()
	
	storedEvents, exists := es.events[aggregateID]
	if !exists {
		return []domain.DomainEvent{}, nil
	}
	
	var events []domain.DomainEvent
	for _, stored := range storedEvents {
		if stored.Version > fromVersion {
			event, err := es.deserializeEvent(stored)
			if err != nil {
				return nil, fmt.Errorf("failed to deserialize event: %w", err)
			}
			events = append(events, event)
		}
	}
	
	return events, nil
}

func (es *InMemoryEventStore) LoadAllEvents(ctx context.Context, aggregateID string) ([]domain.DomainEvent, error) {
	return es.LoadEvents(ctx, aggregateID, 0)
}

func (es *InMemoryEventStore) GetEventStream(ctx context.Context, aggregateID string) (EventStream, error) {
	events, err := es.LoadAllEvents(ctx, aggregateID)
	if err != nil {
		return nil, err
	}
	
	version := len(events)
	if storedEvents, exists := es.events[aggregateID]; exists {
		version = len(storedEvents)
	}
	
	return NewInMemoryEventStream(aggregateID, version, events), nil
}

func (es *InMemoryEventStore) CreateSnapshot(ctx context.Context, aggregateID string, aggregateData []byte, version int) error {
	es.mutex.Lock()
	defer es.mutex.Unlock()
	
	snapshot := &Snapshot{
		AggregateID:   aggregateID,
		AggregateType: "unknown",
		Version:       version,
		Data:          aggregateData,
		CreatedAt:     time.Now(),
		Metadata:      make(map[string]interface{}),
	}
	
	es.snapshots[aggregateID] = snapshot
	return nil
}

func (es *InMemoryEventStore) LoadSnapshot(ctx context.Context, aggregateID string) (*Snapshot, error) {
	es.mutex.RLock()
	defer es.mutex.RUnlock()
	
	snapshot, exists := es.snapshots[aggregateID]
	if !exists {
		return nil, fmt.Errorf("snapshot not found for aggregate %s", aggregateID)
	}
	
	return snapshot, nil
}

func (es *InMemoryEventStore) GetEventsByType(ctx context.Context, eventType string, fromTimestamp time.Time) ([]domain.DomainEvent, error) {
	es.mutex.RLock()
	defer es.mutex.RUnlock()
	
	var events []domain.DomainEvent
	for _, storedEvents := range es.events {
		for _, stored := range storedEvents {
			if stored.EventType == eventType && stored.Timestamp.After(fromTimestamp) {
				event, err := es.deserializeEvent(stored)
				if err != nil {
					continue // Skip events that can't be deserialized
				}
				events = append(events, event)
			}
		}
	}
	
	// Sort by timestamp
	sort.Slice(events, func(i, j int) bool {
		return events[i].OccurredOn().Before(events[j].OccurredOn())
	})
	
	return events, nil
}

func (es *InMemoryEventStore) GetEventsInTimeRange(ctx context.Context, startTime, endTime time.Time) ([]domain.DomainEvent, error) {
	es.mutex.RLock()
	defer es.mutex.RUnlock()
	
	var events []domain.DomainEvent
	for _, storedEvents := range es.events {
		for _, stored := range storedEvents {
			if stored.Timestamp.After(startTime) && stored.Timestamp.Before(endTime) {
				event, err := es.deserializeEvent(stored)
				if err != nil {
					continue
				}
				events = append(events, event)
			}
		}
	}
	
	sort.Slice(events, func(i, j int) bool {
		return events[i].OccurredOn().Before(events[j].OccurredOn())
	})
	
	return events, nil
}

func (es *InMemoryEventStore) GetAggregateVersion(ctx context.Context, aggregateID string) (int, error) {
	es.mutex.RLock()
	defer es.mutex.RUnlock()
	
	storedEvents, exists := es.events[aggregateID]
	if !exists {
		return 0, nil
	}
	
	return len(storedEvents), nil
}

func (es *InMemoryEventStore) DeleteEvents(ctx context.Context, aggregateID string, toVersion int) error {
	es.mutex.Lock()
	defer es.mutex.Unlock()
	
	storedEvents, exists := es.events[aggregateID]
	if !exists {
		return nil
	}
	
	// Keep events after the specified version
	var remainingEvents []StoredEvent
	for _, stored := range storedEvents {
		if stored.Version > toVersion {
			remainingEvents = append(remainingEvents, stored)
		}
	}
	
	if len(remainingEvents) == 0 {
		delete(es.events, aggregateID)
	} else {
		es.events[aggregateID] = remainingEvents
	}
	
	return nil
}

func (es *InMemoryEventStore) GetEventCount(ctx context.Context) (int64, error) {
	es.mutex.RLock()
	defer es.mutex.RUnlock()
	
	var count int64
	for _, events := range es.events {
		count += int64(len(events))
	}
	
	return count, nil
}

func (es *InMemoryEventStore) GetEventTypes(ctx context.Context) ([]string, error) {
	es.mutex.RLock()
	defer es.mutex.RUnlock()
	
	var types []string
	for eventType := range es.eventTypes {
		types = append(types, eventType)
	}
	
	sort.Strings(types)
	return types, nil
}

// Helper methods

func (es *InMemoryEventStore) deserializeEvent(stored StoredEvent) (domain.DomainEvent, error) {
	// This is a simplified deserialization
	// In a real implementation, you'd have a registry of event types and deserializers
	
	switch stored.EventType {
	case "LayoutCreated":
		var event domain.LayoutCreatedEvent
		if err := json.Unmarshal(stored.EventData, &event); err != nil {
			return nil, err
		}
		return &event, nil
		
	case "LayoutResized":
		var event domain.LayoutResizedEvent
		if err := json.Unmarshal(stored.EventData, &event); err != nil {
			return nil, err
		}
		return &event, nil
		
	case "FocusChanged":
		var event domain.FocusChangedEvent
		if err := json.Unmarshal(stored.EventData, &event); err != nil {
			return nil, err
		}
		return &event, nil
		
	case "SidebarToggle":
		var event domain.SidebarToggleEvent
		if err := json.Unmarshal(stored.EventData, &event); err != nil {
			return nil, err
		}
		return &event, nil
		
	case "ContentCreated":
		var event domain.ContentCreatedEvent
		if err := json.Unmarshal(stored.EventData, &event); err != nil {
			return nil, err
		}
		return &event, nil
		
	case "ContentLoaded":
		var event domain.ContentLoadedEvent
		if err := json.Unmarshal(stored.EventData, &event); err != nil {
			return nil, err
		}
		return &event, nil
		
	default:
		return nil, fmt.Errorf("unknown event type: %s", stored.EventType)
	}
}

// InMemoryEventStream implements EventStream interface
type InMemoryEventStream struct {
	aggregateID string
	version     int
	events      []domain.DomainEvent
	position    int
}

func NewInMemoryEventStream(aggregateID string, version int, events []domain.DomainEvent) *InMemoryEventStream {
	return &InMemoryEventStream{
		aggregateID: aggregateID,
		version:     version,
		events:      events,
		position:    0,
	}
}

func (s *InMemoryEventStream) AggregateID() string {
	return s.aggregateID
}

func (s *InMemoryEventStream) Version() int {
	return s.version
}

func (s *InMemoryEventStream) Events() []domain.DomainEvent {
	return s.events
}

func (s *InMemoryEventStream) HasNext() bool {
	return s.position < len(s.events)
}

func (s *InMemoryEventStream) Next() domain.DomainEvent {
	if !s.HasNext() {
		return nil
	}
	
	event := s.events[s.position]
	s.position++
	return event
}

func (s *InMemoryEventStream) Reset() {
	s.position = 0
}

// EventSourcedAggregate represents an aggregate that is built from events
type EventSourcedAggregate interface {
	domain.AggregateRoot
	LoadFromHistory(events []domain.DomainEvent) error
	GetUncommittedEvents() []domain.DomainEvent
	MarkEventsAsCommitted()
}

// AggregateRepository manages event-sourced aggregates
type AggregateRepository interface {
	Save(ctx context.Context, aggregate EventSourcedAggregate, expectedVersion int) error
	GetByID(ctx context.Context, aggregateID string) (EventSourcedAggregate, error)
	GetVersion(ctx context.Context, aggregateID string) (int, error)
	Delete(ctx context.Context, aggregateID string) error
}

// EventSourcedAggregateRepository implements AggregateRepository
type EventSourcedAggregateRepository struct {
	eventStore      EventStore
	aggregateFactory func(string) EventSourcedAggregate
	snapshotFrequency int // Create snapshot every N events
}

func NewEventSourcedAggregateRepository(eventStore EventStore, factory func(string) EventSourcedAggregate) *EventSourcedAggregateRepository {
	return &EventSourcedAggregateRepository{
		eventStore:       eventStore,
		aggregateFactory: factory,
		snapshotFrequency: 10, // Default: snapshot every 10 events
	}
}

func (repo *EventSourcedAggregateRepository) Save(ctx context.Context, aggregate EventSourcedAggregate, expectedVersion int) error {
	uncommittedEvents := aggregate.GetUncommittedEvents()
	if len(uncommittedEvents) == 0 {
		return nil
	}
	
	// Save events to event store
	err := repo.eventStore.SaveEvents(ctx, aggregate.ID(), uncommittedEvents, expectedVersion)
	if err != nil {
		return err
	}
	
	// Mark events as committed
	aggregate.MarkEventsAsCommitted()
	
	// Check if we should create a snapshot
	currentVersion, err := repo.eventStore.GetAggregateVersion(ctx, aggregate.ID())
	if err != nil {
		return err
	}
	
	if currentVersion%repo.snapshotFrequency == 0 {
		// Create snapshot
		aggregateData, err := json.Marshal(aggregate)
		if err == nil { // Don't fail if snapshot creation fails
			repo.eventStore.CreateSnapshot(ctx, aggregate.ID(), aggregateData, currentVersion)
		}
	}
	
	return nil
}

func (repo *EventSourcedAggregateRepository) GetByID(ctx context.Context, aggregateID string) (EventSourcedAggregate, error) {
	aggregate := repo.aggregateFactory(aggregateID)
	
	// Try to load from snapshot first
	var fromVersion int
	snapshot, err := repo.eventStore.LoadSnapshot(ctx, aggregateID)
	if err == nil {
		// Load aggregate from snapshot
		if err := json.Unmarshal(snapshot.Data, aggregate); err == nil {
			fromVersion = snapshot.Version
		}
	}
	
	// Load events from the snapshot version onwards
	events, err := repo.eventStore.LoadEvents(ctx, aggregateID, fromVersion)
	if err != nil {
		return nil, err
	}
	
	// Apply events to aggregate
	if err := aggregate.LoadFromHistory(events); err != nil {
		return nil, err
	}
	
	return aggregate, nil
}

func (repo *EventSourcedAggregateRepository) GetVersion(ctx context.Context, aggregateID string) (int, error) {
	return repo.eventStore.GetAggregateVersion(ctx, aggregateID)
}

func (repo *EventSourcedAggregateRepository) Delete(ctx context.Context, aggregateID string) error {
	// In event sourcing, we typically don't delete events
	// Instead, we could publish a "deleted" event
	// For this implementation, we'll actually delete for simplicity
	return repo.eventStore.DeleteEvents(ctx, aggregateID, 0)
}

func (repo *EventSourcedAggregateRepository) SetSnapshotFrequency(frequency int) {
	repo.snapshotFrequency = frequency
}

// Utility functions

func generateEventID() string {
	return fmt.Sprintf("event-%d", time.Now().UnixNano())
}
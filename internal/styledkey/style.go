package styledkey

import "github.com/charmbracelet/lipgloss"

// Style defines different styles for a key binding
type Style struct {
	Normal  lipgloss.Style
	Active  lipgloss.Style
	Disable lipgloss.Style
}

// NewPrimaryStyle creates a style for primary actions
func NewPrimaryStyle() Style {
	return Style{
		Normal:  lipgloss.NewStyle().Foreground(lipgloss.Color("205")),
		Active:  lipgloss.NewStyle().Foreground(lipgloss.Color("205")).Bold(true),
		Disable: lipgloss.NewStyle().Foreground(lipgloss.Color("240")),
	}
}

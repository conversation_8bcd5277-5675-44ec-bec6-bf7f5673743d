// Package styledkey provides functionality for creating and using styled key bindings.
package styledkey

import (
	"github.com/charmbracelet/bubbles/key"
	"github.com/charmbracelet/lipgloss"
)

// Binding combines a key binding with its style
type Binding struct {
	Binding key.Binding
	Style   Style
}

// StyledHelp returns the styled help text for this binding
func (sb Binding) StyledHelp() string {
	help := sb.Binding.Help()
	return sb.Style.Normal.Render(help.Desc)
}

// <PERSON><PERSON> applies an additional style to the styled help text
func (sb Binding) Render(r lipgloss.Style) string {
	return r.Render(sb.StyledHelp())
}

// DisabledHelp returns the help text styled for the disabled state
func (sb Binding) DisabledHelp() string {
	help := sb.Binding.Help()
	return sb.Style.Disable.Render(help.Desc)
}

// ActiveHelp returns the help text styled for the active state
func (sb Binding) ActiveHelp() string {
	help := sb.Binding.Help()
	return sb.Style.Active.Render(help.Desc)
}

// New creates a new styled key binding
func New(keys []string, helpKey, helpDesc string, style Style) Binding {
	return Binding{
		Binding: key.NewBinding(
			key.WithKeys(keys...),
			key.WithHelp(helpKey, helpDesc),
		),
		Style: style,
	}
}

// WithBinding creates a Binding from an existing key.Binding
func WithBinding(binding key.Binding, style Style) Binding {
	return Binding{
		Binding: binding,
		Style:   style,
	}
}

// WithKeys creates a Binding with the specified keys
func WithKeys(keys []string, style Style) Binding {
	return Binding{
		Binding: key.NewBinding(key.WithKeys(keys...)),
		Style:   style,
	}
}

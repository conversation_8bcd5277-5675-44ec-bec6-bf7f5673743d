package main

import (
	"strings"

	"github.com/charmbracelet/bubbles/viewport"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// Sidebar width constants
const (
	SidebarMinWidth     = 30
	SidebarMaxWidth     = 50
	SidebarDefaultWidth = SidebarMinWidth
	SidebarPadding      = 2 // Account for border and padding in viewport calculations
)

// SidebarItem represents an item in the sidebar
type SidebarItem struct {
	Name     string
	Type     string // "file", "tool", "section", "tip"
	Selected bool
}

// Sidebar manages the sidebar component
type Sidebar struct {
	viewport      viewport.Model
	items         []SidebarItem
	selectedIndex int
	width         int
	height        int
	visible       bool
	focused       bool
	// State management
	stateID       string
}

// NewSidebar creates a new sidebar component
func NewSidebar() *Sidebar {
	config := GetSidebarConfig()

	sidebar := &Sidebar{
		items:         config.Items,
		selectedIndex: 1, // Start with first file selected
		width:         config.DefaultWidth,
		visible:       config.Visible,
		focused:       false, // Start unfocused
		stateID:       "main-sidebar",
	}

	// State initialization deferred to avoid deadlock during startup
	return sidebar
}

// SetSize updates the sidebar dimensions
func (s *Sidebar) SetSize(width, height int) {
	s.width = width
	s.height = height

	if s.visible && width > 0 && height > 0 {
		config := GetSidebarConfig()
		s.viewport = viewport.New(width-config.Padding, height-config.Padding)
		s.updateContent()
	}
}

// SetVisible sets the sidebar visibility
func (s *Sidebar) SetVisible(visible bool) {
	s.visible = visible
	
	// Update global state
	if GlobalStateManager != nil {
		GlobalStateManager.UpdateSidebarState(func(state *SidebarState) {
			state.Visible = visible
		})
	}
	
	if visible && s.width > 0 && s.height > 0 {
		config := GetSidebarConfig()
		s.viewport = viewport.New(s.width-config.Padding, s.height-config.Padding)
		s.updateContent()
	}
}

// IsVisible returns whether the sidebar is visible
func (s *Sidebar) IsVisible() bool {
	return s.visible
}

// SetFocused sets the sidebar focus state
func (s *Sidebar) SetFocused(focused bool) {
	s.focused = focused
	
	// Update global state
	if GlobalStateManager != nil {
		GlobalStateManager.UpdateSidebarState(func(state *SidebarState) {
			state.Focused = focused
		})
	}
	
	s.updateContent() // Update content to reflect focus state
}

// IsFocused returns whether the sidebar is focused
func (s *Sidebar) IsFocused() bool {
	return s.focused
}

// GetWidth returns the current sidebar width, 0 if not visible
func (s *Sidebar) GetWidth() int {
	if s.visible {
		return s.width
	}
	return 0
}

// SetWidth sets the sidebar width
func (s *Sidebar) SetWidth(width int) {
	if width < SidebarMinWidth {
		width = SidebarMinWidth
	} else if width > SidebarMaxWidth {
		width = SidebarMaxWidth
	}
	
	s.width = width
	s.viewport.Width = width - SidebarPadding
	
	// Update global state
	if GlobalStateManager != nil {
		GlobalStateManager.UpdateSidebarState(func(state *SidebarState) {
			state.Width = s.width
		})
	}
	
	s.updateContent()
}

// GetHeight returns the current sidebar height
func (s *Sidebar) GetHeight() int {
	return s.viewport.Height
}

// SetSelectedIndex sets the selected index if valid
func (s *Sidebar) SetSelectedIndex(index int) {
	if index >= 0 && index < len(s.items) && s.isSelectableItem(index) {
		s.selectedIndex = index
		
		// Update global state
		if GlobalStateManager != nil {
			GlobalStateManager.UpdateSidebarState(func(state *SidebarState) {
				state.SelectedIndex = s.selectedIndex
			})
		}
		
		s.updateContent()
	}
}

// ToggleWidth toggles between minimum and maximum sidebar width
func (s *Sidebar) ToggleWidth() {
	config := GetSidebarConfig()
	if s.width == config.MinWidth {
		s.width = config.MaxWidth
	} else {
		s.width = config.MinWidth
	}

	// Update viewport size if visible
	if s.visible && s.height > 0 {
		s.viewport = viewport.New(s.width-config.Padding, s.height-config.Padding)
		s.updateContent()
	}
}

// NavigateUp moves selection up in sidebar
func (s *Sidebar) NavigateUp() {
	if s.selectedIndex > 0 {
		// Find previous selectable item
		for i := s.selectedIndex - 1; i >= 0; i-- {
			if s.isSelectableItem(i) {
				s.selectedIndex = i
				
				// Update global state
				if GlobalStateManager != nil {
					GlobalStateManager.UpdateSidebarState(func(state *SidebarState) {
						state.SelectedIndex = s.selectedIndex
					})
				}
				
				s.updateContent()
				break
			}
		}
	}
}

// NavigateDown moves selection down in sidebar
func (s *Sidebar) NavigateDown() {
	if s.selectedIndex < len(s.items)-1 {
		// Find next selectable item
		for i := s.selectedIndex + 1; i < len(s.items); i++ {
			if s.isSelectableItem(i) {
				s.selectedIndex = i
				
				// Update global state
				if GlobalStateManager != nil {
					GlobalStateManager.UpdateSidebarState(func(state *SidebarState) {
						state.SelectedIndex = s.selectedIndex
					})
				}
				
				s.updateContent()
				break
			}
		}
	}
}

// SelectItem returns the currently selected item
func (s *Sidebar) SelectItem() *SidebarItem {
	if s.selectedIndex >= 0 && s.selectedIndex < len(s.items) {
		return &s.items[s.selectedIndex]
	}
	return nil
}

// GetSelectedItem returns the currently selected item (convenience method)
func (s *Sidebar) GetSelectedItem() *SidebarItem {
	return s.SelectItem()
}

// Update handles messages for the sidebar
func (s *Sidebar) Update(msg tea.Msg) tea.Cmd {
	if !s.visible || !s.focused {
		return nil
	}

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "up", "k":
			s.NavigateUp()
		case "down", "j":
			s.NavigateDown()
		case "enter":
			// Selection is handled by the caller
			return nil
		default:
			// Update viewport for scrolling
			var cmd tea.Cmd
			s.viewport, cmd = s.viewport.Update(msg)
			return cmd
		}
	default:
		// Update viewport for other messages
		var cmd tea.Cmd
		s.viewport, cmd = s.viewport.Update(msg)
		return cmd
	}

	return nil
}

// View renders the sidebar
func (s *Sidebar) View() string {
	if !s.visible || s.width <= 0 || s.height <= 0 {
		return ""
	}

	style := sidebarStyle(s.focused).Width(s.width).Height(s.height)
	return style.Render(s.viewport.View())
}

// isSelectableItem checks if an item can be selected
func (s *Sidebar) isSelectableItem(index int) bool {
	if index < 0 || index >= len(s.items) {
		return false
	}
	item := s.items[index]
	return item.Type == "file" || item.Type == "tool"
}

// updateContent updates the sidebar viewport content
func (s *Sidebar) updateContent() {
	var lines []string

	for i, item := range s.items {
		line := item.Name

		// Skip empty separators
		if item.Type == "separator" {
			lines = append(lines, "")
			continue
		}

		// Style based on item type and selection
		switch item.Type {
		case "section":
			line = lipgloss.NewStyle().Bold(true).Foreground(GetColor("primary")).Render(line)
		case "file":
			prefix := "📄 "
			if i == s.selectedIndex {
				line = lipgloss.NewStyle().Background(GetColor("selection")).Foreground(GetColor("text")).Render(prefix + line)
			} else {
				line = lipgloss.NewStyle().Foreground(GetColor("secondary")).Render(prefix + line)
			}
		case "tool":
			prefix := "🔧 "
			if i == s.selectedIndex {
				line = lipgloss.NewStyle().Background(GetColor("selection")).Foreground(GetColor("text")).Render(prefix + line)
			} else {
				line = lipgloss.NewStyle().Foreground(GetColor("accent")).Render(prefix + line)
			}
		case "tip":
			line = lipgloss.NewStyle().Foreground(GetColor("text_secondary")).Render(line)
		}

		lines = append(lines, line)
	}

	content := strings.Join(lines, "\n")
	s.viewport.SetContent(content)
}

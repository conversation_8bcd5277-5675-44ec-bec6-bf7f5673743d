{"permissions": {"allow": ["<PERSON><PERSON>(go:*)", "Bash(TERMILLM_TITLE=\"Environment Test\" TERMILLM_DEBUG=1 TERMILLM_PRIMARY_COLOR=\"#ff6b6b\" go run . --help)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(grep:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(make test-compile:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(cp:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(./termillm --help)", "<PERSON><PERSON>(make:*)", "Bash(timeout 60s go run quick_test.go)", "Bash(timeout 60s go run integration_check.go)", "Bash(timeout 30s go run integration_check.go)", "<PERSON><PERSON>(timeout:*)", "Bash(rg:*)", "Bash(git rm:*)"], "deny": []}}
# TermiLLM Plugin Examples

This directory contains example plugins demonstrating the capabilities of the TermiLLM Plugin System.

## Available Example Plugins

### 1. Demo Plugin (`demo.go`)
**Purpose**: Simple demonstration plugin showing basic command handling and plugin lifecycle.

**Capabilities**:
- `demo.hello` - Greeting functionality
- `demo.echo` - Message echoing
- `demo.random` - Random number generation
- `demo.time` - Time display in various formats

**Example Commands**:
```bash
# In TermiLLM terminal (using : command mode)
:plugin.load examples/plugins/demo.go
:demo hello Alice
:demo echo Hello World
:demo random 50
:demo time iso
:demo info
```

**Configuration Options**:
- `greeting` (string): Default greeting message
- `show_time` (boolean): Include time in responses
- `random_max` (integer): Maximum value for random numbers
- `command_prefix` (string): Command prefix

### 2. File Operations Plugin (`file_ops.go`)
**Purpose**: Secure file and directory operations within the terminal environment.

**Capabilities**:
- `file.read` - Read file contents with security checks
- `file.write` - Write files with permission validation
- `file.list` - List directory contents
- `file.search` - Search for files by pattern

**Security Features**:
- Base directory restriction (prevents path traversal)
- File extension filtering
- File size limits
- Permission checking

**Configuration Options**:
- `max_file_size` (integer): Maximum file size in bytes
- `allowed_extensions` (array): Permitted file extensions
- `base_directory` (string): Base directory for operations

**Example Usage**:
```bash
:plugin.load examples/plugins/file_ops.go
:file_ops list .
:file_ops read config/app.json
:file_ops search "*.go" src/
```

### 3. Text Processor Plugin (`text_processor.go`)
**Purpose**: Advanced text processing, analysis, and transformation capabilities.

**Capabilities**:
- `text.analyze` - Text analysis (word count, reading time, etc.)
- `text.transform` - Text transformations (case, format, etc.)
- `text.search` - Text search with regex support
- `text.format` - Text formatting (markdown, HTML, etc.)
- `text.validate` - Text validation (email, URL, JSON, etc.)
- `text.extract` - Information extraction (emails, URLs, etc.)

**Transformations Available**:
- Case transformations: `uppercase`, `lowercase`, `title`
- Format transformations: `camel_case`, `snake_case`, `kebab_case`
- Text manipulations: `reverse`, `trim`, `normalize`
- Content transformations: `remove_punctuation`

**Analysis Features**:
- Character, word, line, paragraph counts
- Sentence counting
- Average word length
- Character frequency analysis
- Reading time estimation

**Example Usage**:
```bash
:plugin.load examples/plugins/text_processor.go
:text_processor analyze "Hello World! This is a test."
:text_processor transform "Hello World" uppercase
:text_processor search "test.*pattern" "test content pattern" true
:text_processor format "code block" markdown_code
:text_processor validate "<EMAIL>" email
:text_processor extract "Contact <NAME_EMAIL>" emails
```

### 4. System Information Plugin (`system_info.go`)
**Purpose**: System monitoring and information gathering for development and debugging.

**Capabilities**:
- `system.info` - Basic system information
- `system.memory` - Memory usage statistics
- `system.cpu` - CPU and goroutine information
- `system.environment` - Environment variables (with security filtering)
- `system.process` - Current process information
- `system.uptime` - System and plugin uptime

**Information Provided**:
- Operating system and architecture
- Go runtime version and statistics
- Memory allocation and garbage collection stats
- CPU count and goroutine count
- Process ID and working directory
- Environment variables (filtered for security)

**Configuration Options**:
- `show_sensitive_info` (boolean): Include sensitive system information
- `refresh_interval` (integer): Refresh interval for dynamic information
- `include_environment` (boolean): Include environment variables
- `max_process_count` (integer): Maximum processes to show

**Example Usage**:
```bash
:plugin.load examples/plugins/system_info.go
:system_info info
:system_info memory
:system_info performance
:system_info detailed
```

## Building and Using Plugins

### Building Plugins as Go Plugins (`.so` files)

For production use, compile plugins as shared libraries:

```bash
# Build individual plugins
go build -buildmode=plugin -o demo.so examples/plugins/demo.go
go build -buildmode=plugin -o file_ops.so examples/plugins/file_ops.go
go build -buildmode=plugin -o text_processor.so examples/plugins/text_processor.go
go build -buildmode=plugin -o system_info.so examples/plugins/system_info.so

# Load in TermiLLM
:plugin.load demo.so
```

### Development Mode

For development, plugins can be loaded directly from source:

```bash
# Load source files (requires compilation)
:plugin.load examples/plugins/demo.go
```

## Plugin Development Guide

### Basic Plugin Structure

Every plugin must implement the `Plugin` interface:

```go
type Plugin interface {
    // Metadata
    ID() PluginID
    Metadata() PluginMetadata
    
    // Lifecycle
    Initialize(ctx PluginContext) error
    Start() error
    Stop() error
    Cleanup() error
    
    // Health and status
    IsHealthy() bool
    GetStatus() PluginStatus
    GetLastError() error
    
    // Configuration
    GetConfigSchema() ConfigSchema
    ValidateConfig(config map[string]interface{}) error
    UpdateConfig(config map[string]interface{}) error
    
    // Features
    GetCapabilities() []string
    HasCapability(capability string) bool
}
```

### Plugin Entry Point

Each plugin must export a global `Plugin` variable:

```go
var Plugin YourPluginType

func init() {
    Plugin = *NewYourPlugin()
}
```

### Configuration Schema

Define configuration schema for validation:

```go
func (p *YourPlugin) GetConfigSchema() plugin.ConfigSchema {
    return plugin.ConfigSchema{
        Properties: map[string]plugin.ConfigProperty{
            "your_option": {
                Type:        "string",
                Description: "Description of your option",
                Default:     "default_value",
                Required:    false,
            },
        },
    }
}
```

### Security Considerations

1. **Permission Checking**: Always check permissions before performing sensitive operations
2. **Input Validation**: Validate all inputs and configuration
3. **Path Security**: Prevent path traversal attacks
4. **Resource Limits**: Implement reasonable resource limits
5. **Error Handling**: Don't expose sensitive information in errors

### Best Practices

1. **Stateless Design**: Keep plugins stateless where possible
2. **Resource Cleanup**: Always implement proper cleanup
3. **Error Handling**: Provide meaningful error messages
4. **Logging**: Use the provided logger for debugging
5. **Configuration**: Make plugins configurable
6. **Testing**: Write tests for your plugin functionality

## Plugin System Architecture

The TermiLLM Plugin System provides:

- **Secure Execution**: Sandboxed plugin execution with permission checking
- **Hot Reloading**: Load, unload, and reload plugins without restarting
- **Configuration Management**: Dynamic configuration with validation
- **Resource Monitoring**: Track plugin resource usage
- **Event System**: Plugin communication through events
- **Health Monitoring**: Plugin health checks and status reporting

## Testing Plugins

Test your plugins using the TermiLLM terminal:

1. Start TermiLLM: `go run .`
2. Enter command mode: Press `:`
3. Load plugin: `:plugin.load examples/plugins/demo.go`
4. List plugins: `:plugin.list`
5. Execute commands: `:demo hello`
6. Check status: `:plugin.info demo`
7. Unload plugin: `:plugin.unload demo`

## Plugin Commands Reference

### Core Plugin Management
- `:plugin.list` - List all loaded plugins
- `:plugin.load <path>` - Load a plugin from file
- `:plugin.unload <name>` - Unload a plugin
- `:plugin.reload <name>` - Reload a plugin
- `:plugin.info <name>` - Show plugin information

### Configuration Management
- `:config.get <key>` - Get configuration value
- `:config.set <key> <value>` - Set configuration value
- `:config.reload` - Reload configuration

### System Commands
- `:system.status` - Show system status
- `:system.help` - Show help information

## Contributing

To contribute new example plugins:

1. Follow the plugin interface requirements
2. Include comprehensive documentation
3. Add security considerations
4. Provide example usage
5. Test thoroughly with the TermiLLM system

## Support

For plugin development support:
- Check the main TermiLLM documentation
- Review existing plugin examples
- Test with the development environment
- Report issues via GitHub
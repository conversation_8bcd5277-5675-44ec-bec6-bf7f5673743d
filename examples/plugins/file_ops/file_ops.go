// Package main provides example file ops plugin for the TermiLLM Platform
package main

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"time"

	"eionz.com/demo/pkg/plugin"
)

// FileOpsPlugin provides file operation capabilities
type FileOpsPlugin struct {
	id          plugin.PluginID
	metadata    plugin.PluginMetadata
	ctx         plugin.PluginContext
	status      plugin.PluginStatus
	lastError   error
	config      map[string]interface{}
	initialized bool
}

// NewFileOpsPlugin creates a new file operations plugin
func NewFileOpsPlugin() *FileOpsPlugin {
	id := plugin.NewPluginID("file_ops")
	
	metadata := plugin.PluginMetadata{
		ID:          id,
		Name:        "File Operations",
		Description: "Provides file and directory operation capabilities for terminal applications",
		Version:     plugin.NewPluginVersion(1, 0, 0),
		Author:      "TermiLLM Team",
		License:     "MIT",
		Homepage:    "https://github.com/termillm/plugins",
		Repository:  "https://github.com/termillm/plugins",
		Keywords:    []string{"file", "directory", "operations", "fs"},
		Dependencies: []plugin.PluginDependency{},
		Capabilities: []string{"file.read", "file.write", "file.list", "file.search"},
		MinPlatform:  plugin.NewPluginVersion(1, 0, 0),
		CreatedAt:    time.Now(),
	}

	return &FileOpsPlugin{
		id:       id,
		metadata: metadata,
		status: plugin.PluginStatus{
			State:   plugin.PluginStateUnloaded,
			Message: "Plugin created",
		},
		config: make(map[string]interface{}),
	}
}

// Plugin interface implementation

func (p *FileOpsPlugin) ID() plugin.PluginID {
	return p.id
}

func (p *FileOpsPlugin) Metadata() plugin.PluginMetadata {
	return p.metadata
}

func (p *FileOpsPlugin) Initialize(ctx plugin.PluginContext) error {
	if p.initialized {
		return fmt.Errorf("plugin already initialized")
	}

	p.ctx = ctx
	p.status.State = plugin.PluginStateInitializing
	p.status.Message = "Initializing file operations plugin"

	// Check for required permissions
	if !ctx.HasPermission("read_files") {
		return fmt.Errorf("missing required permission: read_files")
	}

	// Set default configuration
	if len(p.config) == 0 {
		p.config = map[string]interface{}{
			"max_file_size":     1024 * 1024, // 1MB default
			"allowed_extensions": []string{".txt", ".md", ".json", ".yaml", ".yml"},
			"base_directory":    ".",
		}
	}

	p.initialized = true
	p.status.State = plugin.PluginStateLoaded
	p.status.Message = "File operations plugin initialized successfully"

	ctx.GetLogger().Info("File operations plugin initialized", "plugin_id", p.id.String())
	return nil
}

func (p *FileOpsPlugin) Start() error {
	if !p.initialized {
		return fmt.Errorf("plugin not initialized")
	}

	p.status.State = plugin.PluginStateActive
	p.status.Message = "File operations plugin is active"
	
	p.ctx.GetLogger().Info("File operations plugin started", "plugin_id", p.id.String())
	return nil
}

func (p *FileOpsPlugin) Stop() error {
	p.status.State = plugin.PluginStateStopped
	p.status.Message = "File operations plugin stopped"
	
	if p.ctx != nil {
		p.ctx.GetLogger().Info("File operations plugin stopped", "plugin_id", p.id.String())
	}
	return nil
}

func (p *FileOpsPlugin) Cleanup() error {
	p.initialized = false
	p.ctx = nil
	p.status.State = plugin.PluginStateUnloaded
	p.status.Message = "Plugin cleaned up"
	return nil
}

func (p *FileOpsPlugin) IsHealthy() bool {
	return p.status.State == plugin.PluginStateActive && p.lastError == nil
}

func (p *FileOpsPlugin) GetStatus() plugin.PluginStatus {
	return p.status
}

func (p *FileOpsPlugin) GetLastError() error {
	return p.lastError
}

func (p *FileOpsPlugin) GetConfigSchema() plugin.ConfigSchema {
	return plugin.ConfigSchema{
		Properties: map[string]plugin.PropertySchema{
			"max_file_size": {
				Type:        "number",
				Description: "Maximum file size in bytes for read operations",
				Default:     1024 * 1024,
			},
			"allowed_extensions": {
				Type:        "array",
				Description: "List of allowed file extensions for operations",
				Default:     []string{".txt", ".md", ".json", ".yaml", ".yml"},
			},
			"base_directory": {
				Type:        "string",
				Description: "Base directory for file operations",
				Default:     ".",
			},
		},
	}
}

func (p *FileOpsPlugin) ValidateConfig(config map[string]interface{}) error {
	if maxSize, ok := config["max_file_size"]; ok {
		if size, ok := maxSize.(int); ok {
			if size <= 0 {
				return fmt.Errorf("max_file_size must be positive")
			}
		} else {
			return fmt.Errorf("max_file_size must be an integer")
		}
	}

	if baseDir, ok := config["base_directory"]; ok {
		if dir, ok := baseDir.(string); ok {
			if !filepath.IsAbs(dir) && dir != "." {
				// Validate relative path
				if strings.Contains(dir, "..") {
					return fmt.Errorf("base_directory cannot contain '..' for security reasons")
				}
			}
		} else {
			return fmt.Errorf("base_directory must be a string")
		}
	}

	return nil
}

func (p *FileOpsPlugin) UpdateConfig(config map[string]interface{}) error {
	if err := p.ValidateConfig(config); err != nil {
		return err
	}

	for key, value := range config {
		p.config[key] = value
	}

	if p.ctx != nil {
		p.ctx.GetLogger().Info("Configuration updated", "plugin_id", p.id.String())
	}
	return nil
}

func (p *FileOpsPlugin) GetCapabilities() []string {
	return p.metadata.Capabilities
}

func (p *FileOpsPlugin) HasCapability(capability string) bool {
	for _, cap := range p.metadata.Capabilities {
		if cap == capability {
			return true
		}
	}
	return false
}

// File operation methods (these would be called via plugin execution)

func (p *FileOpsPlugin) ListFiles(directory string) ([]string, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	baseDir := p.config["base_directory"].(string)
	fullPath := filepath.Join(baseDir, directory)

	entries, err := os.ReadDir(fullPath)
	if err != nil {
		p.lastError = err
		return nil, fmt.Errorf("failed to read directory: %w", err)
	}

	var files []string
	for _, entry := range entries {
		info, err := entry.Info()
		if err != nil {
			continue
		}

		fileInfo := fmt.Sprintf("%s (%s)", entry.Name(), formatSize(info.Size()))
		if entry.IsDir() {
			fileInfo = fmt.Sprintf("%s/", entry.Name())
		}
		files = append(files, fileInfo)
	}

	return files, nil
}

func (p *FileOpsPlugin) ReadFile(filePath string) (string, error) {
	if !p.IsHealthy() {
		return "", fmt.Errorf("plugin not healthy")
	}

	baseDir := p.config["base_directory"].(string)
	fullPath := filepath.Join(baseDir, filePath)

	// Security check: ensure we're not reading outside base directory
	if !strings.HasPrefix(fullPath, baseDir) {
		return "", fmt.Errorf("access denied: path outside base directory")
	}

	// Check file extension
	ext := filepath.Ext(fullPath)
	allowedExts := p.config["allowed_extensions"].([]string)
	allowed := false
	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			allowed = true
			break
		}
	}
	if !allowed {
		return "", fmt.Errorf("file extension %s not allowed", ext)
	}

	// Check file size
	info, err := os.Stat(fullPath)
	if err != nil {
		p.lastError = err
		return "", fmt.Errorf("failed to stat file: %w", err)
	}

	maxSize := int64(p.config["max_file_size"].(int))
	if info.Size() > maxSize {
		return "", fmt.Errorf("file too large: %d bytes (max: %d)", info.Size(), maxSize)
	}

	content, err := os.ReadFile(fullPath)
	if err != nil {
		p.lastError = err
		return "", fmt.Errorf("failed to read file: %w", err)
	}

	return string(content), nil
}

func (p *FileOpsPlugin) SearchFiles(pattern string, directory string) ([]string, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	baseDir := p.config["base_directory"].(string)
	searchDir := filepath.Join(baseDir, directory)

	var matches []string
	err := filepath.WalkDir(searchDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return nil // Continue walking, ignore errors
		}

		if d.IsDir() {
			return nil
		}

		if matched, _ := filepath.Match(pattern, d.Name()); matched {
			relPath, _ := filepath.Rel(baseDir, path)
			matches = append(matches, relPath)
		}

		return nil
	})

	if err != nil {
		p.lastError = err
		return nil, fmt.Errorf("search failed: %w", err)
	}

	return matches, nil
}

func (p *FileOpsPlugin) WriteFile(filePath, content string) error {
	if !p.IsHealthy() {
		return fmt.Errorf("plugin not healthy")
	}

	// Check write permission
	if !p.ctx.HasPermission("write_files") {
		return fmt.Errorf("missing required permission: write_files")
	}

	baseDir := p.config["base_directory"].(string)
	fullPath := filepath.Join(baseDir, filePath)

	// Security check
	if !strings.HasPrefix(fullPath, baseDir) {
		return fmt.Errorf("access denied: path outside base directory")
	}

	// Create directory if it doesn't exist
	dir := filepath.Dir(fullPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		p.lastError = err
		return fmt.Errorf("failed to create directory: %w", err)
	}

	err := os.WriteFile(fullPath, []byte(content), 0644)
	if err != nil {
		p.lastError = err
		return fmt.Errorf("failed to write file: %w", err)
	}

	p.ctx.GetLogger().Info("File written", "path", filePath, "size", len(content))
	return nil
}

// Utility functions

func formatSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// Plugin entry point for Go plugin system
var Plugin FileOpsPlugin

func init() {
	Plugin = *NewFileOpsPlugin()
}
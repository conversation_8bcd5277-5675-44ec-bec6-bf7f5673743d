// Package plugins provides example plugins for the TermiLLM Platform
package main

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"

	"eionz.com/demo/pkg/plugin"
)

// TextProcessorPlugin provides text processing capabilities
type TextProcessorPlugin struct {
	id          plugin.PluginID
	metadata    plugin.PluginMetadata
	ctx         plugin.PluginContext
	status      plugin.PluginStatus
	lastError   error
	config      map[string]interface{}
	initialized bool
	stats       map[string]int64
}

// NewTextProcessorPlugin creates a new text processing plugin
func NewTextProcessorPlugin() *TextProcessorPlugin {
	id := plugin.NewPluginID("text_processor")
	
	metadata := plugin.PluginMetadata{
		ID:          id,
		Name:        "Text Processor",
		Description: "Advanced text processing and analysis capabilities",
		Version:     plugin.NewPluginVersion(1, 0, 0),
		Author:      "TermiLLM Team",
		License:     "MIT",
		Homepage:    "https://github.com/termillm/plugins",
		Repository:  "https://github.com/termillm/plugins",
		Keywords:    []string{"text", "processing", "analysis", "string", "regex"},
		Dependencies: []plugin.PluginDependency{},
		Capabilities: []string{
			"text.analyze", "text.transform", "text.search", 
			"text.format", "text.validate", "text.extract",
		},
		MinPlatform: plugin.NewPluginVersion(1, 0, 0),
		CreatedAt:   time.Now(),
	}

	return &TextProcessorPlugin{
		id:       id,
		metadata: metadata,
		status: plugin.PluginStatus{
			State:   plugin.PluginStateUnloaded,
			Message: "Plugin created",
		},
		config: make(map[string]interface{}),
		stats:  make(map[string]int64),
	}
}

// Plugin interface implementation

func (p *TextProcessorPlugin) ID() plugin.PluginID {
	return p.id
}

func (p *TextProcessorPlugin) Metadata() plugin.PluginMetadata {
	return p.metadata
}

func (p *TextProcessorPlugin) Initialize(ctx plugin.PluginContext) error {
	if p.initialized {
		return fmt.Errorf("plugin already initialized")
	}

	p.ctx = ctx
	p.status.State = plugin.PluginStateInitializing
	p.status.Message = "Initializing text processor plugin"

	// Set default configuration
	if len(p.config) == 0 {
		p.config = map[string]interface{}{
			"max_text_length":     100 * 1024, // 100KB default
			"case_sensitive":      false,
			"preserve_whitespace": true,
			"regex_timeout":       5, // seconds
		}
	}

	// Initialize stats
	p.stats = map[string]int64{
		"operations_count":    0,
		"characters_processed": 0,
		"regex_matches":       0,
		"transformations":     0,
	}

	p.initialized = true
	p.status.State = plugin.PluginStateLoaded
	p.status.Message = "Text processor plugin initialized successfully"

	ctx.GetLogger().Info("Text processor plugin initialized", "plugin_id", p.id.String())
	return nil
}

func (p *TextProcessorPlugin) Start() error {
	if !p.initialized {
		return fmt.Errorf("plugin not initialized")
	}

	p.status.State = plugin.PluginStateActive
	p.status.Message = "Text processor plugin is active"
	
	p.ctx.GetLogger().Info("Text processor plugin started", "plugin_id", p.id.String())
	return nil
}

func (p *TextProcessorPlugin) Stop() error {
	p.status.State = plugin.PluginStateStopped
	p.status.Message = "Text processor plugin stopped"
	
	if p.ctx != nil {
		p.ctx.GetLogger().Info("Text processor plugin stopped", "plugin_id", p.id.String())
	}
	return nil
}

func (p *TextProcessorPlugin) Cleanup() error {
	p.initialized = false
	p.ctx = nil
	p.status.State = plugin.PluginStateUnloaded
	p.status.Message = "Plugin cleaned up"
	return nil
}

func (p *TextProcessorPlugin) IsHealthy() bool {
	return p.status.State == plugin.PluginStateActive && p.lastError == nil
}

func (p *TextProcessorPlugin) GetStatus() plugin.PluginStatus {
	return p.status
}

func (p *TextProcessorPlugin) GetLastError() error {
	return p.lastError
}

func (p *TextProcessorPlugin) GetConfigSchema() plugin.ConfigSchema {
	return plugin.ConfigSchema{
		Properties: map[string]plugin.PropertySchema{
			"max_text_length": {
				Type:        "number",
				Description: "Maximum text length for processing operations",
				Default:     100 * 1024,
			},
			"case_sensitive": {
				Type:        "boolean",
				Description: "Whether text operations are case sensitive by default",
				Default:     false,
			},
			"preserve_whitespace": {
				Type:        "boolean",
				Description: "Whether to preserve whitespace in transformations",
				Default:     true,
			},
			"regex_timeout": {
				Type:        "number",
				Description: "Timeout for regex operations in seconds",
				Default:     5,
			},
		},
	}
}

func (p *TextProcessorPlugin) ValidateConfig(config map[string]interface{}) error {
	if maxLen, ok := config["max_text_length"]; ok {
		if length, ok := maxLen.(int); ok {
			if length <= 0 {
				return fmt.Errorf("max_text_length must be positive")
			}
		} else {
			return fmt.Errorf("max_text_length must be an integer")
		}
	}

	if timeout, ok := config["regex_timeout"]; ok {
		if t, ok := timeout.(int); ok {
			if t <= 0 {
				return fmt.Errorf("regex_timeout must be positive")
			}
		} else {
			return fmt.Errorf("regex_timeout must be an integer")
		}
	}

	return nil
}

func (p *TextProcessorPlugin) UpdateConfig(config map[string]interface{}) error {
	if err := p.ValidateConfig(config); err != nil {
		return err
	}

	for key, value := range config {
		p.config[key] = value
	}

	if p.ctx != nil {
		p.ctx.GetLogger().Info("Configuration updated", "plugin_id", p.id.String())
	}
	return nil
}

func (p *TextProcessorPlugin) GetCapabilities() []string {
	return p.metadata.Capabilities
}

func (p *TextProcessorPlugin) HasCapability(capability string) bool {
	for _, cap := range p.metadata.Capabilities {
		if cap == capability {
			return true
		}
	}
	return false
}

// Text processing methods

func (p *TextProcessorPlugin) AnalyzeText(text string) (map[string]interface{}, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	if err := p.validateTextLength(text); err != nil {
		return nil, err
	}

	p.incrementStat("operations_count")
	p.incrementStat("characters_processed", int64(len(text)))

	analysis := map[string]interface{}{
		"character_count": len(text),
		"word_count":      len(strings.Fields(text)),
		"line_count":      strings.Count(text, "\n") + 1,
		"paragraph_count": len(strings.Split(strings.TrimSpace(text), "\n\n")),
		"sentence_count":  p.countSentences(text),
		"average_word_length": p.averageWordLength(text),
		"character_frequency": p.characterFrequency(text),
		"reading_time_minutes": p.estimateReadingTime(text),
	}

	return analysis, nil
}

func (p *TextProcessorPlugin) TransformText(text string, transformation string) (string, error) {
	if !p.IsHealthy() {
		return "", fmt.Errorf("plugin not healthy")
	}

	if err := p.validateTextLength(text); err != nil {
		return "", err
	}

	p.incrementStat("transformations")

	switch transformation {
	case "uppercase":
		return strings.ToUpper(text), nil
	case "lowercase":
		return strings.ToLower(text), nil
	case "title":
		return strings.Title(text), nil
	case "reverse":
		return p.reverseString(text), nil
	case "trim":
		return strings.TrimSpace(text), nil
	case "normalize":
		return p.normalizeWhitespace(text), nil
	case "remove_punctuation":
		return p.removePunctuation(text), nil
	case "camel_case":
		return p.toCamelCase(text), nil
	case "snake_case":
		return p.toSnakeCase(text), nil
	case "kebab_case":
		return p.toKebabCase(text), nil
	default:
		return "", fmt.Errorf("unknown transformation: %s", transformation)
	}
}

func (p *TextProcessorPlugin) SearchText(text, pattern string, useRegex bool) ([]map[string]interface{}, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	if err := p.validateTextLength(text); err != nil {
		return nil, err
	}

	var matches []map[string]interface{}

	if useRegex {
		regex, err := regexp.Compile(pattern)
		if err != nil {
			p.lastError = err
			return nil, fmt.Errorf("invalid regex pattern: %w", err)
		}

		regexMatches := regex.FindAllStringIndex(text, -1)
		for i, match := range regexMatches {
			matchInfo := map[string]interface{}{
				"match_number": i + 1,
				"start":        match[0],
				"end":          match[1],
				"text":         text[match[0]:match[1]],
				"line":         p.getLineNumber(text, match[0]),
			}
			matches = append(matches, matchInfo)
		}
		p.incrementStat("regex_matches", int64(len(matches)))
	} else {
		// Simple string search
		caseSensitive := p.config["case_sensitive"].(bool)
		searchText := text
		searchPattern := pattern
		
		if !caseSensitive {
			searchText = strings.ToLower(text)
			searchPattern = strings.ToLower(pattern)
		}

		start := 0
		matchNum := 1
		for {
			index := strings.Index(searchText[start:], searchPattern)
			if index == -1 {
				break
			}
			
			actualIndex := start + index
			matchInfo := map[string]interface{}{
				"match_number": matchNum,
				"start":        actualIndex,
				"end":          actualIndex + len(pattern),
				"text":         text[actualIndex : actualIndex+len(pattern)],
				"line":         p.getLineNumber(text, actualIndex),
			}
			matches = append(matches, matchInfo)
			
			start = actualIndex + 1
			matchNum++
		}
	}

	return matches, nil
}

func (p *TextProcessorPlugin) FormatText(text string, format string) (string, error) {
	if !p.IsHealthy() {
		return "", fmt.Errorf("plugin not healthy")
	}

	if err := p.validateTextLength(text); err != nil {
		return "", err
	}

	switch format {
	case "markdown_code":
		return fmt.Sprintf("```\n%s\n```", text), nil
	case "html_pre":
		return fmt.Sprintf("<pre>%s</pre>", text), nil
	case "json_string":
		return strconv.Quote(text), nil
	case "line_numbers":
		return p.addLineNumbers(text), nil
	case "indent":
		return p.indentText(text, "  "), nil
	case "bullet_list":
		return p.toBulletList(text), nil
	case "numbered_list":
		return p.toNumberedList(text), nil
	default:
		return "", fmt.Errorf("unknown format: %s", format)
	}
}

func (p *TextProcessorPlugin) ValidateText(text string, validationType string) (bool, []string, error) {
	if !p.IsHealthy() {
		return false, nil, fmt.Errorf("plugin not healthy")
	}

	var errors []string
	valid := true

	switch validationType {
	case "email":
		emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
		if !emailRegex.MatchString(text) {
			valid = false
			errors = append(errors, "Invalid email format")
		}
	case "url":
		urlRegex := regexp.MustCompile(`^https?://[^\s/$.?#].[^\s]*$`)
		if !urlRegex.MatchString(text) {
			valid = false
			errors = append(errors, "Invalid URL format")
		}
	case "json":
		// Simple JSON validation
		if !p.isValidJSON(text) {
			valid = false
			errors = append(errors, "Invalid JSON format")
		}
	case "no_profanity":
		if p.containsProfanity(text) {
			valid = false
			errors = append(errors, "Contains inappropriate language")
		}
	case "length":
		maxLen := p.config["max_text_length"].(int)
		if len(text) > maxLen {
			valid = false
			errors = append(errors, fmt.Sprintf("Text too long: %d > %d", len(text), maxLen))
		}
	default:
		return false, nil, fmt.Errorf("unknown validation type: %s", validationType)
	}

	return valid, errors, nil
}

func (p *TextProcessorPlugin) ExtractInfo(text string, extractType string) ([]string, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	if err := p.validateTextLength(text); err != nil {
		return nil, err
	}

	switch extractType {
	case "emails":
		return p.extractEmails(text), nil
	case "urls":
		return p.extractURLs(text), nil
	case "phone_numbers":
		return p.extractPhoneNumbers(text), nil
	case "numbers":
		return p.extractNumbers(text), nil
	case "words":
		return strings.Fields(text), nil
	case "sentences":
		return p.extractSentences(text), nil
	case "hashtags":
		return p.extractHashtags(text), nil
	case "mentions":
		return p.extractMentions(text), nil
	default:
		return nil, fmt.Errorf("unknown extraction type: %s", extractType)
	}
}

// Utility methods

func (p *TextProcessorPlugin) validateTextLength(text string) error {
	maxLen := p.config["max_text_length"].(int)
	if len(text) > maxLen {
		return fmt.Errorf("text too long: %d characters (max: %d)", len(text), maxLen)
	}
	return nil
}

func (p *TextProcessorPlugin) incrementStat(key string, delta ...int64) {
	if len(delta) > 0 {
		p.stats[key] += delta[0]
	} else {
		p.stats[key]++
	}
}

func (p *TextProcessorPlugin) countSentences(text string) int {
	sentenceEnders := regexp.MustCompile(`[.!?]+`)
	return len(sentenceEnders.FindAllString(text, -1))
}

func (p *TextProcessorPlugin) averageWordLength(text string) float64 {
	words := strings.Fields(text)
	if len(words) == 0 {
		return 0
	}
	
	totalLength := 0
	for _, word := range words {
		totalLength += len(word)
	}
	
	return float64(totalLength) / float64(len(words))
}

func (p *TextProcessorPlugin) characterFrequency(text string) map[string]int {
	freq := make(map[string]int)
	for _, char := range text {
		if unicode.IsLetter(char) {
			freq[strings.ToLower(string(char))]++
		}
	}
	return freq
}

func (p *TextProcessorPlugin) estimateReadingTime(text string) float64 {
	wordCount := len(strings.Fields(text))
	// Average reading speed: 200 words per minute
	return float64(wordCount) / 200.0
}

func (p *TextProcessorPlugin) reverseString(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

func (p *TextProcessorPlugin) normalizeWhitespace(text string) string {
	// Replace multiple whitespace with single space
	re := regexp.MustCompile(`\s+`)
	return strings.TrimSpace(re.ReplaceAllString(text, " "))
}

func (p *TextProcessorPlugin) removePunctuation(text string) string {
	re := regexp.MustCompile(`[^\p{L}\p{N}\s]+`)
	return re.ReplaceAllString(text, "")
}

func (p *TextProcessorPlugin) toCamelCase(text string) string {
	words := strings.Fields(text)
	if len(words) == 0 {
		return ""
	}
	
	result := strings.ToLower(words[0])
	for i := 1; i < len(words); i++ {
		result += strings.Title(strings.ToLower(words[i]))
	}
	return result
}

func (p *TextProcessorPlugin) toSnakeCase(text string) string {
	words := strings.Fields(text)
	for i := range words {
		words[i] = strings.ToLower(words[i])
	}
	return strings.Join(words, "_")
}

func (p *TextProcessorPlugin) toKebabCase(text string) string {
	words := strings.Fields(text)
	for i := range words {
		words[i] = strings.ToLower(words[i])
	}
	return strings.Join(words, "-")
}

func (p *TextProcessorPlugin) getLineNumber(text string, index int) int {
	return strings.Count(text[:index], "\n") + 1
}

func (p *TextProcessorPlugin) addLineNumbers(text string) string {
	lines := strings.Split(text, "\n")
	for i, line := range lines {
		lines[i] = fmt.Sprintf("%3d: %s", i+1, line)
	}
	return strings.Join(lines, "\n")
}

func (p *TextProcessorPlugin) indentText(text string, indent string) string {
	lines := strings.Split(text, "\n")
	for i, line := range lines {
		if strings.TrimSpace(line) != "" {
			lines[i] = indent + line
		}
	}
	return strings.Join(lines, "\n")
}

func (p *TextProcessorPlugin) toBulletList(text string) string {
	lines := strings.Split(text, "\n")
	for i, line := range lines {
		if strings.TrimSpace(line) != "" {
			lines[i] = "• " + strings.TrimSpace(line)
		}
	}
	return strings.Join(lines, "\n")
}

func (p *TextProcessorPlugin) toNumberedList(text string) string {
	lines := strings.Split(text, "\n")
	num := 1
	for i, line := range lines {
		if strings.TrimSpace(line) != "" {
			lines[i] = fmt.Sprintf("%d. %s", num, strings.TrimSpace(line))
			num++
		}
	}
	return strings.Join(lines, "\n")
}

func (p *TextProcessorPlugin) isValidJSON(text string) bool {
	// Simple JSON validation
	text = strings.TrimSpace(text)
	return (strings.HasPrefix(text, "{") && strings.HasSuffix(text, "}")) ||
		   (strings.HasPrefix(text, "[") && strings.HasSuffix(text, "]"))
}

func (p *TextProcessorPlugin) containsProfanity(text string) bool {
	// Basic profanity filter - in real implementation, use proper word list
	profanity := []string{"badword1", "badword2"} // Placeholder
	lowerText := strings.ToLower(text)
	for _, word := range profanity {
		if strings.Contains(lowerText, word) {
			return true
		}
	}
	return false
}

func (p *TextProcessorPlugin) extractEmails(text string) []string {
	emailRegex := regexp.MustCompile(`[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}`)
	return emailRegex.FindAllString(text, -1)
}

func (p *TextProcessorPlugin) extractURLs(text string) []string {
	urlRegex := regexp.MustCompile(`https?://[^\s]+`)
	return urlRegex.FindAllString(text, -1)
}

func (p *TextProcessorPlugin) extractPhoneNumbers(text string) []string {
	phoneRegex := regexp.MustCompile(`\b\d{3}[-.]?\d{3}[-.]?\d{4}\b`)
	return phoneRegex.FindAllString(text, -1)
}

func (p *TextProcessorPlugin) extractNumbers(text string) []string {
	numberRegex := regexp.MustCompile(`\b\d+\.?\d*\b`)
	return numberRegex.FindAllString(text, -1)
}

func (p *TextProcessorPlugin) extractSentences(text string) []string {
	sentenceRegex := regexp.MustCompile(`[^.!?]*[.!?]`)
	sentences := sentenceRegex.FindAllString(text, -1)
	for i := range sentences {
		sentences[i] = strings.TrimSpace(sentences[i])
	}
	return sentences
}

func (p *TextProcessorPlugin) extractHashtags(text string) []string {
	hashtagRegex := regexp.MustCompile(`#\w+`)
	return hashtagRegex.FindAllString(text, -1)
}

func (p *TextProcessorPlugin) extractMentions(text string) []string {
	mentionRegex := regexp.MustCompile(`@\w+`)
	return mentionRegex.FindAllString(text, -1)
}

func (p *TextProcessorPlugin) GetStats() map[string]int64 {
	return p.stats
}

// Plugin entry point for Go plugin system
var Plugin TextProcessorPlugin

func init() {
	Plugin = *NewTextProcessorPlugin()
}
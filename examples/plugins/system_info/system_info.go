// Package plugins provides example plugins for the TermiLLM Platform
package main

import (
	"fmt"
	"os"
	"runtime"
	"strings"
	"time"

	"eionz.com/demo/pkg/plugin"
)

// SystemInfoPlugin provides system information and monitoring capabilities
type SystemInfoPlugin struct {
	id          plugin.PluginID
	metadata    plugin.PluginMetadata
	ctx         plugin.PluginContext
	status      plugin.PluginStatus
	lastError   error
	config      map[string]interface{}
	initialized bool
	startTime   time.Time
}

// NewSystemInfoPlugin creates a new system information plugin
func NewSystemInfoPlugin() *SystemInfoPlugin {
	id := plugin.NewPluginID("system_info")
	
	metadata := plugin.PluginMetadata{
		ID:          id,
		Name:        "System Information",
		Description: "Provides system information and monitoring capabilities",
		Version:     plugin.NewPluginVersion(1, 0, 0),
		Author:      "TermiLLM Team",
		License:     "MIT",
		Homepage:    "https://github.com/termillm/plugins",
		Repository:  "https://github.com/termillm/plugins",
		Keywords:    []string{"system", "info", "monitoring", "performance", "os"},
		Dependencies: []plugin.PluginDependency{},
		Capabilities: []string{
			"system.info", "system.memory", "system.cpu", 
			"system.environment", "system.process", "system.uptime",
		},
		MinPlatform: plugin.NewPluginVersion(1, 0, 0),
		CreatedAt:   time.Now(),
	}

	return &SystemInfoPlugin{
		id:       id,
		metadata: metadata,
		status: plugin.PluginStatus{
			State:   plugin.PluginStateUnloaded,
			Message: "Plugin created",
		},
		config: make(map[string]interface{}),
	}
}

// Plugin interface implementation

func (p *SystemInfoPlugin) ID() plugin.PluginID {
	return p.id
}

func (p *SystemInfoPlugin) Metadata() plugin.PluginMetadata {
	return p.metadata
}

func (p *SystemInfoPlugin) Initialize(ctx plugin.PluginContext) error {
	if p.initialized {
		return fmt.Errorf("plugin already initialized")
	}

	p.ctx = ctx
	p.status.State = plugin.PluginStateInitializing
	p.status.Message = "Initializing system info plugin"
	p.startTime = time.Now()

	// Set default configuration
	if len(p.config) == 0 {
		p.config = map[string]interface{}{
			"show_sensitive_info": false,
			"refresh_interval":    30, // seconds
			"include_environment": false,
			"max_process_count":   10,
		}
	}

	p.initialized = true
	p.status.State = plugin.PluginStateLoaded
	p.status.Message = "System info plugin initialized successfully"

	ctx.GetLogger().Info("System info plugin initialized", "plugin_id", p.id.String())
	return nil
}

func (p *SystemInfoPlugin) Start() error {
	if !p.initialized {
		return fmt.Errorf("plugin not initialized")
	}

	p.status.State = plugin.PluginStateActive
	p.status.Message = "System info plugin is active"
	
	p.ctx.GetLogger().Info("System info plugin started", "plugin_id", p.id.String())
	return nil
}

func (p *SystemInfoPlugin) Stop() error {
	p.status.State = plugin.PluginStateStopped
	p.status.Message = "System info plugin stopped"
	
	if p.ctx != nil {
		p.ctx.GetLogger().Info("System info plugin stopped", "plugin_id", p.id.String())
	}
	return nil
}

func (p *SystemInfoPlugin) Cleanup() error {
	p.initialized = false
	p.ctx = nil
	p.status.State = plugin.PluginStateUnloaded
	p.status.Message = "Plugin cleaned up"
	return nil
}

func (p *SystemInfoPlugin) IsHealthy() bool {
	return p.status.State == plugin.PluginStateActive && p.lastError == nil
}

func (p *SystemInfoPlugin) GetStatus() plugin.PluginStatus {
	return p.status
}

func (p *SystemInfoPlugin) GetLastError() error {
	return p.lastError
}

func (p *SystemInfoPlugin) GetConfigSchema() plugin.ConfigSchema {
	return plugin.ConfigSchema{
		Properties: map[string]plugin.PropertySchema{
			"show_sensitive_info": {
				Type:        "boolean",
				Description: "Whether to include sensitive system information",
				Default:     false,
			},
			"refresh_interval": {
				Type:        "number",
				Description: "Refresh interval for dynamic information in seconds",
				Default:     30,
			},
			"include_environment": {
				Type:        "boolean",
				Description: "Whether to include environment variables",
				Default:     false,
			},
			"max_process_count": {
				Type:        "number",
				Description: "Maximum number of processes to show in process list",
				Default:     10,
			},
		},
	}
}

func (p *SystemInfoPlugin) ValidateConfig(config map[string]interface{}) error {
	if interval, ok := config["refresh_interval"]; ok {
		if i, ok := interval.(int); ok {
			if i <= 0 {
				return fmt.Errorf("refresh_interval must be positive")
			}
		} else {
			return fmt.Errorf("refresh_interval must be an integer")
		}
	}

	if maxProc, ok := config["max_process_count"]; ok {
		if count, ok := maxProc.(int); ok {
			if count <= 0 || count > 100 {
				return fmt.Errorf("max_process_count must be between 1 and 100")
			}
		} else {
			return fmt.Errorf("max_process_count must be an integer")
		}
	}

	return nil
}

func (p *SystemInfoPlugin) UpdateConfig(config map[string]interface{}) error {
	if err := p.ValidateConfig(config); err != nil {
		return err
	}

	for key, value := range config {
		p.config[key] = value
	}

	if p.ctx != nil {
		p.ctx.GetLogger().Info("Configuration updated", "plugin_id", p.id.String())
	}
	return nil
}

func (p *SystemInfoPlugin) GetCapabilities() []string {
	return p.metadata.Capabilities
}

func (p *SystemInfoPlugin) HasCapability(capability string) bool {
	for _, cap := range p.metadata.Capabilities {
		if cap == capability {
			return true
		}
	}
	return false
}

// System information methods

func (p *SystemInfoPlugin) GetSystemInfo() (map[string]interface{}, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	info := map[string]interface{}{
		"os":           runtime.GOOS,
		"architecture": runtime.GOARCH,
		"go_version":   runtime.Version(),
		"hostname":     p.getHostname(),
		"uptime":       p.getUptime(),
		"timestamp":    time.Now().Format(time.RFC3339),
	}

	// Add working directory
	if wd, err := os.Getwd(); err == nil {
		info["working_directory"] = wd
	}

	// Add user information if not sensitive
	if !p.config["show_sensitive_info"].(bool) {
		if user := os.Getenv("USER"); user != "" {
			info["user"] = user
		}
	}

	return info, nil
}

func (p *SystemInfoPlugin) GetMemoryInfo() (map[string]interface{}, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	info := map[string]interface{}{
		"allocated_bytes":     m.Alloc,
		"total_allocated":     m.TotalAlloc,
		"system_memory":       m.Sys,
		"num_gc":              m.NumGC,
		"gc_cpu_fraction":     m.GCCPUFraction,
		"heap_allocated":      m.HeapAlloc,
		"heap_sys":            m.HeapSys,
		"heap_idle":           m.HeapIdle,
		"heap_inuse":          m.HeapInuse,
		"heap_released":       m.HeapReleased,
		"heap_objects":        m.HeapObjects,
		"stack_inuse":         m.StackInuse,
		"stack_sys":           m.StackSys,
		"formatted": map[string]string{
			"allocated":     formatBytes(m.Alloc),
			"total_alloc":   formatBytes(m.TotalAlloc),
			"system":        formatBytes(m.Sys),
			"heap_alloc":    formatBytes(m.HeapAlloc),
			"heap_sys":      formatBytes(m.HeapSys),
		},
	}

	return info, nil
}

func (p *SystemInfoPlugin) GetCPUInfo() (map[string]interface{}, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	info := map[string]interface{}{
		"num_cpu":        runtime.NumCPU(),
		"num_goroutines": runtime.NumGoroutine(),
		"max_procs":      runtime.GOMAXPROCS(0),
		"compiler":       runtime.Compiler,
	}

	return info, nil
}

func (p *SystemInfoPlugin) GetEnvironmentInfo() (map[string]interface{}, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	includeEnv := p.config["include_environment"].(bool)
	showSensitive := p.config["show_sensitive_info"].(bool)

	info := map[string]interface{}{
		"include_environment_vars": includeEnv,
		"show_sensitive_info":      showSensitive,
	}

	if includeEnv {
		env := make(map[string]string)
		sensitiveKeys := []string{"PASSWORD", "SECRET", "KEY", "TOKEN", "CREDENTIAL"}
		
		for _, envVar := range os.Environ() {
			parts := strings.SplitN(envVar, "=", 2)
			if len(parts) == 2 {
				key, value := parts[0], parts[1]
				
				// Filter sensitive information unless explicitly allowed
				if !showSensitive {
					isSensitive := false
					for _, sensitiveKey := range sensitiveKeys {
						if strings.Contains(strings.ToUpper(key), sensitiveKey) {
							isSensitive = true
							break
						}
					}
					
					if isSensitive {
						value = "[HIDDEN]"
					}
				}
				
				env[key] = value
			}
		}
		
		info["environment_variables"] = env
		info["environment_count"] = len(env)
	}

	// Add some basic environment info
	info["path"] = os.Getenv("PATH")
	info["home"] = os.Getenv("HOME")
	info["shell"] = os.Getenv("SHELL")
	info["term"] = os.Getenv("TERM")

	return info, nil
}

func (p *SystemInfoPlugin) GetProcessInfo() (map[string]interface{}, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	info := map[string]interface{}{
		"pid":         os.Getpid(),
		"ppid":        os.Getppid(),
		"executable":  p.getExecutable(),
		"args":        os.Args,
		"working_dir": p.getWorkingDir(),
	}

	return info, nil
}

func (p *SystemInfoPlugin) GetPerformanceMetrics() (map[string]interface{}, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	metrics := map[string]interface{}{
		"plugin_uptime_seconds": time.Since(p.startTime).Seconds(),
		"plugin_uptime_formatted": p.formatDuration(time.Since(p.startTime)),
		"memory_usage_mb": float64(m.Alloc) / 1024 / 1024,
		"goroutines": runtime.NumGoroutine(),
		"gc_cycles": m.NumGC,
		"gc_pause_total_ns": m.PauseTotalNs,
		"next_gc_mb": float64(m.NextGC) / 1024 / 1024,
		"last_gc": time.Unix(0, int64(m.LastGC)).Format(time.RFC3339),
	}

	return metrics, nil
}

func (p *SystemInfoPlugin) GetRuntimeStats() (map[string]interface{}, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	stats := map[string]interface{}{
		"runtime_version": runtime.Version(),
		"go_root":         runtime.GOROOT(),
		"num_cgo_calls":   runtime.NumCgoCall(),
		"compiler":        runtime.Compiler,
	}

	// Add build info if available
	if buildInfo := p.getBuildInfo(); buildInfo != nil {
		stats["build_info"] = buildInfo
	}

	return stats, nil
}

func (p *SystemInfoPlugin) MonitorResources() (map[string]interface{}, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	// Force garbage collection and get memory stats
	runtime.GC()
	
	var before, after runtime.MemStats
	runtime.ReadMemStats(&before)
	
	// Small delay to see any changes
	time.Sleep(10 * time.Millisecond)
	runtime.ReadMemStats(&after)

	monitor := map[string]interface{}{
		"timestamp": time.Now().Format(time.RFC3339),
		"memory_before_gc": map[string]interface{}{
			"allocated_mb": float64(before.Alloc) / 1024 / 1024,
			"heap_objects": before.HeapObjects,
		},
		"memory_after_gc": map[string]interface{}{
			"allocated_mb": float64(after.Alloc) / 1024 / 1024,
			"heap_objects": after.HeapObjects,
		},
		"gc_stats": map[string]interface{}{
			"num_gc":          after.NumGC,
			"pause_total_ns":  after.PauseTotalNs,
			"gc_cpu_fraction": after.GCCPUFraction,
		},
		"goroutines": runtime.NumGoroutine(),
		"plugin_health": p.IsHealthy(),
		"plugin_uptime": time.Since(p.startTime).Seconds(),
	}

	return monitor, nil
}

// Utility methods

func (p *SystemInfoPlugin) getHostname() string {
	hostname, err := os.Hostname()
	if err != nil {
		return "unknown"
	}
	return hostname
}

func (p *SystemInfoPlugin) getUptime() string {
	return p.formatDuration(time.Since(p.startTime))
}

func (p *SystemInfoPlugin) getExecutable() string {
	executable, err := os.Executable()
	if err != nil {
		return "unknown"
	}
	return executable
}

func (p *SystemInfoPlugin) getWorkingDir() string {
	wd, err := os.Getwd()
	if err != nil {
		return "unknown"
	}
	return wd
}

func (p *SystemInfoPlugin) getBuildInfo() map[string]interface{} {
	// In a real implementation, this could include build-time information
	return map[string]interface{}{
		"build_time": "unknown",
		"git_commit": "unknown",
		"version":    "development",
	}
}

func (p *SystemInfoPlugin) formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.1fs", d.Seconds())
	} else if d < time.Hour {
		return fmt.Sprintf("%.1fm", d.Minutes())
	} else if d < 24*time.Hour {
		return fmt.Sprintf("%.1fh", d.Hours())
	}
	return fmt.Sprintf("%.1fd", d.Hours()/24)
}

func formatBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

func (p *SystemInfoPlugin) GetDetailedInfo() (map[string]interface{}, error) {
	if !p.IsHealthy() {
		return nil, fmt.Errorf("plugin not healthy")
	}

	systemInfo, _ := p.GetSystemInfo()
	memoryInfo, _ := p.GetMemoryInfo()
	cpuInfo, _ := p.GetCPUInfo()
	processInfo, _ := p.GetProcessInfo()
	performanceMetrics, _ := p.GetPerformanceMetrics()

	detailed := map[string]interface{}{
		"system":     systemInfo,
		"memory":     memoryInfo,
		"cpu":        cpuInfo,
		"process":    processInfo,
		"performance": performanceMetrics,
		"plugin_info": map[string]interface{}{
			"id":          p.id.String(),
			"name":        p.metadata.Name,
			"version":     p.metadata.Version.String(),
			"state":       p.status.State.String(),
			"initialized": p.initialized,
			"uptime":      time.Since(p.startTime).Seconds(),
		},
	}

	// Optionally include environment if configured
	if p.config["include_environment"].(bool) {
		envInfo, _ := p.GetEnvironmentInfo()
		detailed["environment"] = envInfo
	}

	return detailed, nil
}

// Plugin entry point for Go plugin system
var Plugin SystemInfoPlugin

func init() {
	Plugin = *NewSystemInfoPlugin()
}
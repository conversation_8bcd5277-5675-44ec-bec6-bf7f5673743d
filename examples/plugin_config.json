{"plugins": {"demo": "./examples/plugins/demo.go", "file_ops": "./examples/plugins/file_ops.go", "text_processor": "./examples/plugins/text_processor.go", "system_info": "./examples/plugins/system_info.go"}, "plugin_config": {"demo": {"greeting": "Welcome", "show_time": true, "random_max": 1000, "command_prefix": "demo"}, "file_ops": {"max_file_size": 1048576, "allowed_extensions": [".txt", ".md", ".json", ".yaml", ".yml", ".go"], "base_directory": "."}, "text_processor": {"max_text_length": 102400, "case_sensitive": false, "preserve_whitespace": true, "regex_timeout": 5}, "system_info": {"show_sensitive_info": false, "refresh_interval": 30, "include_environment": false, "max_process_count": 10}}, "security": {"allow_file_access": true, "allow_network_access": false, "trusted_plugins": ["demo", "system_info"], "sandbox_mode": true}, "ui": {"theme": "default", "show_line_numbers": true, "word_wrap": true, "tab_size": 4}}
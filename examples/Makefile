# TermiLLM Plugin Examples Makefile

.PHONY: all clean build-plugins build-demo build-file-ops build-text-processor build-system-info test-compile help

# Default target
all: build-plugins

# Help target
help:
	@echo "TermiLLM Plugin Examples"
	@echo "========================"
	@echo ""
	@echo "Available targets:"
	@echo "  all              - Build all plugins (default)"
	@echo "  build-plugins    - Build all plugins as .so files"
	@echo "  build-demo       - Build demo plugin"
	@echo "  build-file-ops   - Build file operations plugin"
	@echo "  build-text-processor - Build text processor plugin"
	@echo "  build-system-info - Build system info plugin"
	@echo "  test-compile     - Test compilation without building .so files"
	@echo "  clean            - Remove built plugin files"
	@echo "  help             - Show this help message"
	@echo ""
	@echo "Usage:"
	@echo "  make build-plugins    # Build all plugins"
	@echo "  make clean           # Clean built files"
	@echo "  make test-compile    # Test compilation"

# Build directory
BUILD_DIR := build
PLUGIN_DIR := plugins

# Ensure build directory exists
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

# Build all plugins
build-plugins: $(BUILD_DIR) build-demo build-file-ops build-text-processor build-system-info
	@echo "All plugins built successfully!"
	@echo "Plugin files are in the $(BUILD_DIR) directory:"
	@ls -la $(BUILD_DIR)/*.so 2>/dev/null || echo "No .so files found"

# Build individual plugins
build-demo: $(BUILD_DIR)
	@echo "Building demo plugin..."
	go build -buildmode=plugin -o $(BUILD_DIR)/demo.so $(PLUGIN_DIR)/demo.go
	@echo "Demo plugin built: $(BUILD_DIR)/demo.so"

build-file-ops: $(BUILD_DIR)
	@echo "Building file operations plugin..."
	go build -buildmode=plugin -o $(BUILD_DIR)/file_ops.so $(PLUGIN_DIR)/file_ops.go
	@echo "File operations plugin built: $(BUILD_DIR)/file_ops.so"

build-text-processor: $(BUILD_DIR)
	@echo "Building text processor plugin..."
	go build -buildmode=plugin -o $(BUILD_DIR)/text_processor.so $(PLUGIN_DIR)/text_processor.go
	@echo "Text processor plugin built: $(BUILD_DIR)/text_processor.so"

build-system-info: $(BUILD_DIR)
	@echo "Building system info plugin..."
	go build -buildmode=plugin -o $(BUILD_DIR)/system_info.so $(PLUGIN_DIR)/system_info.go
	@echo "System info plugin built: $(BUILD_DIR)/system_info.so"

# Test compilation without building plugin files
test-compile:
	@echo "Testing plugin compilation..."
	@echo "Testing demo plugin..."
	go build -buildmode=plugin -o /tmp/demo_test.so $(PLUGIN_DIR)/demo.go && rm -f /tmp/demo_test.so
	@echo "Testing file operations plugin..."
	go build -buildmode=plugin -o /tmp/file_ops_test.so $(PLUGIN_DIR)/file_ops.go && rm -f /tmp/file_ops_test.so
	@echo "Testing text processor plugin..."
	go build -buildmode=plugin -o /tmp/text_processor_test.so $(PLUGIN_DIR)/text_processor.go && rm -f /tmp/text_processor_test.so
	@echo "Testing system info plugin..."
	go build -buildmode=plugin -o /tmp/system_info_test.so $(PLUGIN_DIR)/system_info.go && rm -f /tmp/system_info_test.so
	@echo "All plugins compile successfully!"

# Clean built files
clean:
	@echo "Cleaning built plugin files..."
	rm -rf $(BUILD_DIR)
	@echo "Clean complete!"

# Install plugins to a target directory (optional)
install: build-plugins
	@if [ -z "$(INSTALL_DIR)" ]; then \
		echo "Usage: make install INSTALL_DIR=/path/to/install"; \
		exit 1; \
	fi
	@echo "Installing plugins to $(INSTALL_DIR)..."
	mkdir -p $(INSTALL_DIR)
	cp $(BUILD_DIR)/*.so $(INSTALL_DIR)/
	@echo "Plugins installed to $(INSTALL_DIR)"

# Development targets
dev-demo:
	@echo "Running demo plugin in development mode..."
	go run $(PLUGIN_DIR)/demo.go

# Quick test that checks if plugins load properly
test-load:
	@echo "Testing plugin loading..."
	@echo "This would require the TermiLLM runtime to be available"
	@echo "To test manually:"
	@echo "1. Start TermiLLM: go run ."
	@echo "2. Load plugin: :plugin.load $(BUILD_DIR)/demo.so"
	@echo "3. Test command: :demo hello"

# Show plugin information
info:
	@echo "TermiLLM Plugin Examples"
	@echo "========================"
	@echo "Plugin directory: $(PLUGIN_DIR)"
	@echo "Build directory: $(BUILD_DIR)"
	@echo ""
	@echo "Available plugins:"
	@ls -1 $(PLUGIN_DIR)/*.go 2>/dev/null | sed 's/.*\//  - /' | sed 's/\.go//' || echo "  No plugins found"
	@echo ""
	@echo "Built plugins:"
	@ls -1 $(BUILD_DIR)/*.so 2>/dev/null | sed 's/.*\//  - /' | sed 's/\.so//' || echo "  No built plugins found"

# Validate plugin code
validate:
	@echo "Validating plugin code..."
	go fmt $(PLUGIN_DIR)/*.go
	go vet $(PLUGIN_DIR)/*.go
	@echo "Validation complete!"

# Build for different platforms (example)
build-linux: $(BUILD_DIR)
	@echo "Building plugins for Linux..."
	GOOS=linux GOARCH=amd64 go build -buildmode=plugin -o $(BUILD_DIR)/demo-linux.so $(PLUGIN_DIR)/demo.go
	GOOS=linux GOARCH=amd64 go build -buildmode=plugin -o $(BUILD_DIR)/file_ops-linux.so $(PLUGIN_DIR)/file_ops.go
	GOOS=linux GOARCH=amd64 go build -buildmode=plugin -o $(BUILD_DIR)/text_processor-linux.so $(PLUGIN_DIR)/text_processor.go
	GOOS=linux GOARCH=amd64 go build -buildmode=plugin -o $(BUILD_DIR)/system_info-linux.so $(PLUGIN_DIR)/system_info.go
	@echo "Linux plugins built!"

build-darwin: $(BUILD_DIR)
	@echo "Building plugins for macOS..."
	GOOS=darwin GOARCH=amd64 go build -buildmode=plugin -o $(BUILD_DIR)/demo-darwin.so $(PLUGIN_DIR)/demo.go
	GOOS=darwin GOARCH=amd64 go build -buildmode=plugin -o $(BUILD_DIR)/file_ops-darwin.so $(PLUGIN_DIR)/file_ops.go
	GOOS=darwin GOARCH=amd64 go build -buildmode=plugin -o $(BUILD_DIR)/text_processor-darwin.so $(PLUGIN_DIR)/text_processor.go
	GOOS=darwin GOARCH=amd64 go build -buildmode=plugin -o $(BUILD_DIR)/system_info-darwin.so $(PLUGIN_DIR)/system_info.go
	@echo "macOS plugins built!"
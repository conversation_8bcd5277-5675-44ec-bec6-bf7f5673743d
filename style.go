package main

import "github.com/charmbracelet/lipgloss"

var (
	titleStyle = func() lipgloss.Style {
		// Create a custom border with only left and right sides
		b := lipgloss.Border{
			Left:  "│",
			Right: "├",
		}
		return lipgloss.NewStyle().
			Background(GetColor("text")).
			Border(b, false, true, false, true).Padding(0, 1)
	}()

	infoStyle = func() lipgloss.Style {
		// Create a custom border with only left and right sides
		b := lipgloss.Border{
			Left:  "┤",
			Right: "│",
		}
		return lipgloss.NewStyle().
			Background(GetColor("text")).
			Border(b, false, true, false, true).Padding(0, 1)
	}()

	helpStyle = func() lipgloss.Style {
		// Create a custom border with only left and right sides
		b := lipgloss.Border{
			Left:  "┤",
			Right: "├",
		}
		return lipgloss.NewStyle().
			// Background(lipgloss.Color("252")).
			Border(b, false, true, false, true).Padding(0, 1)
	}()

	sidebarStyle = func(focus bool) lipgloss.Style {
		borderColor := GetColor("border")
		backgroundColor := GetColor("background")

		if focus {
			borderColor = GetColor("border_focused")     // Highlight border when focused
			backgroundColor = GetColor("text_secondary") // Slightly lighter background
		}
		return lipgloss.NewStyle().
			Border(lipgloss.NormalBorder(), false, true, false, false).
			BorderForeground(borderColor).
			Padding(1, 1).
			Background(backgroundColor)
	}
)

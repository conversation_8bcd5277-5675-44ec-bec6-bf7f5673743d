package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"github.com/charmbracelet/lipgloss"
)

// Config holds all application configuration
type Config struct {
	UI       UIConfig      `json:"ui"`
	Sidebar  SidebarConfig `json:"sidebar"`
	Colors   ColorConfig   `json:"colors"`
	KeyBinds KeyBindConfig `json:"keybinds"`
}

// UIConfig contains general UI settings
type UIConfig struct {
	Title        string `json:"title"`
	DefaultWidth int    `json:"default_width"`
	ShowDebug    bool   `json:"show_debug"`
}

// SidebarConfig contains sidebar-specific settings
type SidebarConfig struct {
	MinWidth     int           `json:"min_width"`
	MaxWidth     int           `json:"max_width"`
	DefaultWidth int           `json:"default_width"`
	Padding      int           `json:"padding"`
	Visible      bool          `json:"visible"`
	Items        []SidebarItem `json:"items"`
}

// ColorConfig contains color theme settings
type ColorConfig struct {
	Primary       string `json:"primary"`
	Secondary     string `json:"secondary"`
	Accent        string `json:"accent"`
	Background    string `json:"background"`
	Border        string `json:"border"`
	BorderFocused string `json:"border_focused"`
	Text          string `json:"text"`
	TextSecondary string `json:"text_secondary"`
	TextHighlight string `json:"text_highlight"`
	Selection     string `json:"selection"`
	Spinner       string `json:"spinner"`
}

// KeyBindConfig contains key binding settings
type KeyBindConfig struct {
	Quit               []string `json:"quit"`
	ToggleSidebar      []string `json:"toggle_sidebar"`
	ToggleSidebarWidth []string `json:"toggle_sidebar_width"`
	FocusSidebar       []string `json:"focus_sidebar"`
	FocusMain          []string `json:"focus_main"`
	CommandMode        []string `json:"command_mode"`
	NavigateUp         []string `json:"navigate_up"`
	NavigateDown       []string `json:"navigate_down"`
	Select             []string `json:"select"`
}

// SidebarItemConfig represents a configurable sidebar item
type SidebarItemConfig struct {
	Name string `json:"name"`
	Type string `json:"type"`
	Icon string `json:"icon"`
}

// Global configuration instance
var AppConfig *Config

// DefaultConfig returns the default application configuration
func DefaultConfig() *Config {
	return &Config{
		UI: UIConfig{
			Title:        "TermiLLM",
			DefaultWidth: 80,
			ShowDebug:    false,
		},
		Sidebar: SidebarConfig{
			MinWidth:     30,
			MaxWidth:     50,
			DefaultWidth: 30,
			Padding:      2,
			Visible:      true,
			Items: []SidebarItem{
				{Name: "Files", Type: "section"},
				{Name: "sample.txt", Type: "file"},
				{Name: "test-content.txt", Type: "file"},
				{Name: "", Type: "separator"},
				{Name: "Tools", Type: "section"},
				{Name: "Search", Type: "tool"},
				{Name: "Stats", Type: "tool"},
				{Name: "Settings", Type: "tool"},
				{Name: "", Type: "separator"},
				{Name: "Help", Type: "section"},
				{Name: "About", Type: "tool"},
				{Name: "Shortcuts", Type: "tool"},
				{Name: "", Type: "separator"},
				{Name: "Navigation:", Type: "tip"},
				{Name: "↑↓ to navigate", Type: "tip"},
				{Name: "Enter to select", Type: "tip"},
				{Name: "Shift+S to toggle", Type: "tip"},
				{Name: "W to resize", Type: "tip"},
			},
		},
		Colors: ColorConfig{
			Primary:       "#7D56F4",
			Secondary:     "#73F59F",
			Accent:        "#F25D94",
			Background:    "#262626",
			Border:        "#444444",
			BorderFocused: "#5f87ff",
			Text:          "#EDEDED",
			TextSecondary: "#383838",
			TextHighlight: "#EDFF82",
			Selection:     "#69",
			Spinner:       "#69",
		},
		KeyBinds: KeyBindConfig{
			Quit:               []string{"q", "ctrl+c"},
			ToggleSidebar:      []string{"S"},
			ToggleSidebarWidth: []string{"w"},
			FocusSidebar:       []string{"s"},
			FocusMain:          []string{"m"},
			CommandMode:        []string{":"},
			NavigateUp:         []string{"up", "k"},
			NavigateDown:       []string{"down", "j"},
			Select:             []string{"enter"},
		},
	}
}

// LoadConfig loads configuration from file and environment variables
func LoadConfig() (*Config, error) {
	config := DefaultConfig()

	// Try to load from config file
	configPath := getConfigPath()
	if configPath != "" {
		if err := loadConfigFromFile(config, configPath); err != nil {
			return nil, fmt.Errorf("failed to load config from %s: %w", configPath, err)
		}
	}

	// Override with environment variables
	loadConfigFromEnv(config)

	// Validate configuration
	if err := validateConfig(config); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return config, nil
}

// getConfigPath returns the configuration file path
func getConfigPath() string {
	// Check environment variable first
	if path := os.Getenv("TERMILLM_CONFIG"); path != "" {
		return path
	}

	// Check current directory
	if _, err := os.Stat("termillm.json"); err == nil {
		return "termillm.json"
	}

	// Check home directory
	if home, err := os.UserHomeDir(); err == nil {
		configPath := filepath.Join(home, ".config", "termillm", "config.json")
		if _, err := os.Stat(configPath); err == nil {
			return configPath
		}
	}

	return ""
}

// loadConfigFromFile loads configuration from a JSON file
func loadConfigFromFile(config *Config, path string) error {
	data, err := os.ReadFile(path)
	if err != nil {
		return err
	}

	return json.Unmarshal(data, config)
}

// loadConfigFromEnv loads configuration overrides from environment variables
func loadConfigFromEnv(config *Config) {
	if title := os.Getenv("TERMILLM_TITLE"); title != "" {
		config.UI.Title = title
	}

	if debug := os.Getenv("TERMILLM_DEBUG"); debug == "true" || debug == "1" {
		config.UI.ShowDebug = true
	}

	if primary := os.Getenv("TERMILLM_PRIMARY_COLOR"); primary != "" {
		config.Colors.Primary = primary
	}

	if bg := os.Getenv("TERMILLM_BACKGROUND_COLOR"); bg != "" {
		config.Colors.Background = bg
	}
}

// validateConfig validates the configuration values
func validateConfig(config *Config) error {
	if config.Sidebar.MinWidth < 10 {
		return fmt.Errorf("sidebar min_width must be at least 10, got %d", config.Sidebar.MinWidth)
	}

	if config.Sidebar.MaxWidth < config.Sidebar.MinWidth {
		return fmt.Errorf("sidebar max_width (%d) must be >= min_width (%d)",
			config.Sidebar.MaxWidth, config.Sidebar.MinWidth)
	}

	if config.Sidebar.DefaultWidth < config.Sidebar.MinWidth ||
		config.Sidebar.DefaultWidth > config.Sidebar.MaxWidth {
		return fmt.Errorf("sidebar default_width (%d) must be between min_width (%d) and max_width (%d)",
			config.Sidebar.DefaultWidth, config.Sidebar.MinWidth, config.Sidebar.MaxWidth)
	}

	return nil
}

// SaveConfig saves the current configuration to a file
func SaveConfig(config *Config, path string) error {
	// Ensure directory exists
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	if err := os.WriteFile(path, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}

// InitConfig initializes the global configuration
func InitConfig() error {
	var err error
	AppConfig, err = LoadConfig()
	return err
}

// GetColor returns a lipgloss.Color from the color configuration
func GetColor(colorName string) lipgloss.Color {
	if AppConfig == nil {
		return lipgloss.Color("15") // Default white
	}

	colors := AppConfig.Colors
	switch colorName {
	case "primary":
		return lipgloss.Color(colors.Primary)
	case "secondary":
		return lipgloss.Color(colors.Secondary)
	case "accent":
		return lipgloss.Color(colors.Accent)
	case "background":
		return lipgloss.Color(colors.Background)
	case "border":
		return lipgloss.Color(colors.Border)
	case "border_focused":
		return lipgloss.Color(colors.BorderFocused)
	case "text":
		return lipgloss.Color(colors.Text)
	case "text_secondary":
		return lipgloss.Color(colors.TextSecondary)
	case "text_highlight":
		return lipgloss.Color(colors.TextHighlight)
	case "selection":
		return lipgloss.Color(colors.Selection)
	case "spinner":
		return lipgloss.Color(colors.Spinner)
	default:
		return lipgloss.Color("15") // Default white
	}
}

// GetUIConfig returns the UI configuration
func GetUIConfig() UIConfig {
	if AppConfig == nil {
		return DefaultConfig().UI
	}
	return AppConfig.UI
}

// GetSidebarConfig returns the sidebar configuration
func GetSidebarConfig() SidebarConfig {
	if AppConfig == nil {
		return DefaultConfig().Sidebar
	}
	return AppConfig.Sidebar
}

// GetKeyBindConfig returns the key binding configuration
func GetKeyBindConfig() KeyBindConfig {
	if AppConfig == nil {
		return DefaultConfig().KeyBinds
	}
	return AppConfig.KeyBinds
}

package main

import (
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/bubbles/viewport"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// MessageType represents the type of chat message
type MessageType int

const (
	UserMessage MessageType = iota
	AssistantMessage
	SystemMessage
)

// ChatMessage represents a single message in the chat conversation
type ChatMessage struct {
	Type      MessageType
	Content   string
	Timestamp time.Time
	ID        string
}

// ChatInterface manages the chat conversation and input
type ChatInterface struct {
	messages     []ChatMessage
	viewport     viewport.Model
	width        int
	height       int
	focused      bool
	sending      bool
	currentInput string
}

// NewChatInterface creates a new chat interface
func NewChatInterface() *ChatInterface {
	// Create viewport for chat messages
	vp := viewport.New(0, 0)
	vp.Style = lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("62"))

	return &ChatInterface{
		messages:    make([]ChatMessage, 0),
		viewport:    vp,
		width:       0,
		height:      0,
		focused:     true,
		sending:     false,
		currentInput: "",
	}
}

// SetSize updates the dimensions of the chat interface
func (c *ChatInterface) SetSize(width, height int) {
	c.width = width
	c.height = height

	// Ensure minimum dimensions to prevent panics
	if width < 10 {
		width = 10
	}
	if height < 5 {
		height = 5
	}
	
	// Update viewport size to use full available space
	c.viewport.Width = width - 2 // Account for borders
	c.viewport.Height = height - 2 // Account for borders
	
	// Initialize viewport content if empty
	if len(c.messages) == 0 && c.viewport.Height > 0 {
		c.viewport.SetContent("Welcome to TermiLLM Chat!\nType your message and press Enter to send.")
	}
}

// AddMessage adds a new message to the chat
func (c *ChatInterface) AddMessage(msgType MessageType, content string) {
	message := ChatMessage{
		Type:      msgType,
		Content:   content,
		Timestamp: time.Now(),
		ID:        fmt.Sprintf("msg_%d", time.Now().UnixNano()),
	}
	
	c.messages = append(c.messages, message)
	
	// Update viewport content with new message
	c.updateViewportWithInput()
	
	// Only auto-scroll to bottom if viewport is properly initialized
	if c.viewport.Height > 3 && c.viewport.Width > 3 && len(c.messages) > 0 {
		// Additional check to ensure viewport has been set up
		defer func() {
			if r := recover(); r != nil {
				// Silently recover from any panic in GotoBottom
				// This prevents crashes while viewport is initializing
			}
		}()
		c.viewport.GotoBottom()
	}
}

// SendMessage processes the current input and sends it as a user message
func (c *ChatInterface) SendMessage() tea.Cmd {
	input := strings.TrimSpace(c.currentInput)
	if input == "" {
		return nil
	}

	// Add user message
	c.AddMessage(UserMessage, input)
	
	// Clear the current input
	c.currentInput = ""
	
	// Set sending state
	c.sending = true
	
	// Return command to send message to LLM
	return c.sendToLLM(input)
}

// sendToLLM simulates sending a message to an LLM server
func (c *ChatInterface) sendToLLM(input string) tea.Cmd {
	return tea.Tick(time.Second*2, func(t time.Time) tea.Msg {
		return LLMResponseMsg{
			Input:    input,
			Response: c.generateMockResponse(input),
		}
	})
}

// generateMockResponse creates a mock response from the LLM
func (c *ChatInterface) generateMockResponse(input string) string {
	responses := []string{
		"I understand your question about: " + input,
		"That's an interesting point. Let me think about " + input,
		"Based on what you've said about " + input + ", I would suggest...",
		"Here's my analysis of: " + input,
		"Thank you for asking about " + input + ". Here's what I think...",
	}
	
	// Simple hash to pick a consistent response for the same input
	hash := 0
	for _, char := range input {
		hash += int(char)
	}
	
	return responses[hash%len(responses)]
}

// handleLLMResponse processes a response from the LLM
func (c *ChatInterface) handleLLMResponse(msg LLMResponseMsg) {
	c.sending = false
	c.AddMessage(AssistantMessage, msg.Response)
}

// Update handles tea messages and updates the chat interface
func (c *ChatInterface) Update(msg tea.Msg) tea.Cmd {
	var cmds []tea.Cmd
	var cmd tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		if msg.Type == tea.KeyCtrlC {
			// Allow Ctrl+C to quit
			return tea.Quit
		}
		
		// Only handle input if focused and not sending
		if c.focused && !c.sending {
			switch msg.Type {
			case tea.KeyEnter:
				// Send message on Enter
				if cmd = c.SendMessage(); cmd != nil {
					cmds = append(cmds, cmd)
				}
			case tea.KeyBackspace:
				// Handle backspace
				if len(c.currentInput) > 0 {
					c.currentInput = c.currentInput[:len(c.currentInput)-1]
				}
			case tea.KeyRunes:
				// Add typed characters to current input
				c.currentInput += string(msg.Runes)
			}
		}

	case LLMResponseMsg:
		c.handleLLMResponse(msg)
		return nil
	}

	// Update viewport
	c.viewport, cmd = c.viewport.Update(msg)
	if cmd != nil {
		cmds = append(cmds, cmd)
	}

	return tea.Batch(cmds...)
}

// View renders the chat interface
func (c *ChatInterface) View() string {
	if c.width == 0 || c.height == 0 {
		return "Loading chat..."
	}
	
	// Ensure viewport has minimum content to prevent panics
	if c.viewport.Height <= 0 {
		return "Initializing chat interface..."
	}

	// Update the viewport content to include current input
	c.updateViewportWithInput()

	// Return just the viewport view (no separate input area)
	return c.viewport.View()
}

// updateViewportWithInput updates the viewport content including current input
func (c *ChatInterface) updateViewportWithInput() {
	var content strings.Builder
	
	// Add all existing messages
	for i, msg := range c.messages {
		if i > 0 {
			content.WriteString("\n\n")
		}
		
		// Render message based on type
		switch msg.Type {
		case UserMessage:
			content.WriteString(c.renderUserMessage(msg))
		case AssistantMessage:
			content.WriteString(c.renderAssistantMessage(msg))
		case SystemMessage:
			content.WriteString(c.renderSystemMessage(msg))
		}
	}
	
	// Add current input if any (show as typing indicator)
	if c.currentInput != "" && !c.sending {
		if len(c.messages) > 0 {
			content.WriteString("\n\n")
		}
		
		// Add command help above the typing area
		helpStyle := lipgloss.NewStyle().
			Foreground(lipgloss.Color("240")).
			Italic(true)
		content.WriteString(helpStyle.Render("Commands: Enter=send, Backspace=delete, c=toggle mode") + "\n")
		
		// Style current input as pending user message
		userStyle := lipgloss.NewStyle().
			Foreground(lipgloss.Color("12")).
			Bold(true)
		
		inputStyle := lipgloss.NewStyle().
			Background(lipgloss.Color("24")).
			Foreground(lipgloss.Color("15")).
			Padding(0, 1).
			MarginLeft(2).
			Faint(true) // Make it slightly faded to show it's being typed
		
		header := userStyle.Render("You (typing...):")
		currentText := inputStyle.Render(c.currentInput + "█") // Add cursor
		content.WriteString(header + "\n" + currentText)
	}
	
	// Add status/help text at the bottom
	if len(c.messages) == 0 && c.currentInput == "" {
		content.WriteString("Welcome to TermiLLM Chat!\nStart typing your message...")
	} else if c.sending {
		content.WriteString("\n\n🤔 Thinking...")
	} else if c.currentInput == "" {
		// Show command help when not typing
		content.WriteString("\n\n" + lipgloss.NewStyle().
			Foreground(lipgloss.Color("240")).
			Italic(true).
			Render("Commands: Enter=send, Backspace=delete, c=toggle mode"))
		content.WriteString("\n" + lipgloss.NewStyle().
			Foreground(lipgloss.Color("240")).
			Render("Start typing your message..."))
	}
	
	c.viewport.SetContent(content.String())
}

// Focus sets the focus state of the chat interface
func (c *ChatInterface) Focus() {
	c.focused = true
}

// Blur removes focus from the chat interface
func (c *ChatInterface) Blur() {
	c.focused = false
}

// renderUserMessage renders a user message
func (c *ChatInterface) renderUserMessage(msg ChatMessage) string {
	userStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("12")).
		Bold(true)
	
	messageStyle := lipgloss.NewStyle().
		Background(lipgloss.Color("24")).
		Foreground(lipgloss.Color("15")).
		Padding(0, 1).
		MarginLeft(2)

	header := userStyle.Render("You:")
	content := messageStyle.Render(msg.Content)
	
	return header + "\n" + content
}

// renderAssistantMessage renders an assistant message
func (c *ChatInterface) renderAssistantMessage(msg ChatMessage) string {
	assistantStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("10")).
		Bold(true)
	
	messageStyle := lipgloss.NewStyle().
		Background(lipgloss.Color("22")).
		Foreground(lipgloss.Color("15")).
		Padding(0, 1).
		MarginLeft(2)

	header := assistantStyle.Render("Assistant:")
	content := messageStyle.Render(msg.Content)
	
	return header + "\n" + content
}

// renderSystemMessage renders a system message
func (c *ChatInterface) renderSystemMessage(msg ChatMessage) string {
	systemStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("8")).
		Italic(true)
	
	return systemStyle.Render("System: " + msg.Content)
}

// IsFocused returns whether the chat interface is focused
func (c *ChatInterface) IsFocused() bool {
	return c.focused
}

// IsSending returns whether a message is currently being sent
func (c *ChatInterface) IsSending() bool {
	return c.sending
}

// GetMessageCount returns the number of messages in the chat
func (c *ChatInterface) GetMessageCount() int {
	return len(c.messages)
}

// ClearMessages clears all messages from the chat
func (c *ChatInterface) ClearMessages() {
	c.messages = make([]ChatMessage, 0)
	c.updateViewportWithInput()
}

// LLMResponseMsg represents a message containing an LLM response
type LLMResponseMsg struct {
	Input    string
	Response string
}
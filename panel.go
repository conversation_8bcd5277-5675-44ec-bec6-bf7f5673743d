package main

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/bubbles/spinner"
	"github.com/charmbracelet/bubbles/viewport"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

func getDefaultWidth() int {
	if AppConfig != nil {
		return AppConfig.UI.DefaultWidth
	}
	return 80 // Fallback
}

type FocusState int

const (
	FocusMain FocusState = iota
	FocusSidebar
)

type Panel struct {
	main    viewport.Model
	sidebar *Sidebar
	chat    *ChatInterface
	ready   bool
	spinner spinner.Model
	loader  *LoaderAdapter
	// Store window dimensions for layout calculations
	width  int
	height int
	// Focus state
	focusState FocusState
	// State management
	stateID string
	// Mouse support
	mouseEnabled    bool
	resizing        bool
	resizeStartX    int
	resizeStartWidth int
	minSidebarWidth int
	maxSidebarWidth int
	resizeBorder    int // Width of the resize border area
	// Chat mode
	chatMode bool
}

func NewPanel(loader *LoaderAdapter) *Panel {
	// Create an empty viewport that will be properly initialized later
	main := viewport.Model{}

	// Initialize spinner
	s := spinner.New()
	s.Style = lipgloss.NewStyle().Foreground(GetColor("spinner"))
	s.Spinner = spinner.MiniDot

	// Initialize sidebar
	sidebar := NewSidebar()

	// Initialize chat interface
	chat := NewChatInterface()

	panel := &Panel{
		main:            main,
		sidebar:         sidebar,
		chat:            chat,
		spinner:         s,
		ready:           false, // Explicitly set to not ready
		loader:          loader,
		focusState:      FocusMain, // Start with main panel focused
		stateID:         "main-panel",
		mouseEnabled:    true,
		resizing:        false,
		minSidebarWidth: 20,
		maxSidebarWidth: 80,
		resizeBorder:    2, // 2 character wide resize area
		chatMode:        false, // Start in file viewing mode
	}

	// Initialize state in global state manager (deferred to avoid deadlock during startup)
	// This will be called after the tea.Program is started
	return panel
}

func (p *Panel) WindowSize(msg tea.WindowSizeMsg) tea.Cmd {
	// Store window dimensions for layout calculations
	p.width = msg.Width
	p.height = msg.Height

	// Update global state
	if GlobalStateManager != nil {
		GlobalStateManager.UpdateUIState(func(state *UIState) {
			state.WindowWidth = msg.Width
			state.WindowHeight = msg.Height
		})
		GlobalStateManager.UpdatePanelState(func(state *PanelState) {
			state.Width = msg.Width
			state.Height = msg.Height
		})
	}

	// Calculate dimensions
	sidebarWidth := p.sidebar.GetWidth()
	mainContentWidth := p.width - sidebarWidth
	headerHeight := lipgloss.Height(renderHeader(p.width))
	commandBarHeight := lipgloss.Height(renderFooter(p.main, p.width, p))
	contentHeight := p.height - headerHeight - commandBarHeight

	if p.chatMode {
		// In chat mode, set up the chat interface
		p.chat.SetSize(mainContentWidth, contentHeight)
	} else {
		// In file viewing mode, set up the main viewport
		p.main = viewport.New(mainContentWidth, contentHeight)
	}

	// Update sidebar size
	if p.sidebar.IsVisible() {
		p.sidebar.SetSize(sidebarWidth, contentHeight)
	}

	// Set the content if we have it from the loader and not in chat mode
	if p.loader != nil && !p.chatMode {
		content := p.loader.Content()
		if content != "" {
			p.main.SetContent(content)
		}
	}

	// If this is the first time we're setting up the panel,
	// return a command to signal when initialization is complete
	if !p.ready {
		return waitForPanelInitCmd()
	}

	return nil
}

func (p *Panel) SetContent(content string) {
	// This method is kept for backward compatibility
	// but now we just update the viewport directly
	p.main.SetContent(content)
}

func (p *Panel) SetSize(width, height int) {
	p.main.Width = width
	p.main.Height = height
}

func (p *Panel) View() string {
	if !p.ready || (p.loader != nil && p.loader.IsLoading()) {
		// Center the spinner and message in the terminal
		return p.initializing()
	}

	if p.loader != nil && p.loader.Error() != nil && !p.chatMode {
		// Show error message only in file mode
		return fmt.Sprintf("\nError loading content: %s", p.loader.Error())
	}

	// Calculate sidebar width
	sidebarWidth := p.sidebar.GetWidth()

	// Calculate main content width
	mainContentWidth := p.width - sidebarWidth
	if p.mouseEnabled && sidebarWidth > 0 {
		mainContentWidth-- // Account for resize indicator
	}

	// Render header and command bar with full width
	header := renderHeader(p.width)
	commandBar := renderFooter(p.main, p.width, p)

	// Calculate heights
	headerHeight := lipgloss.Height(header)
	commandBarHeight := lipgloss.Height(commandBar)

	// Calculate content area height
	contentHeight := p.height - headerHeight - commandBarHeight

	// Create the main content area
	var mainContent string
	if p.chatMode {
		// In chat mode, show the chat interface
		if p.chat.width != mainContentWidth || p.chat.height != contentHeight {
			p.chat.SetSize(mainContentWidth, contentHeight)
		}
		mainContent = p.chat.View()
	} else {
		// In file viewing mode, show the viewport
		// Adjust viewport dimensions to fit the available space
		if p.main.Height != contentHeight || p.main.Width != mainContentWidth {
			p.main.Height = contentHeight
			p.main.Width = mainContentWidth
		}
		mainContent = p.main.View()
	}

	var contentArea string
	if sidebarWidth > 0 {
		// Render sidebar
		sidebar := p.sidebar.View()
		
		// Create resize indicator if mouse is enabled
		var resizeIndicator string
		if p.mouseEnabled {
			resizeChar := "│"
			if p.resizing {
				resizeChar = "┃" // Thicker line when actively resizing
			}
			
			resizeStyle := lipgloss.NewStyle().
				Foreground(lipgloss.Color("240")).
				Background(lipgloss.Color("235"))
			
			if p.resizing {
				resizeStyle = resizeStyle.Foreground(lipgloss.Color("12")) // Highlight when resizing
			}
			
			// Create vertical resize indicator
			resizeLines := make([]string, contentHeight)
			for i := range resizeLines {
				resizeLines[i] = resizeChar
			}
			resizeIndicator = resizeStyle.Render(strings.Join(resizeLines, "\n"))
		}
		
		// Join sidebar, resize indicator, and main content horizontally
		if resizeIndicator != "" {
			contentArea = lipgloss.JoinHorizontal(
				lipgloss.Top,
				sidebar,
				resizeIndicator,
				mainContent,
			)
		} else {
			contentArea = lipgloss.JoinHorizontal(
				lipgloss.Top,
				sidebar,
				mainContent,
			)
		}
	} else {
		// No sidebar, just the main content
		contentArea = mainContent
	}

	// Create a layout with header, content area, and footer at bottom
	layout := lipgloss.JoinVertical(
		lipgloss.Left,
		header,
		contentArea,
		commandBar,
	)

	return layout
}

// Update handles messages and returns commands
func (p *Panel) Update(msg tea.Msg) (viewport.Model, tea.Cmd) {
	var (
		cmd  tea.Cmd
		cmds []tea.Cmd
	)

	switch msg := msg.(type) {
	case panelInitializedMsg:
		// Mark the panel as ready when we receive the initialization message
		p.ready = true
		
		// Update global state
		if GlobalStateManager != nil {
			GlobalStateManager.UpdatePanelState(func(state *PanelState) {
				state.Ready = p.ready
			})
		}
		
		// Start loading content if we have a loader and not in chat mode
		if p.loader != nil && !p.chatMode {
			cmds = append(cmds, p.loader.Load())
		}
		return p.main, tea.Batch(cmds...)

	case ContentLoadedMsg:
		if !p.chatMode {
			cmd = p.handleContentLoadedMsg()
			return p.main, cmd
		}

	case LLMResponseMsg:
		// Handle LLM responses in chat mode
		if p.chatMode {
			cmd = p.chat.Update(msg)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
		}

	case spinner.TickMsg:
		// Always update the spinner when we get a tick
		var spinnerCmd tea.Cmd
		p.spinner, spinnerCmd = p.spinner.Update(msg)
		cmds = append(cmds, spinnerCmd)

	case tea.MouseMsg:
		// Handle mouse events for resizing and interaction
		if p.mouseEnabled {
			cmd = p.handleMouseEvent(msg)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
		}

	default:
		// Route input based on focus state and mode
		if p.chatMode {
			// In chat mode, route input to chat interface
			cmd = p.chat.Update(msg)
			if cmd != nil {
				cmds = append(cmds, cmd)
			}
		} else {
			// In file viewing mode, route based on focus state
			switch p.focusState {
			case FocusMain:
				// Update the viewport when main panel is focused
				p.main, cmd = p.main.Update(msg)
				if cmd != nil {
					cmds = append(cmds, cmd)
				}
			case FocusSidebar:
				// Handle sidebar navigation and update
				cmd = p.sidebar.Update(msg)
				if cmd != nil {
					cmds = append(cmds, cmd)
				}

				// Check for item selection
				if keyMsg, ok := msg.(tea.KeyMsg); ok && keyMsg.String() == "enter" {
					item := p.sidebar.GetSelectedItem()
					if item != nil {
						p.handleSidebarSelection(item)
					}
				}
			}
		}
	}

	// Always keep the spinner ticking until we're ready or while loading
	if !p.ready || (p.loader != nil && p.loader.IsLoading()) {
		cmds = append(cmds, p.spinner.Tick)
	}

	return p.main, tea.Batch(cmds...)
}

// IsReady returns whether the panel has completed initialization
func (p *Panel) IsReady() bool {
	return p.ready
}

// ToggleSidebar toggles the sidebar visibility
func (p *Panel) ToggleSidebar() {
	p.sidebar.SetVisible(!p.sidebar.IsVisible())
	// If sidebar is hidden, focus should return to main
	if !p.sidebar.IsVisible() {
		p.focusState = FocusMain
		p.sidebar.SetFocused(false)
	}
}

// ToggleSidebarWidth toggles between minimum and maximum sidebar width
func (p *Panel) ToggleSidebarWidth() {
	p.sidebar.ToggleWidth()
}

// FocusSidebar sets focus to the sidebar
func (p *Panel) FocusSidebar() {
	if p.sidebar.IsVisible() {
		p.focusState = FocusSidebar
		p.sidebar.SetFocused(true)
		
		// Update global state
		if GlobalStateManager != nil {
			GlobalStateManager.UpdateUIState(func(state *UIState) {
				state.FocusState = FocusSidebar
			})
		}
	}
}

// FocusMain sets focus to the main panel
func (p *Panel) FocusMain() {
	p.focusState = FocusMain
	p.sidebar.SetFocused(false)
	
	// Update global state
	if GlobalStateManager != nil {
		GlobalStateManager.UpdateUIState(func(state *UIState) {
			state.FocusState = FocusMain
		})
	}
}

// GetFocusState returns the current focus state
func (p *Panel) GetFocusState() FocusState {
	return p.focusState
}

// handleSidebarSelection handles selection of sidebar items
func (p *Panel) handleSidebarSelection(item *SidebarItem) {
	// Handle different item types
	switch item.Type {
	case "file":
		// TODO: Load file content
		// For now, just show selection in main content
		p.main.SetContent(fmt.Sprintf("Selected file: %s\n\nThis would load the file content in a real implementation.", item.Name))
	case "tool":
		// TODO: Execute tool action
		p.main.SetContent(fmt.Sprintf("Selected tool: %s\n\nThis would execute the tool action in a real implementation.", item.Name))
	}
}

// Init returns the initial command for the panel
func (p *Panel) Init() tea.Cmd {
	// Initialize state in global state manager now that everything is set up
	p.initializeState()
	
	return p.spinner.Tick
}

// initializeState initializes the component state in the global state manager
func (p *Panel) initializeState() {
	if GlobalStateManager != nil {
		// Initialize panel state
		GlobalStateManager.UpdatePanelState(func(state *PanelState) {
			state.ID = p.stateID
			state.Ready = p.ready
			state.LoaderID = ""
			if p.loader != nil {
				state.LoaderID = p.loader.ID
			}
		})
		
		// Initialize sidebar state
		if p.sidebar != nil {
			GlobalStateManager.UpdateSidebarState(func(state *SidebarState) {
				state.ID = p.sidebar.stateID
				state.Visible = p.sidebar.visible
				state.Focused = p.sidebar.focused
				state.Width = p.sidebar.width
				state.SelectedIndex = p.sidebar.selectedIndex
				state.Items = make([]SidebarItem, len(p.sidebar.items))
				copy(state.Items, p.sidebar.items)
			})
		}
	}
}

// StartLoading initiates content loading
func (p *Panel) StartLoading() tea.Cmd {
	if p.loader == nil {
		return nil
	}

	// The loader will set its own loading state
	return p.loader.Load()
}

func (p *Panel) initializing() string {
	// Center the spinner and message in the terminal
	message := "Initializing..."
	if p.loader != nil && p.loader.IsLoading() {
		message = "Loading content..."
	}
	return fmt.Sprintf("\n %s %s", p.spinner.View(), message)
}

func (p *Panel) handleContentLoadedMsg() tea.Cmd {
	// Handle content loaded message
	// The loader has already updated its state
	// We just need to update the viewport with the content
	if p.loader != nil {
		if p.loader.Error() == nil {
			content := p.loader.Content()

			// Calculate dimensions accounting for sidebar
			sidebarWidth := p.sidebar.GetWidth()
			mainContentWidth := p.width - sidebarWidth
			headerHeight := lipgloss.Height(renderHeader(p.width))
			commandBarHeight := lipgloss.Height(renderFooter(p.main, p.width, p))
			contentHeight := p.height - headerHeight - commandBarHeight

			// Recreate the main viewport with the calculated dimensions
			p.main = viewport.New(mainContentWidth, contentHeight)

			// Update sidebar size if visible
			if p.sidebar.IsVisible() {
				p.sidebar.SetSize(sidebarWidth, contentHeight)
			}

			// Set the content
			focusInfo := "main"
			if p.focusState == FocusSidebar {
				focusInfo = "sidebar"
			}
			selectedItem := ""
			if item := p.sidebar.GetSelectedItem(); item != nil {
				selectedItem = item.Name
			}
			screenInfo := fmt.Sprintf("width: %d, height: %d, sidebar: %d, focus: %s, selected: %s",
				p.width, p.height, sidebarWidth, focusInfo, selectedItem)
			p.main.SetContent(content + "\n\n" + screenInfo)
		}
	}

	return nil
}

func renderHeader(width int) string {
	title := titleStyle.Render(GetUIConfig().Title)
	if width <= 0 {
		width = getDefaultWidth() // Default width if not set
	}
	line := strings.Repeat("─", max(0, width-lipgloss.Width(title)))
	return lipgloss.JoinHorizontal(lipgloss.Center, title, line)
}

func renderFooter(m viewport.Model, width int, p *Panel) string {
	// Render the help messages on the left with borders
	quitMsg := DefaultKeyMap.Quit.Render(helpStyle)
	toggleMsg := DefaultKeyMap.ToggleSidebar.Render(helpStyle)
	widthMsg := DefaultKeyMap.ToggleSidebarWidth.Render(helpStyle)
	focusSidebarMsg := DefaultKeyMap.FocusSidebar.Render(helpStyle)
	focusMainMsg := DefaultKeyMap.FocusMain.Render(helpStyle)
	chatModeMsg := DefaultKeyMap.ToggleChatMode.Render(helpStyle)
	
	// Add mouse help if enabled
	var mouseMsg string
	if p != nil && p.mouseEnabled {
		mouseMsg = helpStyle.Render("Ὓ1 drag sidebar border to resize")
	}

	// Render the scroll percentage on the right
	info := infoStyle.Render(fmt.Sprintf("%3.f%%", m.ScrollPercent()*100))

	if width <= 0 {
		width = getDefaultWidth() // Default width if not set
	}

	// Join help messages
	var helpMsgs string
	if mouseMsg != "" {
		helpMsgs = lipgloss.JoinHorizontal(lipgloss.Center, quitMsg, toggleMsg, widthMsg, focusSidebarMsg, focusMainMsg, chatModeMsg, mouseMsg)
	} else {
		helpMsgs = lipgloss.JoinHorizontal(lipgloss.Center, quitMsg, toggleMsg, widthMsg, focusSidebarMsg, focusMainMsg, chatModeMsg)
	}

	// Calculate the line length accounting for both the help messages and info
	lineWidth := max(0, width-lipgloss.Width(helpMsgs)-lipgloss.Width(info))
	line := strings.Repeat("─", lineWidth)

	return lipgloss.JoinHorizontal(lipgloss.Center, helpMsgs, line, info)
}

// handleMouseEvent handles mouse interactions for panel resizing
func (p *Panel) handleMouseEvent(msg tea.MouseMsg) tea.Cmd {
	if !p.sidebar.IsVisible() {
		return nil // No resizing if sidebar is hidden
	}
	
	sidebarWidth := p.sidebar.GetWidth()
	headerHeight := lipgloss.Height(renderHeader(p.width))
	
	// Adjust mouse position for header offset
	adjustedY := msg.Y - headerHeight
	
	// Check if mouse is in the resize border area (right edge of sidebar)
	resizeBorderStart := sidebarWidth - p.resizeBorder/2
	resizeBorderEnd := sidebarWidth + p.resizeBorder/2
	
	switch msg.Type {
	case tea.MouseLeft:
		// Start resizing if clicked on resize border
		if msg.X >= resizeBorderStart && msg.X <= resizeBorderEnd && adjustedY >= 0 {
			p.resizing = true
			p.resizeStartX = msg.X
			p.resizeStartWidth = sidebarWidth
			return nil
		}
		
		// Handle clicks on sidebar
		if msg.X < sidebarWidth && adjustedY >= 0 {
			p.FocusSidebar()
			// Calculate which sidebar item was clicked
			if adjustedY >= 0 && adjustedY < p.sidebar.GetHeight() {
				itemIndex := adjustedY // Simplified - would need proper calculation
				p.sidebar.SetSelectedIndex(itemIndex)
			}
			return nil
		}
		
		// Handle clicks on main content area
		if msg.X > sidebarWidth {
			p.FocusMain()
			return nil
		}
		
	case tea.MouseRelease:
		// Stop resizing
		if p.resizing {
			p.resizing = false
		}
		
	case tea.MouseMotion:
		// Handle resizing
		if p.resizing {
			delta := msg.X - p.resizeStartX
			newWidth := p.resizeStartWidth + delta
			
			// Constrain to min/max widths
			if newWidth < p.minSidebarWidth {
				newWidth = p.minSidebarWidth
			} else if newWidth > p.maxSidebarWidth {
				newWidth = p.maxSidebarWidth
			}
			
			// Update sidebar width
			p.sidebar.SetWidth(newWidth)
			
			// Recalculate layout
			p.recalculateLayout()
			
			return nil
		}
	}
	
	return nil
}

// recalculateLayout recalculates panel layout after resizing
func (p *Panel) recalculateLayout() {
	if p.width <= 0 || p.height <= 0 {
		return
	}
	
	sidebarWidth := p.sidebar.GetWidth()
	mainContentWidth := p.width - sidebarWidth
	headerHeight := lipgloss.Height(renderHeader(p.width))
	commandBarHeight := lipgloss.Height(renderFooter(p.main, p.width, p))
	contentHeight := p.height - headerHeight - commandBarHeight
	
	// Update main viewport size
	p.main.Width = mainContentWidth
	p.main.Height = contentHeight
	
	// Update sidebar size
	if p.sidebar.IsVisible() {
		p.sidebar.SetSize(sidebarWidth, contentHeight)
	}
	
	// Update global state
	if GlobalStateManager != nil {
		GlobalStateManager.UpdatePanelState(func(state *PanelState) {
			state.Width = p.width
			state.Height = p.height
		})
		GlobalStateManager.UpdateSidebarState(func(state *SidebarState) {
			state.Width = sidebarWidth
		})
	}
}

// SetMouseEnabled enables or disables mouse support
func (p *Panel) SetMouseEnabled(enabled bool) {
	p.mouseEnabled = enabled
}

// IsMouseEnabled returns whether mouse support is enabled
func (p *Panel) IsMouseEnabled() bool {
	return p.mouseEnabled
}

// IsResizing returns whether the panel is currently being resized
func (p *Panel) IsResizing() bool {
	return p.resizing
}

// SetSidebarWidthLimits sets the minimum and maximum sidebar widths
func (p *Panel) SetSidebarWidthLimits(min, max int) {
	p.minSidebarWidth = min
	p.maxSidebarWidth = max
}

// GetSidebarWidthLimits returns the minimum and maximum sidebar widths
func (p *Panel) GetSidebarWidthLimits() (int, int) {
	return p.minSidebarWidth, p.maxSidebarWidth
}

// GetResizeBorderWidth returns the width of the resize border area
func (p *Panel) GetResizeBorderWidth() int {
	return p.resizeBorder
}

// ToggleChatMode toggles between chat mode and file viewing mode
func (p *Panel) ToggleChatMode() {
	p.chatMode = !p.chatMode
	
	if p.chatMode {
		// Switching to chat mode - ensure proper sizing first
		if p.width > 0 && p.height > 0 {
			// Calculate dimensions for chat interface
			sidebarWidth := p.sidebar.GetWidth()
			mainContentWidth := p.width - sidebarWidth
			if p.mouseEnabled && sidebarWidth > 0 {
				mainContentWidth-- // Account for resize indicator
			}
			
			headerHeight := lipgloss.Height(renderHeader(p.width))
			commandBarHeight := lipgloss.Height(renderFooter(p.main, p.width, p))
			contentHeight := p.height - headerHeight - commandBarHeight
			
			// Set up chat interface with proper dimensions
			p.chat.SetSize(mainContentWidth, contentHeight)
			
			// Add welcome message if chat is empty and viewport is ready
			if p.chat.GetMessageCount() == 0 && p.chat.viewport.Height > 0 {
				p.chat.AddMessage(SystemMessage, "Welcome to TermiLLM Chat! Type a message to start the conversation.")
			}
		}
		
		// Focus the chat interface
		p.chat.Focus()
	} else {
		// Switching to file viewing mode
		p.chat.Blur()
	}
	
	// Recalculate layout for the new mode
	p.recalculateLayout()
}

// SetChatMode sets the chat mode explicitly
func (p *Panel) SetChatMode(enabled bool) {
	if p.chatMode == enabled {
		return // No change needed
	}
	p.ToggleChatMode()
}

// IsChatMode returns whether the panel is in chat mode
func (p *Panel) IsChatMode() bool {
	return p.chatMode
}

// GetChatInterface returns the chat interface for external access
func (p *Panel) GetChatInterface() *ChatInterface {
	return p.chat
}

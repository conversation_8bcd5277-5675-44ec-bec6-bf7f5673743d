# Configuration System

The TermiLLM application now supports a comprehensive configuration system that allows customization of UI elements, colors, sidebar content, and key bindings.

## Configuration Sources

Configuration is loaded in the following priority order:

1. **Default configuration** (built-in defaults)
2. **Configuration file** (JSON format)
3. **Environment variables** (highest priority)

## Configuration File Location

The application looks for configuration files in this order:

1. `$TERMILLM_CONFIG` environment variable path
2. `./termillm.json` (current directory)
3. `~/.config/termillm/config.json` (user config directory)

## Configuration Structure

### UI Settings
```json
{
  "ui": {
    "title": "TermiLLM",           // Window title
    "default_width": 80,           // Default terminal width
    "show_debug": false            // Enable debug mode
  }
}
```

### Sidebar Configuration
```json
{
  "sidebar": {
    "min_width": 30,               // Minimum sidebar width
    "max_width": 50,               // Maximum sidebar width  
    "default_width": 30,           // Default sidebar width
    "padding": 2,                  // Internal padding
    "visible": true,               // Initial visibility
    "items": [                     // Sidebar content items
      {
        "name": "Files",
        "type": "section"           // Types: section, file, tool, tip, separator
      }
    ]
  }
}
```

### Color Theme
```json
{
  "colors": {
    "primary": "#7D56F4",          // Primary theme color
    "secondary": "#73F59F",        // Secondary accent color
    "accent": "#F25D94",           // Additional accent color
    "background": "#262626",       // Background color
    "border": "#444444",           // Border color
    "border_focused": "#5f87ff",   // Focused border color
    "text": "#EDEDED",             // Primary text color
    "text_secondary": "#383838",   // Secondary text color
    "text_highlight": "#EDFF82",   // Highlighted text color
    "selection": "#69",            // Selection background
    "spinner": "#69"               // Loading spinner color
  }
}
```

### Key Bindings
```json
{
  "keybinds": {
    "quit": ["q", "ctrl+c"],                    // Exit application
    "toggle_sidebar": ["S"],                    // Toggle sidebar visibility
    "toggle_sidebar_width": ["w"],              // Toggle sidebar width
    "focus_sidebar": ["s"],                     // Focus sidebar
    "focus_main": ["m"],                        // Focus main panel
    "navigate_up": ["up", "k"],                 // Navigate up
    "navigate_down": ["down", "j"],             // Navigate down
    "select": ["enter"]                         // Select item
  }
}
```

## Environment Variables

Override specific settings using environment variables:

- `TERMILLM_CONFIG` - Path to configuration file
- `TERMILLM_TITLE` - Application title
- `TERMILLM_DEBUG` - Enable debug mode (true/1)
- `TERMILLM_PRIMARY_COLOR` - Primary theme color
- `TERMILLM_BACKGROUND_COLOR` - Background color

## Example Usage

### Custom Configuration File
```bash
# Create custom config
cat > termillm.json << EOF
{
  "ui": {
    "title": "My Terminal",
    "default_width": 120
  },
  "colors": {
    "primary": "#ff6b6b",
    "background": "#1a1a1a"
  }
}
EOF

# Run with custom config
./termillm
```

### Environment Variable Override
```bash
# Run with environment overrides
TERMILLM_TITLE="Dev Mode" TERMILLM_DEBUG=1 ./termillm
```

### Save Current Configuration
```go
// Save current configuration to file
config := GetCurrentConfig()
err := SaveConfig(config, "my-config.json")
```

## Configuration Validation

The system validates configuration values:

- Sidebar `min_width` must be ≥ 10
- Sidebar `max_width` must be ≥ `min_width`  
- Sidebar `default_width` must be between min and max
- Colors must be valid CSS color values
- Key bindings must be non-empty arrays

Invalid configurations will prevent application startup with descriptive error messages.
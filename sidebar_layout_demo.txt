SIDEBAR LAYOUT DEMONSTRATION
============================

1. SIDEBAR VISIBLE (Width: 30)
┌─────────────────────────────────────────────────────────────────────────────┐
│ TermiLLM ─────────────────────────────────────────────────────────────────  │
├─────────────────────────────────────────────────────────────────────────────┤
│┌──────────────────────────────────┐                                         │
││ Files                            │ Main Content Area                       │
││                                  │                                         │
││ sample.txt                       │ This is where the loaded content        │
││ test-content.txt                 │ will be displayed. The viewport         │
││                                  │ automatically adjusts its width         │
││ Tools                            │ based on the sidebar state.             │
││                                  │                                         │
││ Search                           │ Content can be scrolled using           │
││ Stats                            │ arrow keys or mouse wheel.              │
││ Settings                         │                                         │
││                                  │ Screen info will show:                  │
││ Help                             │ width: 120, height: 30, sidebar: 30    │
││                                  │                                         │
││ Tips:                            │                                         │
││ • Shift+S to toggle              │                                         │
││ • W to resize                    │                                         │
││ • S to focus sidebar             │                                         │
││ • M to focus main                │                                         │
│└──────────────────────────────────┘                                         │
├─────────────────────────────────────────────────────────────────────────────┤
│┤q quit├┤Shift+S toggle├┤w resize├┤s focus sidebar├┤m focus main├────── 85%│
└─────────────────────────────────────────────────────────────────────────────┘

2. SIDEBAR VISIBLE (Width: 50)
┌─────────────────────────────────────────────────────────────────────────────┐
│ TermiLLM ─────────────────────────────────────────────────────────────────  │
├─────────────────────────────────────────────────────────────────────────────┤
│┌──────────────────────────────────────────────────────────┐                 │
││ Files                                                    │ Main Content    │
││                                                          │                 │
││ sample.txt                                               │ Narrower content│
││ test-content.txt                                         │ area when       │
││                                                          │ sidebar is      │
││ Tools                                                    │ expanded to 50  │
││                                                          │ characters.     │
││ Search                                                   │                 │
││ Stats                                                    │ Screen info:    │
││ Settings                                                 │ sidebar: 50     │
││                                                          │                 │
││ Help                                                     │                 │
││                                                          │                 │
││ Tips:                                                    │                 │
││ • Shift+S to toggle                                      │                 │
││ • W to resize                                            │                 │
││ • S to focus sidebar                                     │                 │
││ • M to focus main                                        │                 │
│└──────────────────────────────────────────────────────────┘                 │
├─────────────────────────────────────────────────────────────────────────────┤
│┤q quit├┤Shift+S toggle├┤w resize├┤s focus sidebar├┤m focus main├────── 85%│
└─────────────────────────────────────────────────────────────────────────────┘

3. SIDEBAR HIDDEN
┌─────────────────────────────────────────────────────────────────────────────┐
│ TermiLLM ─────────────────────────────────────────────────────────────────  │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ Main Content Area (Full Width)                                             │
│                                                                             │
│ When the sidebar is hidden, the main content area expands to use the       │
│ full available width. This provides maximum space for viewing content.     │
│                                                                             │
│ The content loader design has been enhanced with the following features:   │
│ 1. ContentLoader interface with state management methods                    │
│ 2. BaseLoader implementation for common functionality                       │
│ 3. FileContentLoader and StringContentLoader implementations               │
│ 4. Thread-safe state management with mutex                                 │
│ 5. Clear separation of concerns between loaders and panels                 │
│                                                                             │
│ This design makes it easy to add new content loaders in the future and     │
│ keeps the loading state with the loader where it belongs.                  │
│                                                                             │
│ Screen info: width: 120, height: 30, sidebar: 0                           │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│┤q quit├┤Shift+S toggle├┤w resize├┤s focus sidebar├┤m focus main├────── 85%│
└─────────────────────────────────────────────────────────────────────────────┘

KEY FEATURES:
- Responsive layout that adapts to sidebar state
- Minimum sidebar width: 30 characters
- Maximum sidebar width: 50 characters  
- Toggle visibility with 'Shift+S' key
- Toggle width with 'w' key
- Focus sidebar with 's' key
- Focus main panel with 'm' key
- Visual focus indication (highlighted border)
- Focus-aware input routing
- Main content automatically adjusts width
- Header and footer span full terminal width
- Sidebar shows files, tools, and help information
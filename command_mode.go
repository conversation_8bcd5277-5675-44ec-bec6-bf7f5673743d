package main

import (
	"fmt"
	"strconv"
	"strings"

	"eionz.com/demo/pkg/completion"
	"eionz.com/demo/pkg/history"
	"eionz.com/demo/pkg/syntax"
	"eionz.com/demo/pkg/themes"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// CommandMode represents the command input mode state
type CommandMode struct {
	active         bool
	input          *completion.CompletionInput
	lastCommand    string
	commandHistory *history.CommandHistory
	errorMessage   string
	successMessage string
	messageTimeout int
	searchMode     bool
	searchResults  []history.HistoryEntry
	maxWidth       int // Maximum width for the input area
}

// NewCommandMode creates a new command mode
func NewCommandMode() *CommandMode {
	input := completion.NewCompletionInput("")
	input.SetWidth(60) // Default width, will be adjusted later

	// Initialize persistent command history
	historyPath := history.GetHistoryPath()
	cmdHistory := history.NewCommandHistory(historyPath, 1000)

	return &CommandMode{
		active:         false,
		input:          input,
		commandHistory: cmdHistory,
		messageTimeout: 0,
		searchMode:     false,
		maxWidth:       120, // Maximum width for input area
	}
}

// SetMaxWidth sets the maximum width for the input area
func (cm *CommandMode) SetMaxWidth(maxWidth int) {
	cm.maxWidth = maxWidth
}

// AdjustWidth adjusts the input width based on available space
func (cm *CommandMode) AdjustWidth(availableWidth int) {
	// Calculate optimal width (leave some margin for borders and padding)
	optimalWidth := availableWidth - 10 // Account for borders, padding, and margins

	// Ensure minimum width
	if optimalWidth < 30 {
		optimalWidth = 30
	}

	// Ensure maximum width
	if optimalWidth > cm.maxWidth {
		optimalWidth = cm.maxWidth
	}

	// Set the adjusted width
	cm.input.SetWidth(optimalWidth)
}

// IsActive returns whether command mode is active
func (cm *CommandMode) IsActive() bool {
	return cm.active
}

// Activate enters command mode
func (cm *CommandMode) Activate() {
	cm.active = true
	cm.input.SetActive(true)
	cm.input.Clear()
	cm.clearMessages()
}

// Deactivate exits command mode
func (cm *CommandMode) Deactivate() {
	cm.active = false
	cm.input.SetActive(false)
	cm.commandHistory.Reset()
	cm.exitSearchMode()
}

// clearMessages clears any status messages
func (cm *CommandMode) clearMessages() {
	cm.errorMessage = ""
	cm.successMessage = ""
	cm.messageTimeout = 0
}

// setError sets an error message
func (cm *CommandMode) setError(message string) {
	cm.errorMessage = message
	cm.successMessage = ""
	cm.messageTimeout = 150 // Show for ~3 seconds at 50fps
}

// setSuccess sets a success message
func (cm *CommandMode) setSuccess(message string) {
	cm.successMessage = message
	cm.errorMessage = ""
	cm.messageTimeout = 100 // Show for ~2 seconds at 50fps
}

// exitSearchMode exits search mode
func (cm *CommandMode) exitSearchMode() {
	cm.searchMode = false
	cm.searchResults = nil
	cm.commandHistory.ExitSearchMode()
}

// enterSearchMode enters search mode
func (cm *CommandMode) enterSearchMode(term string) {
	cm.searchMode = true
	cm.searchResults = cm.commandHistory.StartSearch(term)
}

// Update handles command mode input and returns commands to execute
func (cm *CommandMode) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	if !cm.active {
		return nil, nil
	}

	var cmd tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "enter":
			// Execute command
			command := strings.TrimSpace(cm.input.Value())
			if command != "" {
				cm.executeCommand(command)
				cm.lastCommand = command
			}
			cm.Deactivate()
			return nil, nil

		case "esc":
			// Cancel command mode
			cm.Deactivate()
			return nil, nil

		case "up":
			// Navigate command history up
			if prevCmd, found := cm.commandHistory.GetPrevious(); found {
				cm.input.SetValue(prevCmd)
			}
			return nil, nil

		case "down":
			// Navigate command history down
			if nextCmd, found := cm.commandHistory.GetNext(); found {
				cm.input.SetValue(nextCmd)
			} else {
				cm.input.Clear()
			}
			return nil, nil

		case "ctrl+r":
			// Start reverse search
			if !cm.searchMode {
				cm.enterSearchMode("")
				cm.setSuccess("Search mode: type to search history")
			} else {
				// Continue search with current term
				if prevCmd, found := cm.commandHistory.GetPrevious(); found {
					cm.input.SetValue(prevCmd)
				}
			}
			return nil, nil

		case "ctrl+s":
			// Forward search in search mode
			if cm.searchMode {
				if nextCmd, found := cm.commandHistory.GetNext(); found {
					cm.input.SetValue(nextCmd)
				}
			}
			return nil, nil

		default:
			// Pass other keys to the completion input
			cmd = cm.input.Update(msg)
		}

	default:
		cmd = cm.input.Update(msg)
	}

	// Update message timeout
	if cm.messageTimeout > 0 {
		cm.messageTimeout--
		if cm.messageTimeout == 0 {
			cm.clearMessages()
		}
	}

	return nil, cmd
}

// executeCommand processes and executes a command
func (cm *CommandMode) executeCommand(command string) {
	// Remove the leading "/" if present before processing
	if strings.HasPrefix(command, "/") {
		command = command[1:]
	}

	parts := strings.Fields(command)
	if len(parts) == 0 {
		return
	}

	commandName := parts[0]
	args := parts[1:]

	// Execute the command and record the result
	success := true
	errorMsg := ""

	switch commandName {
	case "help":
		cm.handleHelp(args)
	case "quit", "exit":
		cm.handleQuit()
	case "theme":
		if err := cm.handleTheme(args); err != nil {
			success = false
			errorMsg = err.Error()
		}
	case "themes":
		cm.handleThemes(args)
	case "syntax":
		if err := cm.handleSyntax(args); err != nil {
			success = false
			errorMsg = err.Error()
		}
	case "clear":
		cm.handleClear()
	case "history":
		cm.handleHistory(args)
	case "reload":
		cm.handleReload()
	case "goto":
		if err := cm.handleGoto(args); err != nil {
			success = false
			errorMsg = err.Error()
		}
	case "search":
		cm.handleSearch(args)
	default:
		success = false
		errorMsg = fmt.Sprintf("Unknown command: %s", commandName)
		cm.setError(errorMsg)
	}

	// Record command in history with original format (including /)
	originalCommand := command
	if strings.HasPrefix(cm.input.Value(), "/") {
		originalCommand = "/" + command
	}
	cm.commandHistory.Add(originalCommand, success, errorMsg)
}

// Command handlers

func (cm *CommandMode) handleHelp(args []string) {
	if len(args) == 0 {
		cm.setSuccess("Available commands: help, quit, theme, syntax, clear, history, reload, goto")
	} else {
		command := args[0]
		switch command {
		case "theme":
			cm.setSuccess("theme <name> - Change syntax highlighting theme (dark, light, default, monochrome)")
		case "syntax":
			cm.setSuccess("syntax <on|off|language> - Control syntax highlighting")
		case "goto":
			cm.setSuccess("goto <line> - Go to specific line number")
		case "quit":
			cm.setSuccess("quit - Exit the application")
		default:
			cm.setError(fmt.Sprintf("No help available for: %s", command))
		}
	}
}

func (cm *CommandMode) handleQuit() {
	// This would typically send a quit message to the main application
	cm.setSuccess("Quit command received")
	// TODO: Send tea.Quit command to main application
}

func (cm *CommandMode) handleTheme(args []string) error {
	themeManager := themes.GetGlobalThemeManager()

	if len(args) == 0 {
		// List available themes
		availableThemes := themeManager.GetAvailableThemes()
		currentTheme := themeManager.GetCurrentTheme()
		currentName := "none"
		if currentTheme != nil {
			currentName = currentTheme.Name
		}
		cm.setSuccess(
			fmt.Sprintf(
				"Current: %s. Available: %s",
				currentName,
				strings.Join(availableThemes, ", "),
			),
		)
		return nil
	}

	themeName := args[0]

	// Handle theme subcommands
	if len(args) > 1 {
		switch args[0] {
		case "save":
			if len(args) < 2 {
				cm.setError("Usage: theme save <name>")
				return fmt.Errorf("missing theme name")
			}
			return cm.handleSaveTheme(args[1])
		case "delete":
			if len(args) < 2 {
				cm.setError("Usage: theme delete <name>")
				return fmt.Errorf("missing theme name")
			}
			return cm.handleDeleteTheme(args[1])
		case "info":
			if len(args) < 2 {
				cm.setError("Usage: theme info <name>")
				return fmt.Errorf("missing theme name")
			}
			return cm.handleThemeInfo(args[1])
		}
	}

	// Set theme
	if err := themeManager.SetTheme(themeName); err != nil {
		cm.setError(fmt.Sprintf("Failed to set theme: %s", err.Error()))
		return err
	}

	// Apply the theme
	if err := themeManager.ApplyTheme(); err != nil {
		cm.setError(fmt.Sprintf("Failed to apply theme: %s", err.Error()))
		return err
	}

	cm.setSuccess(fmt.Sprintf("Theme changed to: %s", themeName))
	return nil
}

func (cm *CommandMode) handleSyntax(args []string) error {
	if len(args) == 0 {
		cm.setError("Usage: syntax <on|off|language>")
		return fmt.Errorf("missing syntax argument")
	}

	syntaxService := syntax.GetGlobalSyntaxService()
	arg := args[0]

	switch arg {
	case "on":
		syntaxService.SetEnabled(true)
		cm.setSuccess("Syntax highlighting enabled")
	case "off":
		syntaxService.SetEnabled(false)
		cm.setSuccess("Syntax highlighting disabled")
	default:
		// Assume it's a language name
		supportedLanguages := syntax.GetSupportedLanguages()
		found := false
		for _, lang := range supportedLanguages {
			if lang == arg {
				found = true
				break
			}
		}
		if found {
			cm.setSuccess(fmt.Sprintf("Language set to: %s", arg))
			// TODO: Set language for current content
		} else {
			cm.setError(fmt.Sprintf("Unsupported language: %s", arg))
			return fmt.Errorf("unsupported language: %s", arg)
		}
	}
	return nil
}

func (cm *CommandMode) handleClear() {
	// This would typically clear the main content area
	cm.setSuccess("Content cleared")
	// TODO: Send clear message to main application
}

func (cm *CommandMode) handleHistory(args []string) {
	if len(args) > 0 {
		// Handle history subcommands
		switch args[0] {
		case "clear":
			cm.commandHistory.Clear()
			cm.setSuccess("Command history cleared")
			return
		case "stats":
			stats := cm.commandHistory.GetStatistics()
			message := fmt.Sprintf("Total: %d, Success rate: %.1f%%, Most used: %s",
				stats.TotalCommands, stats.SuccessRate, stats.MostUsedCommand)
			cm.setSuccess(message)
			return
		case "search":
			if len(args) > 1 {
				cm.handleSearch(args[1:])
			} else {
				cm.setError("Usage: history search <term>")
			}
			return
		}
	}

	// Default: show recent history
	recentEntries := cm.commandHistory.GetRecent(5)
	if len(recentEntries) == 0 {
		cm.setSuccess("Command history is empty")
	} else {
		var commands []string
		for _, entry := range recentEntries {
			status := "✓"
			if !entry.Success {
				status = "✗"
			}
			commands = append(commands, fmt.Sprintf("%s %s", status, entry.Command))
		}
		cm.setSuccess(fmt.Sprintf("Recent commands: %s", strings.Join(commands, ", ")))
	}
}

func (cm *CommandMode) handleReload() {
	cm.setSuccess("Content reloaded")
	// TODO: Send reload message to main application
}

func (cm *CommandMode) handleGoto(args []string) error {
	if len(args) == 0 {
		cm.setError("Usage: goto <line_number>")
		return fmt.Errorf("missing line number")
	}

	lineNum, err := strconv.Atoi(args[0])
	if err != nil {
		cm.setError("Invalid line number")
		return fmt.Errorf("invalid line number: %s", args[0])
	}

	if lineNum <= 0 {
		cm.setError("Line number must be positive")
		return fmt.Errorf("line number must be positive")
	}

	cm.setSuccess(fmt.Sprintf("Going to line %d", lineNum))
	// TODO: Send goto message to main application
	return nil
}

// handleSearch handles search commands
func (cm *CommandMode) handleSearch(args []string) {
	if len(args) == 0 {
		cm.setError("Usage: search <term>")
		return
	}

	searchTerm := strings.Join(args, " ")
	results := cm.commandHistory.Search(searchTerm)

	if len(results) == 0 {
		cm.setError(fmt.Sprintf("No commands found matching: %s", searchTerm))
	} else {
		cm.searchResults = results
		cm.searchMode = true
		message := fmt.Sprintf("Found %d commands matching '%s'. Use ↑↓ to navigate.", len(results), searchTerm)
		cm.setSuccess(message)
	}
}

// handleThemes handles the themes command
func (cm *CommandMode) handleThemes(args []string) {
	themeManager := themes.GetGlobalThemeManager()

	if len(args) == 0 {
		// List all themes with details
		availableThemes := themeManager.GetAvailableThemes()
		if len(availableThemes) == 0 {
			cm.setError("No themes available")
			return
		}

		var themeList []string
		for _, name := range availableThemes {
			if theme, exists := themeManager.GetTheme(name); exists {
				desc := theme.Description
				if len(desc) > 40 {
					desc = desc[:37] + "..."
				}
				themeList = append(themeList, fmt.Sprintf("%s (%s)", name, desc))
			} else {
				themeList = append(themeList, name)
			}
		}

		cm.setSuccess(fmt.Sprintf("Available themes: %s", strings.Join(themeList, ", ")))
		return
	}

	// Handle subcommands
	switch args[0] {
	case "list":
		cm.handleThemes([]string{}) // Recursive call with no args
	case "current":
		currentTheme := themeManager.GetCurrentTheme()
		if currentTheme == nil {
			cm.setError("No theme currently set")
		} else {
			cm.setSuccess(fmt.Sprintf("Current theme: %s - %s", currentTheme.Name, currentTheme.Description))
		}
	default:
		cm.setError("Usage: themes [list|current]")
	}
}

// handleSaveTheme saves the current theme with a new name
func (cm *CommandMode) handleSaveTheme(name string) error {
	themeManager := themes.GetGlobalThemeManager()
	currentTheme := themeManager.GetCurrentTheme()

	if currentTheme == nil {
		cm.setError("No current theme to save")
		return fmt.Errorf("no current theme")
	}

	// Create a copy with the new name
	newTheme := *currentTheme
	newTheme.Name = name
	newTheme.Author = "Custom"
	newTheme.Version = "1.0.0"

	if err := themeManager.SaveTheme(&newTheme); err != nil {
		cm.setError(fmt.Sprintf("Failed to save theme: %s", err.Error()))
		return err
	}

	cm.setSuccess(fmt.Sprintf("Theme saved as: %s", name))
	return nil
}

// handleDeleteTheme deletes a custom theme
func (cm *CommandMode) handleDeleteTheme(name string) error {
	themeManager := themes.GetGlobalThemeManager()

	if err := themeManager.DeleteTheme(name); err != nil {
		cm.setError(fmt.Sprintf("Failed to delete theme: %s", err.Error()))
		return err
	}

	cm.setSuccess(fmt.Sprintf("Theme deleted: %s", name))
	return nil
}

// handleThemeInfo shows information about a theme
func (cm *CommandMode) handleThemeInfo(name string) error {
	themeManager := themes.GetGlobalThemeManager()
	theme, exists := themeManager.GetTheme(name)

	if !exists {
		cm.setError(fmt.Sprintf("Theme not found: %s", name))
		return fmt.Errorf("theme not found: %s", name)
	}

	info := fmt.Sprintf("Theme: %s v%s by %s - %s",
		theme.Name, theme.Version, theme.Author, theme.Description)
	cm.setSuccess(info)
	return nil
}

// View renders the command mode interface
func (cm *CommandMode) View() string {
	if !cm.active {
		return ""
	}

	var sections []string

	// Search mode indicator and status messages (above input box)
	if cm.searchMode && len(cm.searchResults) > 0 {
		searchStyle := lipgloss.NewStyle().
			Foreground(lipgloss.Color("99")).
			Italic(true)
		searchText := fmt.Sprintf("Search mode: %d results", len(cm.searchResults))
		sections = append(sections, searchStyle.Render(searchText))
	}

	// Status messages (above input box)
	if cm.errorMessage != "" {
		errorStyle := lipgloss.NewStyle().
			Foreground(lipgloss.Color("196")).
			Bold(true)
		sections = append(sections, errorStyle.Render("Error: "+cm.errorMessage))
	}

	if cm.successMessage != "" {
		successStyle := lipgloss.NewStyle().
			Foreground(lipgloss.Color("46")).
			Bold(true)
		sections = append(sections, successStyle.Render(cm.successMessage))
	}

	// Input area (in the middle)
	sections = append(sections, cm.input.View())

	// Help text (back at the bottom)
	helpStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("244")).
		Italic(true)
	helpText := "Enter: execute, Esc: cancel, Tab: complete, ↑↓: history, Ctrl+R: search"
	if cm.searchMode {
		helpText = "Enter: select, Esc: exit search, ↑↓: navigate results"
	}
	// Add 1 character padding to align with command list and input text
	sections = append(sections, helpStyle.Render(helpText))

	return lipgloss.JoinVertical(lipgloss.Left, sections...)
}

// GetHeight returns the height needed for command mode display
func (cm *CommandMode) GetHeight() int {
	if !cm.active {
		return 0
	}

	height := cm.input.GetHeight() + 1 // +1 for help text

	if cm.errorMessage != "" || cm.successMessage != "" {
		height++ // +1 for status message
	}

	return height
}


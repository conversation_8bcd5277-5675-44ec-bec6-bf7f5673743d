This is a sample text file that demonstrates loading content from a file.

The content loader design has been implemented with the following features:
1. ContentLoader interface for different content sources
2. FileContentLoader for loading from files
3. StringContentLoader for loading from strings
4. <PERSON><PERSON> moved to the panel structure
5. Loading state management in the panel

This design makes it easy to add new content loaders in the future.

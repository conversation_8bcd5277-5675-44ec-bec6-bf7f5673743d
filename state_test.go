package main

import (
	"os"
	"testing"
	"time"
)

// Test observer implementation
type testObserver struct {
	changes []StateChangeEvent
}

func (t *testObserver) OnStateChanged(oldState, newState interface{}) {
	t.changes = append(t.changes, StateChangeEvent{
		Timestamp: time.Now(),
		OldState:  oldState,
		NewState:  newState,
		Source:    "test",
	})
}

func TestAppStateManager_Creation(t *testing.T) {
	manager := NewAppStateManager()
	
	if manager == nil {
		t.Fatal("Manager should not be nil")
	}
	
	state := manager.GetState()
	if state == nil {
		t.Fatal("State should not be nil")
	}
	
	if state.Version == "" {
		t.Error("Version should not be empty")
	}
	
	if state.Panel == nil {
		t.Error("Panel state should not be nil")
	}
	
	if state.Sidebar == nil {
		t.Error("Sidebar state should not be nil")
	}
	
	if state.Content == nil {
		t.Error("Content state should not be nil")
	}
}

func TestAppStateManager_UIStateUpdate(t *testing.T) {
	manager := NewAppStateManager()
	manager.EnableAutoSave(false) // Disable auto-save for tests
	
	observer := &testObserver{}
	
	// Subscribe to state changes
	unsubscribe := manager.Subscribe(observer)
	defer unsubscribe()
	
	// Update UI state
	manager.UpdateUIState(func(state *UIState) {
		state.WindowWidth = 100
		state.WindowHeight = 50
		state.FocusState = FocusSidebar
	})
	
	// Wait a bit for goroutine to complete
	time.Sleep(10 * time.Millisecond)
	
	// Verify state was updated
	state := manager.GetState()
	if state.UI.WindowWidth != 100 {
		t.Errorf("Expected width 100, got %d", state.UI.WindowWidth)
	}
	
	if state.UI.WindowHeight != 50 {
		t.Errorf("Expected height 50, got %d", state.UI.WindowHeight)
	}
	
	if state.UI.FocusState != FocusSidebar {
		t.Errorf("Expected focus state FocusSidebar, got %d", state.UI.FocusState)
	}
	
	// Check that observer was notified
	if len(observer.changes) == 0 {
		t.Error("Observer should have been notified of state change")
	}
}

func TestAppStateManager_PanelStateUpdate(t *testing.T) {
	manager := NewAppStateManager()
	manager.EnableAutoSave(false) // Disable auto-save for tests
	
	manager.UpdatePanelState(func(state *PanelState) {
		state.Width = 200
		state.Height = 100
		state.Ready = true
		state.LoaderID = "test-loader"
	})
	
	state := manager.GetState()
	panel := state.Panel
	
	if panel.Width != 200 {
		t.Errorf("Expected panel width 200, got %d", panel.Width)
	}
	
	if panel.Height != 100 {
		t.Errorf("Expected panel height 100, got %d", panel.Height)
	}
	
	if !panel.Ready {
		t.Error("Expected panel to be ready")
	}
	
	if panel.LoaderID != "test-loader" {
		t.Errorf("Expected loader ID 'test-loader', got '%s'", panel.LoaderID)
	}
	
	if panel.LastUpdated.IsZero() {
		t.Error("LastUpdated should be set")
	}
}

func TestAppStateManager_SidebarStateUpdate(t *testing.T) {
	manager := NewAppStateManager()
	manager.EnableAutoSave(false) // Disable auto-save for tests
	
	testItems := []SidebarItem{
		{Name: "Test1", Type: "file"},
		{Name: "Test2", Type: "tool"},
	}
	
	manager.UpdateSidebarState(func(state *SidebarState) {
		state.Visible = false
		state.Focused = true
		state.Width = 60
		state.SelectedIndex = 1
		state.Items = testItems
	})
	
	state := manager.GetState()
	sidebar := state.Sidebar
	
	if sidebar.Visible {
		t.Error("Expected sidebar to be hidden")
	}
	
	if !sidebar.Focused {
		t.Error("Expected sidebar to be focused")
	}
	
	if sidebar.Width != 60 {
		t.Errorf("Expected sidebar width 60, got %d", sidebar.Width)
	}
	
	if sidebar.SelectedIndex != 1 {
		t.Errorf("Expected selected index 1, got %d", sidebar.SelectedIndex)
	}
	
	if len(sidebar.Items) != 2 {
		t.Errorf("Expected 2 items, got %d", len(sidebar.Items))
	}
}

func TestAppStateManager_CustomData(t *testing.T) {
	manager := NewAppStateManager()
	manager.EnableAutoSave(false) // Disable auto-save for tests
	
	// Set custom data
	manager.SetCustomData("test_key", "test_value")
	manager.SetCustomData("number", 42)
	
	// Get custom data
	value, exists := manager.GetCustomData("test_key")
	if !exists {
		t.Error("Expected test_key to exist")
	}
	
	if value != "test_value" {
		t.Errorf("Expected 'test_value', got %v", value)
	}
	
	number, exists := manager.GetCustomData("number")
	if !exists {
		t.Error("Expected number to exist")
	}
	
	if number != 42 {
		t.Errorf("Expected 42, got %v", number)
	}
	
	// Test non-existent key
	_, exists = manager.GetCustomData("non_existent")
	if exists {
		t.Error("Expected non_existent key to not exist")
	}
}

func TestAppStateManager_Validation(t *testing.T) {
	manager := NewAppStateManager()
	manager.EnableAutoSave(false) // Disable auto-save for tests
	
	// Valid state should pass validation
	err := manager.Validate()
	if err != nil {
		t.Errorf("Valid state should pass validation: %v", err)
	}
	
	// Invalid selected index should fail validation
	manager.UpdateSidebarState(func(state *SidebarState) {
		state.SelectedIndex = -1
	})
	
	err = manager.Validate()
	if err == nil {
		t.Error("Invalid selected index should fail validation")
	}
	
	// Out of bounds selected index should fail validation
	manager.UpdateSidebarState(func(state *SidebarState) {
		state.Items = []SidebarItem{{Name: "Test", Type: "file"}}
		state.SelectedIndex = 5
	})
	
	err = manager.Validate()
	if err == nil {
		t.Error("Out of bounds selected index should fail validation")
	}
}

func TestAppStateManager_Persistence(t *testing.T) {
	// Create temporary state file
	tempFile := "test_state.json"
	defer os.Remove(tempFile)
	
	manager := NewAppStateManager()
	manager.stateFile = tempFile
	
	// Disable auto-save to avoid conflicts
	manager.EnableAutoSave(false)
	
	// Update some state
	manager.UpdateUIState(func(state *UIState) {
		state.WindowWidth = 800
		state.WindowHeight = 600
	})
	
	manager.UpdatePanelState(func(state *PanelState) {
		state.Ready = true
		state.LoaderID = "persistence-test"
	})
	
	// Save state
	err := manager.Save()
	if err != nil {
		t.Fatalf("Failed to save state: %v", err)
	}
	
	// Create new manager and load state
	newManager := NewAppStateManager()
	newManager.stateFile = tempFile
	newManager.EnableAutoSave(false)
	
	err = newManager.Load()
	if err != nil {
		t.Fatalf("Failed to load state: %v", err)
	}
	
	// Verify loaded state
	state := newManager.GetState()
	
	if state.UI.WindowWidth != 800 {
		t.Errorf("Expected loaded width 800, got %d", state.UI.WindowWidth)
	}
	
	if state.UI.WindowHeight != 600 {
		t.Errorf("Expected loaded height 600, got %d", state.UI.WindowHeight)
	}
	
	if !state.Panel.Ready {
		t.Error("Expected loaded panel to be ready")
	}
	
	if state.Panel.LoaderID != "persistence-test" {
		t.Errorf("Expected loaded loader ID 'persistence-test', got '%s'", state.Panel.LoaderID)
	}
}

func TestAppStateManager_Reset(t *testing.T) {
	manager := NewAppStateManager()
	manager.EnableAutoSave(false) // Disable auto-save for tests
	
	// Update some state
	manager.UpdateUIState(func(state *UIState) {
		state.WindowWidth = 1000
	})
	
	manager.SetCustomData("test", "value")
	
	// Reset state
	manager.Reset()
	
	// Verify state was reset
	state := manager.GetState()
	
	if state.UI.WindowWidth != 0 {
		t.Errorf("Expected reset width 0, got %d", state.UI.WindowWidth)
	}
	
	if len(state.CustomData) != 0 {
		t.Errorf("Expected empty custom data, got %d items", len(state.CustomData))
	}
}

func TestComponentState_Clone(t *testing.T) {
	original := &PanelState{
		ID:      "test",
		Ready:   true,
		Width:   100,
		Height:  50,
		LoaderID: "loader",
	}
	
	cloned := original.Clone().(*PanelState)
	
	if cloned.ID != original.ID {
		t.Error("Cloned ID should match original")
	}
	
	if cloned.Ready != original.Ready {
		t.Error("Cloned Ready should match original")
	}
	
	// Modify clone to ensure independence
	cloned.Width = 200
	
	if original.Width == 200 {
		t.Error("Original should not be affected by clone modification")
	}
}

func TestComponentState_Merge(t *testing.T) {
	original := &SidebarState{
		ID:      "original",
		Visible: true,
		Width:   30,
		Items:   []SidebarItem{{Name: "Original", Type: "file"}},
	}
	
	update := &SidebarState{
		ID:      "update",
		Visible: false,
		Width:   50,
		Items:   []SidebarItem{{Name: "Updated", Type: "tool"}},
	}
	
	err := original.Merge(update)
	if err != nil {
		t.Fatalf("Merge should succeed: %v", err)
	}
	
	if original.Visible {
		t.Error("Original visibility should be updated")
	}
	
	if original.Width != 50 {
		t.Errorf("Expected width 50, got %d", original.Width)
	}
	
	if len(original.Items) != 1 {
		t.Errorf("Expected 1 item, got %d", len(original.Items))
	}
	
	if original.Items[0].Name != "Updated" {
		t.Errorf("Expected item name 'Updated', got '%s'", original.Items[0].Name)
	}
}